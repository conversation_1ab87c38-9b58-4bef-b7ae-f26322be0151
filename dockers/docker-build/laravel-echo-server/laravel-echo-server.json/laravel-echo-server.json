{"apiOriginAllow": {"allowCors": true, "allowOrigin": "LARAVEL_ECHO_ALLOW_ORIGIN", "allowMethods": "GET, POST", "allowHeaders": "Origin, Content-Type, X-Auth-Token, X-Requested-With, Accept, Authorization, X-CSRF-TOKEN, X-Socket-Id"}, "authEndpoint": "/broadcasting/auth", "authHost": "LARAVEL_ECHO_AUTH_HOST", "clients": "LARAVEL_ECHO_CLIENTS", "database": "LARAVEL_ECHO_SERVER_DB", "databaseConfig": {"redis": {"host": "REDIS_HOST", "port": "REDIS_PORT", "password": "REDIS_PASSWORD", "keyPrefix": "REDIS_PREFIX", "options": {"db": "REDIS_DB"}}, "sqlite": {"databasePath": "/database/laravel-echo-server.sqlite"}, "publishPresence": true}, "devMode": false, "host": null, "port": 6001, "protocol": "http", "sslCertPath": "", "sslKeyPath": "", "sslCertChainPath": "", "sslPassphrase": "", "socketio": {}, "subscribers": {"http": true, "redis": true}}