#FROM ingress.osvlabs.com:32116/infrastructures/base-images/base-images-php8.2.13-fpm-supervisord
FROM harbor.ks.osvlabs.com:31310/infrastructures/base-images/base-images-php:php-8-2-13-fpm-8107

ENV TZ=Asia/Shanghai

RUN apt-get update && apt-get install -y --no-install-recommends \
  nginx && rm -rf /var/lib/apt/lists/*

ARG DOCKER_UID=1000
ENV DOCKER_UID ${DOCKER_UID}
ARG DOCKER_GID=1000
ENV DOCKER_GID ${DOCKER_GID}

# user and group and permissions
RUN groupadd -g ${DOCKER_GID} docker && \
  useradd -u ${DOCKER_UID} -g docker docker && \
  usermod -d /home/<USER>
RUN mkdir /home/<USER>
  chown docker:docker /home/<USER>

RUN chown docker:docker ./ -R

# supervisord
RUN mkdir -p /var/log/supervisor && \
  chown docker:docker /var/log/supervisor

# Development php.ini setting
ARG PHAR_READONLY=On
RUN cp /usr/local/etc/php/php.ini-development /usr/local/etc/php/php.ini
RUN sed -i "s/upload_max_filesize = 2M/upload_max_filesize = 20M/" /usr/local/etc/php/php.ini && \
  sed -i "s/post_max_size = 8M/post_max_size = 32M/" /usr/local/etc/php/php.ini && \
  sed -i "s/post_max_size = 8M/post_max_size = 32M/" /usr/local/etc/php/php.ini && \
  sed -i "s/;opcache.enable=1/opcache.enable=1/" /usr/local/etc/php/php.ini && \
  sed -i "s/;opcache.enable_cli=0/opcache.enable_cli=1/" /usr/local/etc/php/php.ini && \
  sed -i "s/;phar.readonly = On/phar.readonly = ${PHAR_READONLY}/" /usr/local/etc/php/php.ini && \
  sed -i "s/;error_log = php_errors.log/error_log = \/var\/log\/php\/php_errors.log/" /usr/local/etc/php/php.ini

# install composer
COPY --from=composer:2.0 /usr/bin/composer /usr/local/bin/composer
RUN composer config -g repo.packagist composer https://mirrors.cloud.tencent.com/composer/
RUN composer self-update --2

COPY ./development/nginx/default.conf /etc/nginx/sites-enabled/default
RUN sed -i "s]php:]localhost:]g" /etc/nginx/sites-enabled/default
RUN sed -i "s]www-data]docker]g" /etc/nginx/nginx.conf

COPY --chown=docker:docker ./development/backend/supervisord.conf /etc/supervisor/conf.d/default.conf
COPY --chown=docker:docker ./development/backend/crontab /etc/supercronictab
COPY --chown=docker:docker ./packages/backend /etc/dist/backend
COPY --chown=docker:docker ./packages/admin/dist /etc/dist/admin

RUN mkdir -p /etc/dist/backend/storage/logs
RUN chmod 777 /run
RUN chown -R docker:docker /var/lib/nginx /var/log/nginx 

COPY ./deployment/dockers/entrypoint.sh /etc/entrypoint/entrypoint.sh
RUN chmod 777 /etc/entrypoint -R
ENTRYPOINT [ "/etc/entrypoint/entrypoint.sh" ]

USER docker

EXPOSE 80
WORKDIR /etc/dist
