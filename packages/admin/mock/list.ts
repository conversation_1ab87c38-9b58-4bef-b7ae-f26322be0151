// 模拟后端动态生成路由
import { <PERSON>ckMethod } from "vite-plugin-mock";

const draftleadsData = [
  {
    id: 5,
    client: {
      id: 1,
      name: "Draft Leads 1",
      phone: "15037373633",
      email: "<EMAIL>",
      address: null
    },
    category: { id: 1, name: "类别1" },
    source: { id: 1, name: "来源1" },
    enquiryDate: "Feb 11, 2024",
    createBy: { id: 1, name: "张三" },
    salesManager: { id: 1, name: "销售经理J" },
    result: { id: 1, name: "draft" },
    spokenTo: "MartinBaptista"
  },
  {
    id: "2",
    client: {
      id: 2,
      name: "",
      phone: "15437373633",
      email: "<EMAIL>",
      address: "上海市浦东新区世纪大道"
    },
    category: { id: 2, name: "类别2" },
    source: { id: 2, name: "Google Ads" },
    enquiryDate: "Feb 12, 2024",
    createBy: { id: 1, name: "张三" },
    salesManager: { id: 1, name: "<PERSON>" },
    result: { id: 1, name: "draft" },
    spokenTo: ""
  }
];

const leadsData = [
  {
    id: 3,
    client: {
      id: 3,
      name: "Draft Leads 3",
      phone: "15037373633",
      email: "<EMAIL>",
      address: null
    },
    category: { id: 1, name: "类别1" },
    source: { id: 1, name: "来源1" },
    enquiryDate: "Feb 11, 2024",
    createBy: { id: 1, name: "张三" },
    salesManager: { id: 1, name: "销售经理J" },
    apptSetter: { id: 2, name: "tomJ" },
    result: { id: 1, name: "new" },
    spokenTo: "MartinBaptista",
    appointmentDate: "Feb 11, 2024",
    retail: null,
    subResult: { id: 3, name: "subResult3" },
    quotedAmount: 1000,
    soldAmount: 800,
    followupTime: "Feb 13, 2024",
    apptTime: "Feb 13, 2024"
  }
];

export default [
  {
    url: "/draftleads/list",
    method: "get",
    response: () => {
      return {
        success: true,
        data: draftleadsData,
        links: {
          first: "http://localhost/api/draftleads/list?sort=id&size=20&page=1",
          last: "http://localhost/api/draftleads/list?sort=id&size=20&page=1",
          prev: null,
          next: null
        },
        meta: {
          current_page: 1,
          from: 1,
          last_page: 1,
          links: [
            {
              url: null,
              label: "&laquo; Previous",
              active: false
            },
            {
              url: "http://localhost/api/draftleads/list?sort=id&size=20&page=1",
              label: "1",
              active: true
            },
            {
              url: null,
              label: "Next &raquo;",
              active: false
            }
          ],
          path: "http://localhost/api/draftleads/list",
          per_page: 20,
          to: 2,
          total: 35
        }
      };
    }
  },
  {
    url: "/leads/list",
    method: "get",
    response: () => {
      return {
        success: true,
        data: leadsData,
        links: {
          first: "http://localhost/api/leads/list?sort=id&size=20&page=1",
          last: "http://localhost/api/leads/list?sort=id&size=20&page=1",
          prev: null,
          next: null
        },
        meta: {
          current_page: 1,
          from: 1,
          last_page: 1,
          links: [
            {
              url: null,
              label: "&laquo; Previous",
              active: false
            },
            {
              url: "http://localhost/api/leads/list?sort=id&size=20&page=1",
              label: "1",
              active: true
            },
            {
              url: null,
              label: "Next &raquo;",
              active: false
            }
          ],
          path: "http://localhost/api/leads/list",
          per_page: 20,
          to: 2,
          total: leadsData.length
        }
      };
    }
  }
] as MockMethod[];
