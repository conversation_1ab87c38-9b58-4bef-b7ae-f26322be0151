<script setup lang="ts">
import SuccessIcon from "@/assets/svg/success.svg?component";
import { WarningFilled } from "@element-plus/icons-vue";
import { ref, onMounted } from "vue";

const dialogVisible = ref(false);
const promise: any = {};
const title = ref("Success");
const content = ref("content...");
const type = ref("success"); // success, warning, error,custom
const buttons = ref([{ text: "Create Another New", onClick: () => {} }]);
const icon = ref(null);
const width = ref("300px");
const btnLayout = ref("column"); //row column

function show(data) {
  title.value = data.title;
  content.value = data.content;
  type.value = data.type;
  buttons.value = data.buttons;
  if (data.width) {
    width.value = data.width;
  }
  if (data.icon) {
    icon.value = data.icon;
  }
  if (data.btnLayout) {
    btnLayout.value = data.btnLayout;
  }

  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  if (actionOver) {
    promise.resolve();
  }
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="''"
    :width="width"
    align-center
    class="result-dialog"
  >
    <div class="center-box">
      <SuccessIcon class="result-icon" v-if="type === 'success'" />
      <span
        v-else-if="icon"
        :class="`mr-1 primary-color iconfont just-icon-${icon}`"
      />
      <WarningFilled class="result-icon warning" v-else />

      <div class="title level-1">{{ title }}</div>
      <div class="content">
        {{ content }}
      </div>
      <div :class="btnLayout == 'row' ? 'btnRow' : 'btnColumn'">
        <el-button
          type="info"
          class="mt-[20px] w-[100%] !mx-[0px]"
          v-for="(btn, index) in buttons"
          :key="index"
          @click="btn.onClick"
          >{{ btn.text }}</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.el-button--info.el-button:focus,
.el-button--info.el-button:hover {
  color: var(--el-button-hover-text-color);
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  outline: 0;

  .iconfont {
    color: #fff;
  }
}

.content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.result-icon {
  width: 50px;
  height: 50px;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.el-button {
  font-size: 14px;
  font-weight: 500;
  color: #2a2e34;
  background: #f7f8f9;
  border: 1px soild #f7f8f9;
}

.warning {
  color: #ff9000;
}

.btnRow {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-betweens;
}

.btnColumn {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.iconfont {
  font-size: 24px;
}
</style>
