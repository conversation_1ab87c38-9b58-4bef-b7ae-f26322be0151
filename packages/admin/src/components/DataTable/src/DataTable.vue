<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import type { PaginationProps } from "@pureadmin/table";
import { PureTableBar } from "@/components/RePureTableBar";
import { usePublicHooks } from "@/utils/hooks";
import { transformI18n } from "@/plugins/i18n";

const tableData = ref([]);
const loading = ref(false);
const showTable = ref(true);
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  pageSizes: [20, 30, 50, 100],
  total: 0,
  align: "left",
  background: true,
  layout: "prev, pager, next"
});
const summaryData = reactive({
  totalCount: 0,
  totalUnit: 0,
  totalSold: 0,
  totalCost: 0
});
const { tableStyle } = usePublicHooks();
const props = defineProps({
  title: {
    type: String,
    default: ""
  },
  columns: {
    type: Array,
    default: () => []
  },
  source: {
    type: Function,
    required: true
  },
  slotNames: {
    type: Array,
    default: () => []
  },
  subPagination: {
    type: Object,
    default: () => ({})
  },
  form: {
    type: Object,
    default: () => ({})
  },
  queryParams: {
    type: [Function, Object],
    default: null
  },
  ifShowSearch: {
    type: Boolean,
    default: false
  },
  keywordPlaceHolder: {
    type: String,
    default: ""
  },
  searchKeyword: {
    type: String,
    default: ""
  },
  spanMethod: {
    type: Function,
    required: false
  },
  showSummary: {
    type: Boolean,
    default: false
  },
  getSummariesCustom: {
    type: Function,
    default: null
  }
});

watch(
  () => props.columns,
  () => refreshTable()
);

function refreshTable() {
  showTable.value = false;
  setTimeout(() => {
    showTable.value = true;
  }, 50);
}

const multipleSelection = ref([]);

const emit = defineEmits<{
  (e: "pageSizeChange", val: number): void;
  (e: "pageCurrentChange", val: number): void;
  (e: "handleSelectionChange", val: Array<Object>): void;
  (e: "handleRowClick", val: Array<Object>): void;
  (e: "toPassNewInfo", val: Object): void;
  (e: "dynamicColumnsChange", val: Object): void;
  (e: "handleSearch", val: String): void;
  (e: "sortChange", val: Object): void;
}>();

onMounted(() => {
  loadData();
  Object.assign(pagination, props.subPagination);
});

function loadData() {
  loading.value = true;
  request()
    .then(res => {
      loading.value = false;
      tableData.value = res.data;
      pagination.total = res.meta.total;
      pagination.pageSize = res.meta.per_page;
      pagination.currentPage = res.meta.current_page;
      if (res.summaryData) {
        summaryData.totalCount = res.summaryData.totalCount;
        summaryData.totalUnit = res.summaryData.totalUnit;
        summaryData.totalSold = res.summaryData.totalSold;
        summaryData.totalCost = res.summaryData.totalCost;
      }
      emit("toPassNewInfo", {
        total: pagination.total,
        pageSize: pagination.pageSize
      });
    })
    .catch(() => {
      loading.value = false;
    });
}

function request() {
  const params = {
    search: "",
    filter: "",
    sort: "-id",
    _with: "",
    withCount: "",
    page: pagination.currentPage,
    size: pagination.pageSize,
    otherParam: null,
    isList: null,
    companyRegion: null
  };
  if (props.queryParams) {
    if (typeof props.queryParams === "function") {
      Object.assign(params, props.queryParams(props.form));
    } else {
      Object.assign(params, props.queryParams);
    }
  }
  const {
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    otherParam,
    isList,
    companyRegion
  } = params;
  let searchStr = "";
  if (search) {
    searchStr = `%${search}%`;
  }
  if (otherParam) {
    return props.source(
      searchStr,
      filter,
      sort,
      _with,
      withCount,
      page,
      size,
      otherParam
    );
  } else if (isList) {
    return props.source(
      searchStr,
      filter,
      sort,
      _with,
      withCount,
      page,
      size,
      isList
    );
  } else if (companyRegion) {
    return props.source(
      searchStr,
      filter,
      sort,
      _with,
      withCount,
      page,
      size,
      companyRegion
    );
  } else {
    return props.source(searchStr, filter, sort, _with, withCount, page, size);
  }
}

function handlePageSizeChange(val) {
  pagination.pageSize = val;
  loadData();
  emit("pageSizeChange", val);
}

function handlePageCurrentChange(val) {
  pagination.currentPage = val;
  loadData();
  emit("pageCurrentChange", val);
}

const handleSelectionChange = val => {
  multipleSelection.value = val;
  emit("handleSelectionChange", val);
};

const handleRowClick = row => {
  emit("handleRowClick", row);
};

const handleSearch = val => {
  emit("handleSearch", val);
};

const dynamicColumnsChange = val => {
  emit("dynamicColumnsChange", val);
};

const sortChange = column => {
  emit("sortChange", column);
};

const getSummaries = (param: any) => {
  return props.getSummariesCustom(param, summaryData);
};

defineExpose({ loadData });
</script>

<template>
  <div class="data-table-content">
    <PureTableBar
      :title="props.title"
      :columns="props.columns"
      :ifShowSearch="props.ifShowSearch"
      :keywordPlaceHolder="props.keywordPlaceHolder"
      :searchKeyword="props.searchKeyword"
      @search="handleSearch"
      @refresh="loadData"
      @dynamicColumnsChange="dynamicColumnsChange"
      v-if="showTable"
    >
      <template #buttons>
        <slot name="buttons" />
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <pure-table
          adaptive
          :border="true"
          align-whole="left"
          showOverflowTooltip
          table-layout="auto"
          :loading="loading"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :span-method="spanMethod"
          :size="size"
          :style="tableStyle"
          :headerRowClassName="'header-row'"
          :headerCellClassName="'header-cell'"
          :rowClassName="'tab-row'"
          :cellClassName="'tab-cell'"
          @page-size-change="handlePageSizeChange"
          @page-current-change="handlePageCurrentChange"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          :emptyText="transformI18n('common.noData')"
          @sort-change="sortChange"
          :show-summary="props.showSummary"
          :summary-method="getSummaries"
        >
          <template v-for="slotName in slotNames" v-slot:[slotName]="scope">
            <slot :name="slotName" v-bind="scope" />
          </template>
        </pure-table>
      </template>
    </PureTableBar>
  </div>
</template>
