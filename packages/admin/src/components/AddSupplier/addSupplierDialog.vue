<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { supplierControllerIndex } from "@/api/admin/basic-data";
import { orderControllerAddSupplier } from "@/api/admin/order";
import { transformI18n } from "@/plugins/i18n";
import { sendFormFilter } from "@/utils/form";
import { ElMessage } from "element-plus";

const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const btnLoading = ref(false);
const form = reactive({
  order_id: 0,
  supplier_id: 0,
  order_date: "",
  type: null
});
const formRef = ref(null);
const rules = {
  supplier_id: [
    { required: true, message: "Please select a supplier", trigger: "change" }
  ],
  order_date: [
    {
      required: true,
      message: "Please enter the order date",
      trigger: "change"
    }
  ]
};

const options = ref([]);
function show(data, type = "order") {
  if (!data.id) return;
  initFormData();
  form.order_id = data.id;
  form.type = type;
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function initFormData() {
  Object.keys(form).forEach(key => {
    form[key] = null;
  });
  loadOptions();
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  promise.resolve(actionOver);
}
function loadOptions() {
  loading.value = true;
  supplierControllerIndex("", "")
    .then(res => {
      if (Array.isArray(res.data)) {
        options.value = res.data.map(item => ({
          value: item.id,
          label: item.name
        }));
      }
      loading.value = false;
    })
    .catch(_err => {
      loading.value = false;
    });
}

function addSupplier() {
  formRef.value.validate(valid => {
    if (valid) {
      btnLoading.value = true;
      const sendForm = sendFormFilter(form);
      orderControllerAddSupplier(form.order_id, sendForm)
        .then(res => {
          btnLoading.value = false;
          if (res.success) {
            ElMessage.success(res.message);
            hide(true);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_err => {
          btnLoading.value = false;
        });
    }
  });
  return;
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'Add Supply'"
    align-center
    :width="'550px'"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top border-form"
    >
      <div class="center-box">
        <el-row class="mt-[20px]">
          <el-col :span="24">
            <el-form-item prop="supplier_id" label="Supplier">
              <el-select
                :loading="loading"
                v-model="form.supplier_id"
                filterable
                clearable
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="order_date" label="Order Date">
              <el-date-picker
                v-model="form.order_date"
                type="date"
                class="grow"
                :placeholder="transformI18n('common.select')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          class="w-52 !text-sm h-10 !bg-[#E9EBF0]"
          @click="hide(false)"
          >{{ transformI18n("buttons.hsCancel") }}</el-button
        >
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :disabled="btnLoading"
          :loading="btnLoading"
          @click="addSupplier"
          >{{ transformI18n("buttons.hssave") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.result-icon {
  width: 50px;
  height: 50px;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
}

.warning {
  color: #ff9000;
}

.iconfont {
  font-size: 24px;
}
</style>
