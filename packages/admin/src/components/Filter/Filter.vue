<script setup lang="ts">
import { ref } from "vue";

import { transformI18n } from "@/plugins/i18n";

const dialogVisible = ref(false);
const promise: any = {};

const form = ref({
  enquiryDateStart: null,
  enquiryDateEnd: null,
  apptDateStart: null,
  apptDateEnd: null
});
function show() {
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}
function hide() {
  dialogVisible.value = false;
  promise.resolve(form);
}

function reset() {
  form.value = {
    enquiryDateStart: null,
    enquiryDateEnd: null,
    apptDateStart: null,
    apptDateEnd: null
  };
  dialogVisible.value = false;
  promise.resolve(form);
}

defineExpose({ show });
</script>

<template>
  <div>
    <el-dialog v-model="dialogVisible" title="Filter" width="38.75rem">
      <div class="p-2">
        <p class="mt-2 mb-2">Enquiry Date</p>
        <el-row>
          <el-col :span="12" :xs="24" class="date-item">
            <div class="pl-2 mr-2">
              <el-date-picker
                v-model="form.enquiryDateStart"
                type="date"
                placeholder="Start"
                format="YYYY/MM/DD"
                :clearable="false"
                value-format="YYYY/MM/DD"
              />
            </div>
          </el-col>
          <el-col :span="12" :xs="24" class="date-item">
            <div class="pl-2 mr-2">
              <el-date-picker
                v-model="form.enquiryDateEnd"
                type="date"
                placeholder="End"
                format="YYYY/MM/DD"
                :clearable="false"
                value-format="YYYY/MM/DD"
              />
            </div>
          </el-col>
        </el-row>
        <p class="mt-2 mb-2">Appt Date</p>
        <el-row>
          <el-col :span="12" :xs="24" class="date-item">
            <div class="pl-2 mr-2">
              <el-date-picker
                v-model="form.apptDateStart"
                type="date"
                placeholder="Start"
                format="YYYY/MM/DD"
                :clearable="false"
                value-format="YYYY/MM/DD"
              />
            </div>
          </el-col>
          <el-col :span="12" :xs="24" class="date-item">
            <div class="pl-2 mr-2">
              <el-date-picker
                v-model="form.apptDateEnd"
                type="date"
                placeholder="End"
                format="YYYY/MM/DD"
                :clearable="false"
                value-format="YYYY/MM/DD"
              />
            </div>
          </el-col>
        </el-row>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button class="!text-sm h-10" @click="reset" type="info"
            >Reset</el-button
          >
          <el-button class="!text-sm h-10" @click="hide" type="primary">{{
            transformI18n("leads.confirm")
          }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.title {
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  color: var(--el-color-primary);
}

.date-item {
  margin-bottom: 5px;
}

.date-item div {
  border: 1px solid #e9ebf0;
  border-radius: 5px;
}

@media screen and (width <= 520px) {
  ::v-deep(.el-dialog) {
    width: 80% !important;
  }
}
</style>
