<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { Loader } from "@googlemaps/js-api-loader";
import { googleMapConfig } from "@/config";
import { Search } from "@element-plus/icons-vue";
import LocationIcon from "@/assets/svg/location.svg?component";
import { transformI18n } from "@/plugins/i18n";

const props = defineProps({
  mapID: {
    type: String,
    default: "googleMap"
  },
  googleMapStyle: {
    type: Object,
    default: () => {
      return {
        wdith: "100%",
        height: "100vh"
      };
    }
  },
  mapOptions: {
    type: Object,
    default: () => {
      return {
        disableDefaultUI: false,
        gestureHandling: "greedy",
        panControl: true,
        zoomControl: true,
        scaleControl: true,
        streetViewControl: false
      };
    }
  },
  zoom: {
    type: Number,
    default() {
      return 15;
    }
  }
});
const loading = ref(false);
const currentPlaceInfo = ref(null); // current currentPlaceInfo
const googleApi = ref(null); // map object
const googleMapCenter = ref({
  lng: 0,
  lat: 0
});
let marker,
  service,
  geocoder,
  googleMap,
  searchBox,
  infowindow,
  infowindowContent;
const searchInput = ref(null);
const apiKey = googleMapConfig.apiKey;
const defaultMapCenter = googleMapConfig.defaultMapCenter;
const formData = ref(null);
const emit = defineEmits<{
  (e: "toUpdateAddress", val: Object): void;
}>();

const autoAddressList = ref([]);
function init(data = null) {
  formData.value = data;
  googleMapCenter.value = null;
  searchInput.value = "";
  if (formData.value && formData.value.longitude && formData.value.latitude) {
    googleMapCenter.value = {
      lat: formData.value.latitude,
      lng: formData.value.longitude
    };
    searchInput.value = formData.value.address;
  }
  nextTick(() => {
    initMap();
  });
}

async function initMap() {
  loading.value = true;
  //1.load js
  const loader = new Loader({
    apiKey: apiKey,
    version: "weekly",
    libraries: ["places", "maps", "marker"],
    language: googleMapConfig.language
    // region: "Canana"
  });
  const google = await loader.load();
  googleApi.value = google;

  //2.init map
  const option = {
    zoom: props.zoom,
    mapTypeControl: false
  };
  if (
    googleMapCenter.value &&
    googleMapCenter.value.lng &&
    googleMapCenter.value.lat
  ) {
    option["center"] = googleMapCenter.value;
  } else {
    option["center"] = defaultMapCenter;
  }
  const map = new google.maps.Map(document.getElementById(props.mapID), option);
  googleMap = map;

  // reference documents：https://developers.google.com/maps/documentation/javascript/reference/places-autocomplete-service?hl=en
  searchBox = new google.maps.places.AutocompleteService();
  // reference documents：https://developers.google.com/maps/documentation/javascript/reference/places-service?hl=en
  service = new google.maps.places.PlacesService(map);
  //：https://developers.google.com/maps/documentation/javascript/reference/geocoder?hl=en
  geocoder = new google.maps.Geocoder();
  marker = new google.maps.Marker({
    map: map,
    position: option["center"],
    draggable: true
  });
  //4.add listener
  google.maps.event.addListener(marker, "mouseup", function (e) {
    const newPosition = {
      lng: e.latLng.lng(),
      lat: e.latLng.lat()
    };
    geocoder.geocode(
      {
        location: newPosition
      },
      (results, status) => {
        if (status == "OK") {
          if (results[0].place_id) {
            const request = {
              placeId: results[0].place_id,
              fields: ["name", "formatted_address", "place_id", "geometry"]
            };
            service.getDetails(request, (detailInfo, status) => {
              if (status === "OK") {
                searchInput.value = detailInfo.name;
                geocoderHandle(detailInfo, false, newPosition);
              }
            });
          } else {
            geocoderHandle(results[0], false, newPosition);
          }
        } else {
          alert("get map detail error " + status);
        }
      }
    );
  });

  //3.init infowindow
  infowindow = new google.maps.InfoWindow();
  if (!infowindowContent) {
    infowindowContent = document.getElementById(
      "infowindow-content"
    ) as HTMLElement;
  }
  infowindow.setContent(infowindowContent);
  nextTick(() => {
    if (infowindowContent && searchInput.value) {
      infowindowContent.children["place-name"].textContent = "";
      infowindowContent.children["place-address"].textContent =
        searchInput.value;
      infowindow.open(googleMap, marker);
    }
  });
  loading.value = false;
}

// Click one line address
function confirm(e) {
  // 搜索地点和检索地点详细信息
  const request = {
    placeId: e.place_id,
    fields: ["name", "formatted_address", "place_id", "geometry"]
  };
  service.getDetails(request, (event, status) => {
    if (status === "OK") {
      searchInput.value = event.name;
      geocoderHandle(event, true);
      autoAddressList.value = [];
    }
  });
}

function geocoderHandle(addressDetail, resetMaker = false, newPosition = null) {
  currentPlaceInfo.value = null;
  if (resetMaker) {
    infowindow.close();
    marker.setVisible(false);
    googleMap.setCenter(addressDetail.geometry.location);
    googleMap.setZoom(props.zoom);
    marker.setPosition(addressDetail.geometry.location);
    marker.setVisible(true);
  }
  if (newPosition) {
    currentPlaceInfo.value = {
      latitude: newPosition.lat,
      longitude: newPosition.lng,
      address: addressDetail.formatted_address
      // name: addressDetail.name
    };
  }
  currentPlaceInfo.value = {
    latitude: addressDetail.geometry.location.lat(),
    longitude: addressDetail.geometry.location.lng(),
    address: addressDetail.formatted_address
    // name: addressDetail.name
  };
  marker.setVisible(true);
  if (infowindowContent && searchInput.value) {
    infowindowContent.children["place-name"].textContent = searchInput.value;
    infowindowContent.children["place-address"].textContent =
      addressDetail.formatted_address;
    infowindow.open(googleMap, marker);
  }
}

function onSave() {
  emit("toUpdateAddress", currentPlaceInfo.value);
}

function chnageinput(e) {
  searchBox.getPlacePredictions({ input: e }, (event, status) => {
    if (status === "OK") {
      autoAddressList.value = event || [];
      // place_id 后面有用，所以只保留存在place_id的数据
      autoAddressList.value = autoAddressList.value.filter(
        x => x && x.place_id
      );
    } else {
      autoAddressList.value = [];
    }
  });
}

onMounted(() => {});
defineExpose({ init });
</script>
<template>
  <div
    class="just-map-container"
    v-loading="loading"
    element-loading-text="loading..."
  >
    <div class="pac-card" id="pac-card">
      <div id="pac-container">
        <el-input
          v-model="searchInput"
          placeholder="Search for a place or address"
          id="pac-input"
          @input="chnageinput"
        >
          <template #append>
            <Search class="mr-1" style="border: 1px solid red" />
          </template>
        </el-input>
      </div>

      <div class="address-list-box">
        <div
          class="list-item"
          @click="confirm(item)"
          v-for="(item, index) in autoAddressList"
          :key="index"
        >
          <LocationIcon class="locationIcon" />
          <div class="address">
            <span class="title">{{
              item.structured_formatting.main_text
            }}</span>
            <span>{{ item.structured_formatting.secondary_text }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="mapStyle">
      <div class="mapRightStyle">
        <div :style="googleMapStyle" class="googleMap" :id="props.mapID" />
      </div>
    </div>
    <div id="infowindow-content">
      <span id="place-name" class="title" />
      <span id="place-address" />
      <el-button type="primary" @click="onSave">{{
        transformI18n("buttons.hssave")
      }}</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
#description {
  font-size: 15px;
  font-weight: 300;
}

#infowindow-content {
  display: flex;
  flex-direction: column;
  font-weight: bold;
  line-height: 24px;
}

.pac-card {
  position: absolute;
  top: 65px;
  left: 20px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 600px;
  max-height: 500px;
  padding: 0;
  overflow: hidden;
  font-size: 18px;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 1px 4px -1px rgb(0 0 0 / 30%);
}

#pac-container {
  width: 100%;
}

.address-list-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  background: #fff;
  border-radius: 0 0 20px 20px;

  .list-item {
    display: flex;
    align-items: center;
    min-height: 24px;
    padding-top: 6px;
    padding-bottom: 7px;
    line-height: 32px;
    color: #70757a;
  }

  .address {
    width: 540px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .title {
      font-weight: 500;
      color: #202124;
    }
  }
}

.pac-controls {
  display: inline-block;
  padding: 0;
}

.pac-controls label {
  /* font-family: Roboto; */
  font-size: 13px;
  font-weight: 300;
}

#pac-input {
  z-index: 3;
  width: 450px;
  height: 40px;
  padding: 0 11px 0 13px;
  padding-left: 38px;
  margin: 0;
  margin-left: 12px;
  font-size: 15px;
  font-weight: 300;
  text-overflow: ellipsis;
  background-color: #fff;
  border: 0;
  border-radius: 32px;
  box-shadow: 0 1px 2px rgb(60 64 67 / 30%), 0 1px 3px 1px rgb(60 64 67 / 15%);
}

#pac-input:focus {
  border-color: #4d90fe;
}

.searchIcon {
  position: absolute;
  top: 11px;
  left: 26px;
  width: 20px;
  font-size: 20px;
}
</style>
