<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch } from "vue";
import { Loader } from "@googlemaps/js-api-loader";
import { googleMapConfig } from "@/config";
import { Search } from "@element-plus/icons-vue";
import LocationIcon from "@/assets/svg/location.svg?component";
import { transformI18n } from "@/plugins/i18n";
import { appointmentTypeText } from "@/views/appointment/data";
import { MarkerClusterer } from "@googlemaps/markerclusterer";
import { useNav } from "@/layout/hooks/useNav";

const { isAppointmentUser } = useNav();

const props = defineProps({
  mapID: {
    type: String,
    default: "googleMapID"
  },
  googleMapStyle: {
    type: Object,
    default: () => {
      return {
        wdith: "100%",
        height: "100vh"
      };
    }
  },
  mapOptions: {
    type: Object,
    default: () => {
      return {
        disableDefaultUI: false,
        gestureHandling: "greedy",
        panControl: true,
        zoomControl: true,
        scaleControl: true,
        streetViewControl: false
      };
    }
  },
  zoom: {
    type: Number,
    default() {
      return 15;
    }
  },
  appointmentsList: {
    type: Array,
    default: () => []
  },
  pageType: {
    type: String,
    default: "default" // default (default map use for search) , appointmentListPage (appointmentListPage map use for appointment)
  }
});
const loading = ref(false);
const currentPlaceInfo = ref(null); // current currentPlaceInfo
const googleApi = ref(null); // map object
const googleMapCenter = ref({
  lng: 0,
  lat: 0
});
let marker,
  service,
  geocoder,
  googleMap,
  searchBox,
  infowindow,
  collisionBehavior,
  markerClusterer,
  infowindowContent;
const searchInput = ref(null);
const apiKey = googleMapConfig.apiKey;
const defaultMapCenter = googleMapConfig.defaultMapCenter;
const formData = ref(null); // for default map
const properties = ref([]);
const autoAddressList = ref([]);

const appointmentTypeOption = ref([]);
const appointmentForm = reactive({
  type: [], // cm install sale
  unsigned: false
});
const appointmentSearch = ref("");
const emit = defineEmits<{
  (e: "toUpdateAddress", val: Object): void;
  (e: "toAppointmentDetail", val: Object): void;
  (e: "appointmentSearchChange", val: Object): void;
}>();
watch(
  () => [props.appointmentsList, googleApi.value],
  _val => {
    handleAppointmentsList();
  },
  {
    immediate: true,
    deep: true
  }
);

watch(
  () => appointmentForm,
  _value => {
    appointmentSearchChange();
  },
  {
    immediate: false,
    deep: true
  }
);

// Map component search criteria change processing
function appointmentSearchChange() {
  if (props.pageType !== "appointmentListPage") {
    return;
  }
  emit("appointmentSearchChange", {
    ...appointmentForm,
    search: appointmentSearch
  });
}

function handleAppointmentsList() {
  if (props.pageType !== "appointmentListPage") {
    return;
  }
  if (!googleApi.value) {
    init();
    return;
  }
  const list = [];
  if (props.appointmentsList.length > 0) {
    props.appointmentsList.map((item: any) => {
      if (typeof item === "object" && item !== null) {
        list.push({
          ...item,
          position: {
            lat: parseFloat(item.latitude),
            lng: parseFloat(item.longitude)
          }
        });
      }
    });
  }
  properties.value = list;
  setAppointmentMaker();
}

function init(data = null) {
  formData.value = data;
  googleMapCenter.value = null;
  searchInput.value = "";
  if (formData.value && formData.value.longitude && formData.value.latitude) {
    googleMapCenter.value = {
      lat: parseFloat(formData.value.latitude),
      lng: parseFloat(formData.value.longitude)
    };
    searchInput.value = formData.value.address;
  }
  nextTick(() => {
    if (!googleApi.value) {
      initMap();
    }
  });
}

async function initMap() {
  loading.value = true;
  //1.load js
  const loader = new Loader({
    apiKey: apiKey,
    version: "weekly",
    libraries: ["places", "maps", "marker"],
    language: googleMapConfig.language
    // region: "Canana"
  });
  const google = await loader.load();
  googleApi.value = google;
  //2.init map
  const option = {
    zoom: props.zoom,
    mapTypeControl: false,
    zoomControl: true,
    mapId: props.mapID,
    zoomControlOptions: {
      position: google.maps.ControlPosition.INLINE_END_BLOCK_START
    }
  };
  if (
    googleMapCenter.value &&
    googleMapCenter.value.lng &&
    googleMapCenter.value.lat
  ) {
    option["center"] = googleMapCenter.value;
  } else {
    option["center"] = defaultMapCenter;
  }
  const map = new google.maps.Map(document.getElementById(props.mapID), option);
  googleMap = map;
  if (props.pageType === "default") {
    defaultInitmarker(google, option);
  } else {
    // for appointment map
    collisionBehavior = google.maps.CollisionBehavior.REQUIRED;
    markerClusterer = new MarkerClusterer({ map: googleMap });
  }
  loading.value = false;
}

// Add some markers to the map.
function setAppointmentMaker() {
  if (!googleApi.value.maps.marker || !markerClusterer) {
    return;
  }
  markerClusterer.clearMarkers();
  const markers = properties.value.map(property => {
    const AdvancedMarkerElement =
      new googleApi.value.maps.marker.AdvancedMarkerElement({
        map: googleMap,
        content: buildContent(property),
        position: property.position,
        title: property.description,
        collisionBehavior: collisionBehavior
      });

    AdvancedMarkerElement.addListener("click", () => {
      emit("toAppointmentDetail", property);
    });
    return AdvancedMarkerElement;
  });
  // Add a marker clusterer to manage the markers.
  markerClusterer.addMarkers(markers);
}

function buildContent(property) {
  const content = document.createElement("div");
  content.classList.add("property");
  let smallName = "";
  let color = "green";
  if (property.assign_to && property.assign_to.login_name) {
    smallName = sliceText(property.assign_to.login_name);
    color = property.assign_to.color;
  }
  const type = appointmentTypeText[property.type].label;
  content.innerHTML = `
    <div class="appointment-info">
        <div class="circleText mr-1" style="background-color:${color}">${smallName}</div>
        <div class="appointment-title">${type}</div>
    </div>
    `;
  return content;
}

function sliceText(value) {
  if (!value) return "";
  if (/^[\u4E00-\u9FFF]$/.test(value[0])) {
    return value[0];
  } else {
    return value.slice(0, 2);
  }
}

// for default map
function defaultInitmarker(google, option) {
  // reference documents：https://developers.google.com/maps/documentation/javascript/reference/places-autocomplete-service?hl=en
  searchBox = new google.maps.places.AutocompleteService();
  // reference documents：https://developers.google.com/maps/documentation/javascript/reference/places-service?hl=en
  service = new google.maps.places.PlacesService(googleMap);
  //：https://developers.google.com/maps/documentation/javascript/reference/geocoder?hl=en
  geocoder = new google.maps.Geocoder();
  marker = new googleApi.value.maps.Marker({
    map: googleMap,
    position: option["center"],
    draggable: true
  });
  //4.add listener
  googleApi.value.maps.event.addListener(marker, "mouseup", function (e) {
    const newPosition = {
      lng: e.latLng.lng(),
      lat: e.latLng.lat()
    };
    geocoder.geocode(
      {
        location: newPosition
      },
      (results, status) => {
        if (status == "OK") {
          if (results[0].place_id) {
            const request = {
              placeId: results[0].place_id,
              fields: ["name", "formatted_address", "place_id", "geometry"]
            };
            service.getDetails(request, (detailInfo, status) => {
              if (status === "OK") {
                searchInput.value = detailInfo.name;
                geocoderHandle(detailInfo, false, newPosition);
              }
            });
          } else {
            geocoderHandle(results[0], false, newPosition);
          }
        } else {
          alert("get map detail error " + status);
        }
      }
    );
  });

  //3.init infowindow
  infowindow = new googleApi.value.maps.InfoWindow();
  if (!infowindowContent) {
    infowindowContent = document.getElementById(
      "infowindow-content"
    ) as HTMLElement;
  }
  infowindow.setContent(infowindowContent);
  nextTick(() => {
    if (infowindowContent && searchInput.value) {
      infowindowContent.children["place-name"].textContent = "";
      infowindowContent.children["place-address"].textContent =
        searchInput.value;
      infowindow.open(googleMap, marker);
    }
  });
}

// Click one line address
function confirm(e) {
  // 搜索地点和检索地点详细信息
  const request = {
    placeId: e.place_id,
    fields: ["name", "formatted_address", "place_id", "geometry"]
  };
  service.getDetails(request, (event, status) => {
    if (status === "OK") {
      searchInput.value = event.name;
      geocoderHandle(event, true);
      autoAddressList.value = [];
    }
  });
}

function geocoderHandle(addressDetail, resetMaker = false, newPosition = null) {
  currentPlaceInfo.value = null;
  if (resetMaker) {
    infowindow.close();
    marker.setVisible(false);
    googleMap.setCenter(addressDetail.geometry.location);
    googleMap.setZoom(props.zoom);
    marker.setPosition(addressDetail.geometry.location);
    marker.setVisible(true);
  }
  if (newPosition) {
    currentPlaceInfo.value = {
      latitude: newPosition.lat,
      longitude: newPosition.lng,
      address: addressDetail.formatted_address,
      name: addressDetail.name
    };
  }
  currentPlaceInfo.value = {
    latitude: addressDetail.geometry.location.lat(),
    longitude: addressDetail.geometry.location.lng(),
    address: addressDetail.formatted_address,
    name: addressDetail.name
  };
  marker.setVisible(true);
  if (infowindowContent && searchInput.value) {
    infowindowContent.children["place-name"].textContent = searchInput.value;
    infowindowContent.children["place-address"].textContent =
      addressDetail.formatted_address;
    infowindow.open(googleMap, marker);
  }
}

function onSave() {
  emit("toUpdateAddress", currentPlaceInfo.value);
}

function chnageinput(e) {
  searchBox.getPlacePredictions({ input: e }, (event, status) => {
    if (status === "OK") {
      autoAddressList.value = event || [];
      // place_id 后面有用，所以只保留存在place_id的数据
      autoAddressList.value = autoAddressList.value.filter(
        x => x && x.place_id
      );
    } else {
      autoAddressList.value = [];
    }
  });
}

function changeUnsigned() {
  appointmentForm.unsigned = !appointmentForm.unsigned;
}

onMounted(() => {
  delete appointmentTypeText.all;
  appointmentTypeOption.value = Object.values(appointmentTypeText);
});
defineExpose({ init });
</script>
<template>
  <div
    class="just-map-container"
    v-loading="loading"
    element-loading-text="loading..."
  >
    <!-- default page  search  -->
    <div
      class="pac-card"
      id="pac-card"
      v-if="pageType !== 'appointmentListPage'"
    >
      <div id="pac-container">
        <el-input
          v-model="searchInput"
          placeholder="Search for a place or address"
          id="pac-input"
          @input="chnageinput"
          class="shadow"
        >
          <template #append>
            <Search class="mr-1" style="border: 1px solid red" />
          </template>
        </el-input>
      </div>

      <div class="address-list-box">
        <div
          class="list-item"
          @click="confirm(item)"
          v-for="(item, index) in autoAddressList"
          :key="index"
        >
          <LocationIcon class="locationIcon" />
          <div class="address">
            <span class="title">{{
              item.structured_formatting.main_text
            }}</span>
            <span>{{ item.structured_formatting.secondary_text }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- appointmentListPage  -->
    <div class="appointment-header">
      <el-input
        v-model="appointmentSearch"
        placeholder="Search for a  place, staff..."
        @keyup.enter="appointmentSearchChange"
        @clear="appointmentSearchChange"
        clearable
        class="mb-[10px]"
      >
        <template #prefix>
          <Search class="mr-1 w-[16px]" />
        </template>
      </el-input>
      <el-checkbox-group
        v-model="appointmentForm.type"
        class="flex flex-nowrap"
      >
        <el-checkbox-button
          class="mr-2"
          v-for="(item, index) in appointmentTypeOption"
          :key="index"
          :label="item.value"
          >{{ item.detailLabel || item.label }}</el-checkbox-button
        >
      </el-checkbox-group>
      <div
        v-if="!isAppointmentUser"
        :class="'checkUnsigned ' + (appointmentForm.unsigned ? 'active' : '')"
        @click="changeUnsigned"
      >
        Unsigned
      </div>
    </div>
    <div class="mapStyle">
      <div class="mapRightStyle">
        <div :style="googleMapStyle" class="googleMap" :id="props.mapID" />
      </div>
    </div>
    <div id="infowindow-content" v-if="pageType !== 'appointmentListPage'">
      <span id="place-name" class="title" />
      <span id="place-address" />
      <el-button type="primary" @click="onSave">{{
        transformI18n("buttons.hssave")
      }}</el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
#description {
  font-size: 15px;
  font-weight: 300;
}

#infowindow-content {
  display: flex;
  flex-direction: column;
  font-weight: bold;
  line-height: 24px;
}

.pac-card {
  position: absolute;
  top: 65px;
  left: 20px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 600px;
  max-height: 500px;
  padding: 0;
  overflow: hidden;
  font-size: 18px;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 1px 4px -1px rgb(0 0 0 / 30%);
}

#pac-container {
  width: 100%;
}

.address-list-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  background: #fff;
  border-radius: 0 0 20px 20px;

  .list-item {
    display: flex;
    align-items: center;
    min-height: 24px;
    padding-top: 6px;
    padding-bottom: 7px;
    line-height: 32px;
    color: #70757a;
  }

  .address {
    width: 540px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .title {
      font-weight: 500;
      color: #202124;
    }
  }
}

.pac-controls {
  display: inline-block;
  padding: 0;
}

.pac-controls label {
  /* font-family: Roboto; */
  font-size: 13px;
  font-weight: 300;
}

#pac-input {
  z-index: 3;
  width: 450px;
  height: 40px;
  padding: 0 11px 0 13px;
  padding-left: 38px;
  margin: 0;
  margin-left: 12px;
  font-size: 15px;
  font-weight: 300;
  text-overflow: ellipsis;
  background-color: #fff;
  border: 0;
  border-radius: 32px;
  box-shadow: 0 1px 2px rgb(60 64 67 / 30%), 0 1px 3px 1px rgb(60 64 67 / 15%);
}

#pac-input:focus {
  border-color: #4d90fe;
}

.searchIcon {
  position: absolute;
  top: 11px;
  left: 26px;
  width: 20px;
  font-size: 20px;
}
</style>

<style>
.property {
  position: relative;
}

.property::after {
  position: absolute;
  top: 95%;
  left: 50%;
  z-index: 1;
  width: 0;
  height: 0;
  content: "";
  border-top: 7px solid #d0d5dd;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
  transition: all 0.3s ease-out;
  transform: translate(-50%, 0);
}

.property::before {
  position: absolute;
  top: 95%;
  left: 50%;
  z-index: 2;
  width: 0;
  height: 0;
  content: "";
  border-top: 6px solid #fff;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  transition: all 0.3s ease-out;
  transform: translate(-50%, 0);
}

.property .appointment-info {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px;
  font-size: 12px;
  background: #fff;
  border: 1px solid #d0d5dd;
  border-radius: 2ex;
}

.property .circleText {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  background: green;
  border-radius: 50%;
}

/* appointement map header style */
.appointment-header {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 2;
  display: flex;
  flex-wrap: wrap;
}

.appointment-header .el-input__wrapper {
  padding: 0 10px;
  border: 1px solid #e9ebf0;
  border-radius: 20px;
  box-shadow: 0 1px 4px -1px rgb(0 0 0 / 10%);
}

.appointment-header .el-checkbox-button__inner {
  font-size: 13px;
  font-weight: 500;
  line-height: 15px;
  color: var(--font-color-level1);
  background: #fff;
  border: none !important;
  border-radius: 20px !important;
  box-shadow: none !important;
}

.appointment-header .el-checkbox-group {
  padding: 0 10px;
  margin-left: 10px;
  border-left: 1px solid #ddd;
}

.appointment-header .el-checkbox-button.is-checked .el-checkbox-button__inner,
.el-checkbox-button:hover .el-checkbox-button__inner {
  color: var(--el-color-primary);
  background: #f5ebfc !important;
  border: 1px solid var(--el-color-primary) !important;
}

.appointment-header .checkUnsigned {
  display: flex;
  align-items: center;
  height: 30px;
  padding: 0 10px;
  font-size: 13px;
  font-weight: 500;
  color: var(--font-color-level1);
  cursor: pointer;
  background: #fff;
  border: none;
  border-radius: 20px;
}

.appointment-header .checkUnsigned.active {
  color: var(--el-color-primary);
  background: #f5ebfc;
  border: 1px solid var(--el-color-primary);
}
</style>
