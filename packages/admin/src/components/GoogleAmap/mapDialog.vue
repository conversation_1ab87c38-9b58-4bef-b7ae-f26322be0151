<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { GoogleAmap } from "@/components/GoogleAmap/";

const emit = defineEmits<{
  (e: "toUpdateAddress", val: Object): void;
}>();

const dialogVisible = ref(false);
const promise: any = {};
const googleAmapRef = ref(null);
const formData = ref({});

function show(data = null) {
  dialogVisible.value = true;
  if (data && data.longitude && data.latitude) {
    formData.value = {
      longitude: parseFloat(data.longitude),
      latitude: parseFloat(data.latitude),
      address: data.address
    };
  } else {
    formData.value = null;
  }
  nextTick(() => {
    if (googleAmapRef.value) {
      googleAmapRef.value.init(formData.value);
    }
  });

  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function toUpdateAddress(addressInfo) {
  formData.value = addressInfo;
  emit("toUpdateAddress", addressInfo);
  dialogVisible.value = false;
}
onMounted(() => {});
defineExpose({ show });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'Select Address'"
    width="80%"
    class="map-box"
  >
    <GoogleAmap
      v-if="dialogVisible"
      ref="googleAmapRef"
      @toUpdateAddress="toUpdateAddress"
      :formData="formData"
    />
  </el-dialog>
</template>
