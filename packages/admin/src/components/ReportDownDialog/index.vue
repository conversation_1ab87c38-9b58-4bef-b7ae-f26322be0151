<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { useCommonStoreHook } from "@/store/modules/common";

const dialogVisible = ref(false);
const promise: any = {};
const title = ref("Do you want to generate and download report ?");
const content = ref("");
const form = reactive({
  subCategoryIds: []
});
const formRef = ref(null);
const rules = {
  sub_product: [
    { required: false, message: "Please select a type", trigger: "change" }
  ]
};
const productCategoryList = ref([]);

function initSearchData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
}

function show() {
  initFormData();
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function initFormData() {
  Object.keys(form).forEach(key => {
    switch (key) {
      case "subCategoryIds":
        form[key] = [];
        break;
      default:
        form[key] = null;
        break;
    }
  });
}

function hide(actionOver = false, resData = null) {
  dialogVisible.value = false;
  promise.resolve(actionOver ? resData : false);
}

function confirmHandle() {
  formRef.value.validate(valid => {
    console.log("form", form);
    if (valid) {
      hide(true, form);
    }
  });
  return;
}
onMounted(() => {
  initSearchData();
});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog v-model="dialogVisible" :title="''" align-center :width="'550px'">
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="100"
      class="w-[99/100]"
      label-position="left"
    >
      <div class="center-box">
        <div class="title level-1">{{ title }}</div>
        <div class="content">
          {{ content }}
        </div>

        <el-row class="mt-[20px]">
          <el-col :span="24">
            <el-form-item prop="sub_product" label="Sub Product">
              <el-select
                v-model="form.subCategoryIds"
                placeholder="Select"
                filterable
                clearable
                multiple
                collapse-tags
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] w-[100%]"
                collapse-tags-tooltip
              >
                <el-option-group
                  v-for="group in productCategoryList"
                  :key="group.name"
                  :label="group.name"
                >
                  <el-option
                    v-for="item in group.products"
                    :key="item.id"
                    :label="item.name"
                    :value="`${item.id}`"
                  />
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide(false)"
          >Later</el-button
        >
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          @click="confirmHandle"
          >{{ transformI18n("leads.confirm") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.result-icon {
  width: 50px;
  height: 50px;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.warning {
  color: #ff9000;
}

.iconfont {
  font-size: 24px;
}
</style>
