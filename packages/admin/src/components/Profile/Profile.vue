<script setup lang="ts">
import { ref } from "vue";

import { transformI18n } from "@/plugins/i18n";
import { useNav } from "@/layout/hooks/useNav";

const dialogVisible = ref(false);
const promise: any = {};
const { userBaseInfo } = useNav();
function show() {
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}
function hide() {
  dialogVisible.value = false;
}

defineExpose({ show });
</script>

<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="transformI18n('common.viewProfile')"
      width="38.75rem"
    >
      <el-descriptions
        class="w-[99/100] ml-7 mr-7 mt-5"
        :column="1"
        :colon="false"
      >
        <el-descriptions-item :label="transformI18n('account.username')">{{
          userBaseInfo.login_name
        }}</el-descriptions-item>
        <el-descriptions-item :label="transformI18n('account.position')">{{
          userBaseInfo.position || "-"
        }}</el-descriptions-item>
        <el-descriptions-item
          :label="transformI18n('account.role')"
          v-if="userBaseInfo.roles"
          >{{ userBaseInfo.roles.join(",") }}</el-descriptions-item
        >
        <el-descriptions-item :label="transformI18n('account.email')">{{
          userBaseInfo.email || "-"
        }}</el-descriptions-item>
        <el-descriptions-item :label="transformI18n('account.mobile')">{{
          userBaseInfo.mobile || "-"
        }}</el-descriptions-item>
        <el-descriptions-item
          :label="transformI18n('account.productCategory')"
          >{{ userBaseInfo.products || "-" }}</el-descriptions-item
        >
      </el-descriptions>
      <template #footer>
        <span class="dialog-footer">
          <el-button class="w-52 !text-sm h-10" @click="hide" type="primary">{{
            transformI18n("leads.confirm")
          }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.title {
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  color: var(--el-color-primary);
}

@media screen and (width <= 520px) {
  ::v-deep(.el-dialog) {
    width: 80% !important;
  }
}
</style>
