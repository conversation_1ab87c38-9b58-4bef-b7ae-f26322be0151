<script setup lang="ts">
import { ref } from "vue";
import { uploadControllerStore } from "@/api/upload";
import { message } from "@/utils/message";
import { ElMessage } from "element-plus";

const props = defineProps({
  fileList: [],
  accept: {
    type: Array,
    default() {
      return ["image/png", "image/jpeg", "image/png", "application/pdf"];
    }
  },
  otherParam: {
    type: Object,
    default() {
      return {};
    }
  },
  maxSize: {
    type: Number,
    default: null
  },
  showFileList: {
    type: Boolean,
    default: false
  },
  btnTxt: {
    type: String,
    default: "Upload File"
  },
  btnIcon: {
    type: String,
    default: "just-icon-upload"
  },
  btnClass: {
    type: String,
    default: "subBtn"
  },
  // 新增：是否允许修改文件名
  allowRename: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits<{
  (e: "fileChange", val: object): void;
}>();

const loading = ref(false);
const uploadRef = ref("");
const showRenameDialog = ref(false);
const currentFile = ref(null);
const newFileName = ref("");
// 获取文件扩展名
const getFileExtension = (filename: string) => {
  return filename.slice(filename.lastIndexOf("."));
};

// 获取不带扩展名的文件名
const getFileNameWithoutExtension = (filename: string) => {
  return filename.slice(0, filename.lastIndexOf("."));
};

// 上传前验证
function beforeUpload(rawFile: any) {
  console.log("beforeAvatarUpload", rawFile);
  if (props.accept && props.accept.length) {
    if (
      !props.accept.includes(rawFile.type) &&
      !rawFile.type.includes("image")
    ) {
      message("The file format is incorrect. Please upload a pdf or picture", {
        type: "error"
      });
      return false;
    }
  }
  if (props.maxSize) {
    if (rawFile.size > props.maxSize) {
      message("The file too bigger", { type: "error" });
      return false;
    }
  }
  return true;
}

// 文件选择处理
function fileChange(val: any) {
  if (!beforeUpload(val.raw)) {
    return false;
  }

  // 如果允许重命名，显示重命名对话框
  if (props.allowRename) {
    currentFile.value = val;
    newFileName.value = getFileNameWithoutExtension(val.name);
    showRenameDialog.value = true;
  } else {
    // 直接上传
    uploadFile(val, val.name);
  }
}

// 确认重命名并上传
function confirmRename() {
  if (!currentFile.value) return;

  // 新文件上传
  const extension = getFileExtension(currentFile.value.name);
  const finalFileName = newFileName.value + extension;

  uploadFile(currentFile.value, finalFileName);
  showRenameDialog.value = false;
}

// 取消重命名
function cancelRename() {
  showRenameDialog.value = false;
  currentFile.value = null;
  newFileName.value = "";
}

// 上传文件
function uploadFile(fileObj: any, fileName: string) {
  let param = {
    file: fileObj.raw,
    name: fileName
  };

  if (props.otherParam) {
    param = {
      file: fileObj.raw,
      name: fileName,
      ...props.otherParam
    };
  }

  loading.value = true;
  uploadControllerStore(param)
    .then((res: any) => {
      loading.value = false;

      // 构建文件信息
      const fileInfo = {
        ...res,
        originalName: fileObj.name,
        displayName: fileName,
        size: fileObj.size,
        type: fileObj.raw.type
      };

      emit("fileChange", fileInfo);
      ElMessage.success("Upload success");
    })
    .catch((err: any) => {
      loading.value = false;
      ElMessage.error("Upload failed: " + err);
    });
}
</script>

<template>
  <div class="upload-file-container">
    <!-- 上传按钮 -->
    <el-upload
      ref="uploadRef"
      v-loading="loading"
      :auto-upload="false"
      :on-change="fileChange"
      :show-file-list="false"
      class="upload-section"
    >
      <el-button type="primary" :class="`${btnClass} w-[100%]`">
        <span :class="`iconfont ${props.btnIcon} mr-2`" />
        {{ props.btnTxt }}
      </el-button>
    </el-upload>

    <!-- 重命名对话框 -->
    <el-dialog
      v-model="showRenameDialog"
      title="Rename File"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="rename-content">
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            Original Name:
          </label>
          <div class="text-sm text-gray-600 bg-gray-100 p-2 rounded">
            {{ currentFile?.name }}
          </div>
        </div>

        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            New Name:
          </label>
          <el-input
            v-model="newFileName"
            placeholder="Enter new file name (without extension)"
            @keyup.enter="confirmRename"
          />
          <div class="text-xs text-gray-500 mt-1">
            Extension {{ getFileExtension(currentFile?.name || "") }} will be
            kept automatically
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelRename">Cancel</el-button>
          <el-button
            type="primary"
            @click="confirmRename"
            :disabled="!newFileName.trim()"
          >
            Confirm
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.upload-file-container {
  width: 100%;
}

.upload-section {
  width: 100%;
}

.rename-content {
  padding: 8px 10px;
  text-align: left;

  :deep(.el-input__wrapper) {
    padding: 0 5px;
    border: 1px solid #e9ebf0 !important;
    border-radius: 5px !important;
    box-shadow: 0 1px 4px -1px rgb(0 0 0 / 10%) !important;
  }
}

.dialog-footer {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
</style>
