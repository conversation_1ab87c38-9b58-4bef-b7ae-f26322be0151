<template>
  <el-upload
    ref="uploadRef"
    v-loading="loading"
    class="avatar-uploader"
    :action="actionUrl"
    :headers="headers"
    :drag="true"
    :accept="props.accept"
    :show-file-list="false"
    :on-success="handleUploadSuccess"
    :before-upload="beforeAvatarUpload"
    :on-error="handleUploadFail"
  >
    <img v-if="props.imageUrl" :src="props.imageUrl" class="avatar" />
    <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
  </el-upload>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import { message } from "@/utils/message";
import { Plus } from "@element-plus/icons-vue";
import { getToken, formatToken } from "@/utils/auth";

import type { UploadProps } from "element-plus";

const headers = reactive({
  Authorization: ""
});
const loading = ref(false);
const uploadRef = ref("");

const props = defineProps({
  imageUrl: String,
  accept: {
    type: String,
    default: "image/png,image/jpeg, image/png"
  }
});
const actionUrl =
  process.env.NODE_ENV === "development"
    ? `http://localhost/api/admin/upload`
    : `/api/admin/upload`;
const imgArray = ["image/jpeg", "image/jpg", "image/png"];

const emit = defineEmits<{
  (e: "imageChange", val: object): void;
}>();

function setHeaders() {
  const data = getToken();
  headers["Authorization"] = formatToken(data.access_token);
}

// 上传成功
const handleUploadSuccess: UploadProps["onSuccess"] = response => {
  imageChange(response.data);
  loading.value = false;
};
const handleUploadFail: UploadProps["onError"] = response => {
  console.log("handleUploadFail", response);
  message("图片上传失败", { type: "error" });
};

// 上传前
const beforeAvatarUpload: UploadProps["beforeUpload"] = rawFile => {
  console.log("beforeAvatarUpload");
  setHeaders();
  loading.value = true;
  if (!imgArray.includes(rawFile.type)) {
    message("图片格式不正确", { type: "error" });
    return false;
  } else if (rawFile.size / 1024 / 1024 > 2) {
    message("图片大小超过2MB", { type: "error" });
    return false;
  }
  return true;
};

function imageChange(val) {
  emit("imageChange", val);
}

onMounted(() => {});

defineExpose({});
</script>

<style scoped>
.avatar-uploader .avatar {
  display: block;
  width: 178px;
  height: 178px;
}
</style>

<style>
.avatar-uploader .el-upload {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  width: 178px;
  height: 178px;
  font-size: 28px;
  color: #8c939d;
  text-align: center;
}
</style>
