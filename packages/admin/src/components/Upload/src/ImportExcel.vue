<script setup lang="ts">
import { ref } from "vue";
import { message } from "@/utils/message";
// import { ElMessage } from "element-plus";

const props = defineProps({
  fileList: [],
  accept: {
    type: Array,
    default() {
      return [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel"
      ];
    }
  },
  otherParam: {
    type: Object,
    default() {
      return {};
    }
  },
  maxSize: {
    type: Number,
    default: null
  },
  showFileList: {
    type: Boolean,
    default: false
  },
  btnTxt: {
    type: String,
    default: "Upload File"
  },
  btnIcon: {
    type: String,
    default: ""
  },
  btnClass: {
    type: String,
    default: "subBtn"
  },
  btnType: {
    type: String,
    default: "info"
  },
  loading: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits<{
  (e: "fileChange", val: object): void;
}>();

const uploadRef = ref(null);
// 上传前
function beforeUpload(rawFile) {
  console.log("beforeAvatarUpload", rawFile);
  if (props.accept && props.accept.length) {
    if (!props.accept.includes(rawFile.type)) {
      message(
        "The file format is incorrect. Please select the correct file type.",
        {
          type: "error"
        }
      );
      return false;
    }
  }
  return true;
}

function fileChange(val) {
  if (!beforeUpload(val.raw)) {
    return false;
  }
  let param = {
    file: val.raw
  };
  if (props.otherParam) {
    param = {
      file: val.raw
    };
  }
  emit("fileChange", param);
}
</script>

<template>
  <el-upload
    ref="uploadRef"
    :auto-upload="false"
    :on-change="fileChange"
    :show-file-list="props.showFileList"
    class="w-[100%]"
  >
    <el-button
      :type="btnType"
      :class="`${btnClass} w-[100%]`"
      :loading="props.loading"
      :disabled="props.loading"
    >
      <span :class="`iconfont ${props.btnIcon} mr-2`" v-if="props.btnIcon" />
      {{ props.btnTxt }}
    </el-button>
  </el-upload>
</template>
