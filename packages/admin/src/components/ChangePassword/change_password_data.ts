import { requiredField } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";

export const REGEXP_PASSWORD = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{10,}$/;

export const rules = {
  old: [requiredField(transformI18n("common.oldPassword"))],
  new: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          return true;
        } else if (!REGEXP_PASSWORD.test(value)) {
          callback(new Error(transformI18n("login.passwordRuleReg")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  repeat: [requiredField(transformI18n("common.confirmPassword"))]
};
