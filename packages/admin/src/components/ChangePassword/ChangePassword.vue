<script setup lang="ts">
import { reactive, ref } from "vue";
import { accountControllerChangePassword } from "@/api/admin/admin-account";
import { sendFormFilter } from "@/utils/form";

import { transformI18n } from "@/plugins/i18n";
import { rules } from "./change_password_data";
import { message } from "@/utils/message";

const form = reactive({
  old: null,
  new: null,
  repeat: null
});
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
function show() {
  Object.keys(form).forEach(key => {
    form[key] = null;
  });
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function hide() {
  dialogVisible.value = false;
  promise.resolve(true);
}

async function onSave() {
  await formRef.value.validate(valid => {
    if (valid) {
      if (form.new != form.repeat) {
        message(transformI18n("login.passwordDifferentReg"), { type: "error" });
        return;
      }
      if (form.new == form.old) {
        message(transformI18n("common.sameAsOladPassword"), { type: "error" });
        return;
      }
      loading.value = true;
      const sendForm = sendFormFilter(form);
      accountControllerChangePassword(sendForm)
        .then(res => {
          loading.value = false;
          if (res.success) {
            message(transformI18n("common.success"), { type: "success" });
            hide();
          }
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
}

defineExpose({ show });
</script>

<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="transformI18n('common.changePassword')"
      width="38.75rem"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        class="w-[99/100] ml-7 mr-7"
      >
        <el-row>
          <el-col class="mt-5 !mb-0 text-sm text-[#2A2E34]" :span="24">{{
            transformI18n("common.oldPassword")
          }}</el-col>
          <el-col>
            <el-form-item prop="old">
              <el-input
                v-model="form.old"
                type="password"
                :placeholder="transformI18n('common.oldPasswordRequire')"
                autocomplete="off"
              />
            </el-form-item>
          </el-col>
          <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
            transformI18n("common.newPassword")
          }}</el-col>
          <el-col class="new-password">
            <el-form-item prop="new">
              <el-input
                v-model="form.new"
                type="password"
                :placeholder="transformI18n('common.newPasswordRequire')"
                autocomplete="off"
              />
            </el-form-item>
          </el-col>
          <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
            transformI18n("common.confirmPassword")
          }}</el-col>
          <el-col :span="24">
            <el-form-item prop="repeat" class="!mr-2">
              <el-input
                v-model="form.repeat"
                type="password"
                :placeholder="transformI18n('login.passwordSureReg')"
                autocomplete="off"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide">{{
            transformI18n("buttons.hsCancel")
          }}</el-button>
          <el-button
            class="w-52 !text-sm h-10"
            type="primary"
            :disabled="loading"
            :loading="loading"
            @click="onSave"
            >{{ transformI18n("leads.confirm") }}</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
.title {
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  color: var(--el-color-primary);
}

.dialog-footer {
  display: flex;
}

@media screen and (width <= 520px) {
  ::v-deep(.el-dialog) {
    width: 80% !important;
  }

  ::v-deep(.new-password .is-error) {
    margin-bottom: 30px;
  }
}
</style>
./change_password_data
