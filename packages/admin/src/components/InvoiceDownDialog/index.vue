<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { transformI18n } from "@/plugins/i18n";

const dialogVisible = ref(false);
const promise: any = {};
const title = ref("Do you want to down invoice ?");
const content = ref("");
const form = reactive({
  type: null,
  amount: null,
  date: ""
});
const formRef = ref(null);
const rules = {
  type: [
    { required: true, message: "Please select a type", trigger: "change" }
  ],
  date: [
    { required: true, message: "Please select a date", trigger: "change" }
  ],
  amount: [
    { required: true, message: "Please enter the amount", trigger: "change" }
  ]
};
const typeOption = [
  { label: "Due invoice", value: "due_invoice" },
  { label: "Paid invoice", value: "paid_invoice" }
];

function show() {
  initFormData();
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function initFormData() {
  Object.keys(form).forEach(key => {
    switch (key) {
      case "type":
        form[key] = typeOption[0].value;
        break;
      default:
        form[key] = null;
        break;
    }
  });
}

function hide(actionOver = false, resData = null) {
  dialogVisible.value = false;
  promise.resolve(actionOver ? resData : false);
}

function confirmHandle() {
  formRef.value.validate(valid => {
    console.log("form", form);
    if (valid) {
      hide(true, form);
    }
  });
  return;
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog v-model="dialogVisible" :title="''" align-center :width="'550px'">
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="80"
      class="w-[99/100]"
      label-position="left"
    >
      <div class="center-box">
        <div class="title level-1">{{ title }}</div>
        <div class="content">
          {{ content }}
        </div>

        <el-row class="mt-[20px]">
          <el-col :span="24">
            <el-form-item prop="type" label="Type">
              <el-select
                v-model="form.type"
                :placeholder="transformI18n('common.select')"
                size="small"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] w-[100%]"
              >
                <el-option
                  v-for="item in typeOption"
                  :label="item.label"
                  :value="item.value"
                  :key="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.type == 'due_invoice'">
            <el-form-item prop="date" label="Date">
              <el-date-picker
                v-model="form.date"
                type="date"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] grow"
                :placeholder="transformI18n('common.select')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.type == 'due_invoice'">
            <el-form-item prop="amount" label="Amount">
              <el-input-number
                v-model="form.amount"
                :min="1"
                :placeholder="transformI18n('common.input')"
                controls-position="right"
                class="border border-[#E9EBF0] rounded-[6px] grow"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide(false)"
          >Later</el-button
        >
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          @click="confirmHandle"
          >{{ transformI18n("leads.confirm") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.result-icon {
  width: 50px;
  height: 50px;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.warning {
  color: #ff9000;
}

.iconfont {
  font-size: 24px;
}
</style>
