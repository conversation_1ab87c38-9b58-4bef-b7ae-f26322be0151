<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { leadsControllerConvert } from "@/api/admin/leads";
import { transformI18n } from "@/plugins/i18n";
import { sendFormFilter } from "@/utils/form";
import { ElMessage } from "element-plus";

const dialogVisible = ref(false);
const promise: any = {};
const title = ref("Do you want to convert the lead into an order?");
const content = ref(
  "After the lead is created into order, the lead cannot be edited, and this operation is irreversible. Please confirm the conversion operation."
);
const convertLoading = ref(false);
const form = reactive({
  lead_id: 0,
  type: "Cash",
  amount: null,
  detail: "",
  received_date: "",
  old_payment_id: null
});
const formRef = ref(null);
const rules = {
  type: [
    { required: true, message: "Please select a type", trigger: "change" }
  ],
  amount: [
    { required: true, message: "Please enter the amount", trigger: "change" }
  ],
  estimated_comm: [
    {
      required: true,
      message: "Please enter the estimated comm",
      trigger: "change"
    }
  ],
  detail: [
    { required: true, message: "Please enter the detail", trigger: "change" }
  ],
  received_date: [
    {
      required: true,
      message: "Please enter the received date",
      trigger: "change"
    }
  ]
};
const typeOption = [
  "Cash",
  "Cheque",
  "Credit",
  "CreditCard",
  "Direct Tsf",
  "Finn",
  "Finn Cost",
  "Refund",
  "Unpaid"
];
// function disabledDate(time) {
//   return time.getTime() < Date.now();
// }
function show(data) {
  if (!data.lead_id) return;
  initFormData();
  form.lead_id = data.lead_id;
  if (data.stagnation_order_desposit) {
    form.type = data.stagnation_order_desposit.type;
    form.amount = data.stagnation_order_desposit.amount;
    form.detail = data.stagnation_order_desposit.detail;
    form.received_date = data.stagnation_order_desposit.received_date;
    form.old_payment_id = data.stagnation_order_desposit.id;
  }
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function disabledDate(time) {
  return time.getTime() > Date.now();
}

function initFormData() {
  Object.keys(form).forEach(key => {
    switch (key) {
      case "type":
        form[key] = "Cash";
        break;
      default:
        form[key] = null;
        break;
    }
  });
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  promise.resolve(actionOver);
}

function doConvertToOrder() {
  formRef.value.validate(valid => {
    if (valid) {
      convertLoading.value = true;
      const sendForm = sendFormFilter(form);
      delete sendForm.lead_id;
      leadsControllerConvert(form.lead_id, sendForm)
        .then(res => {
          convertLoading.value = false;
          if (res.success) {
            ElMessage.success(res.message);
            hide(true);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_err => {
          convertLoading.value = false;
          ElMessage.error("Convert lead to order fail.");
        });
    }
  });
  return;
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog v-model="dialogVisible" :title="''" align-center :width="'550px'">
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="center-box">
        <span class="mr-1 primary-color iconfont just-icon-convert" />
        <div class="title level-1">{{ title }}</div>
        <div class="content">
          {{ content }}
        </div>

        <el-row class="mt-[20px]">
          <el-col :span="24">
            <el-form-item prop="type" label="Type">
              <el-select
                v-model="form.type"
                :placeholder="transformI18n('common.select')"
                size="small"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] w-[100%]"
              >
                <el-option
                  v-for="item in typeOption"
                  :label="item"
                  :value="item"
                  :key="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="received_date" label="Date Received">
              <el-date-picker
                v-model="form.received_date"
                type="date"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] grow"
                :placeholder="transformI18n('common.select')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="amount" label="Amount Received">
              <el-input-number
                v-model="form.amount"
                :min="1"
                :precision="2"
                :placeholder="transformI18n('common.input')"
                controls-position="right"
                class="border border-[#E9EBF0] rounded-[6px] grow"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="detail" label="Receipt Details">
              <el-input
                v-model="form.detail"
                type="textarea"
                size="small"
                :rows="3"
                :placeholder="transformI18n('common.input')"
                autocomplete="off"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px]"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide(false)"
          >Later</el-button
        >
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :loading="convertLoading"
          @click="doConvertToOrder"
          >{{ transformI18n("leads.confirm") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.result-icon {
  width: 50px;
  height: 50px;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.warning {
  color: #ff9000;
}

.iconfont {
  font-size: 24px;
}
</style>
