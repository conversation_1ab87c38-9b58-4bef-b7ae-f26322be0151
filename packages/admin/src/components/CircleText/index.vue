<script setup lang="ts">
import { computed } from "vue";
const props = defineProps({
  size: {
    type: Number,
    default: 26
  },
  text: {
    type: String,
    default: ""
  },
  fontSize: {
    type: String,
    default: "13px"
  },
  customBgColor: {
    type: String,
    default: null
  },
  showNum: {
    type: Number,
    default: 2
  }
});

const showText = computed(() => {
  if (!props.text) return "";
  return sliceText(props.text);
});

const bgcolor = computed(() => {
  if (props.customBgColor) {
    return props.customBgColor;
  } else {
    const colors = [
      "#007AFF",
      "#F6272A",
      "#6B55D3",
      "#ff7350",
      "#FFB442",
      "#25B856",
      "#42E0FF"
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }
});

function sliceText(value) {
  if (!value) return "";
  if (isChineseCharacter(value[0])) {
    return value[0];
  } else {
    return value.slice(0, props.showNum);
  }
}
function isChineseCharacter(char) {
  return /^[\u4E00-\u9FFF]$/.test(char);
}
</script>
<template>
  <el-avatar
    v-if="showText && showText.length > 0"
    class="circle-text mr-1"
    :size="props.size"
    :style="{ background: bgcolor, 'font-size': props.fontSize }"
    >{{ showText }}</el-avatar
  >
</template>
<style scoped>
.circle-text {
  flex-shrink: 0;
}
</style>
