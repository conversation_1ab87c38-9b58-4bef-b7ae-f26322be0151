<script setup lang="ts">
import { reactive, ref, nextTick } from "vue";
import { leadsControllerChangeResult } from "@/api/admin/leads";
import { orderControllerChangeResult } from "@/api/admin/order";
import { serviceControllerChangeResult } from "@/api/admin/service";
import { sendFormFilter } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
import { rules } from "./change_result_data";
import { UploadFile } from "@/components/Upload";
import { message } from "@/utils/message";
import { requiredField } from "@/utils/form";

const currentRules = ref(rules);
const form = reactive({
  id: null,
  type: null, // leads or order
  comment: null,
  result: null,
  sub_result: null,
  followup_time: null,
  sold: null,
  quoted: null,
  retail: null,
  note: null,
  on_product_date: null,
  ready_date: null,
  row: null,
  estimated_comm: null,
  submit_date: null,
  approved_date: null
});
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const uploadParam = ref(null);
const showFollowUp = ref(false);
const showApprovedDate = ref(false);

function show(data) {
  Object.keys(form).forEach(key => {
    form[key] = null;
  });
  showFollowUp.value = false;
  Object.assign(form, data);
  if (data.row) {
    form.retail = data.row.retail;
    form.sold = data.row.sold;
    form.followup_time = data.row.followup_time;
    form.quoted = data.row.quoted;
  }
  if (data.row && data.row.result == "installation" && data.result == "ready") {
    showFollowUp.value = true;
  }
  currentRules.value.note = [];
  showApprovedDate.value = false;

  if (data.row && data.row.result == "inCouncil") {
    showApprovedDate.value = true;
    currentRules.value.note = [requiredField("Note")];
  }
  if (data.result == "inCouncil") {
    currentRules.value.note = [requiredField("Note")];
  }
  uploadParam.value = {
    object_id: form.id,
    type: "contract"
  };

  dialogVisible.value = true;
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  });
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function uploadFileChange(_res) {
  // do somethin about the upload contract file
}

function disabledDate(time) {
  if (
    form.result == "ready" ||
    (form.result == "onProduction" && form.row && form.row.result == "ready")
  ) {
    return false;
  }
  return time.getTime() < Date.now();
}

function hide(e = null) {
  dialogVisible.value = false;
  if (e?.cancelable) {
    promise.resolve(false);
  } else {
    promise.resolve(true);
  }
}

async function onSave() {
  await formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      delete form.row;
      const sendForm = sendFormFilter(form);
      if (form.type == "leads") {
        leadsControllerChangeResult(form.id, sendForm)
          .then(res => {
            loading.value = false;
            if (!res.success) {
              message(res.message, { type: "error" });
            }
            hide();
            promise.resolve(true);
          })
          .catch(() => {
            loading.value = false;
          });
      } else if (form.type == "order") {
        orderControllerChangeResult(form.id, sendForm)
          .then(res => {
            loading.value = false;
            if (!res.success) {
              message(res.message, { type: "error" });
            }
            hide();
            promise.resolve(true);
          })
          .catch(() => {
            loading.value = false;
          });
      } else if (form.type == "service") {
        serviceControllerChangeResult(form.id, sendForm)
          .then(res => {
            loading.value = false;
            if (!res.success) {
              message(res.message, { type: "error" });
            }
            hide();
            promise.resolve(true);
          })
          .catch(() => {
            loading.value = false;
          });
      }
    }
  });
}

defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="transformI18n('common.changeResult')"
    width="38.75rem"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="currentRules"
      class="w-[99/100] ml-7 mr-7"
    >
      <el-row>
        <el-col class="text-[#8831CC] mt-7 justify-center items-center flex">
          <span
            class="justify-center mr-2 items-center flex rounded-[6px] border-[1px] border-[#E9EBF0] w-11 h-11"
            ><i
              :class="
                '!text-2xl iconfont just-icon-' +
                form.result +
                ' input-result-icon'
              "
            />
          </span>
          <span class="title capitalize"
            >{{ transformI18n("common.result") }}:{{ form.result }}
            <span class="capitalize" v-if="form.sub_result"
              >-{{ form.sub_result }}</span
            ></span
          >
        </el-col>
        <el-col class="text-xs text-center text-[#656F7D]">
          {{ transformI18n("common.resultInfoFirst") }} {{ form.type }}
          {{ transformI18n("common.resultInfoSecond") }}
          <span class="font-bold capitalize"
            >{{ form.result
            }}<span v-if="form.sub_result">-{{ form.sub_result }}</span></span
          >

          {{ transformI18n("common.resultInfoLast") }}
          <span v-if="form.result == 'cancelled'">{{
            transformI18n("common.resultCancelInfo")
          }}</span>
          <!-- <span v-if="form.result == 'completion'">{{
            transformI18n("common.resultCompletionInfo")
          }}</span> -->
        </el-col>

        <el-col
          v-if="form.result == 'followup' || showFollowUp"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >{{ transformI18n("common.followupDate") }}
        </el-col>
        <el-col v-if="form.result == 'followup' || showFollowUp" class="!mb-5">
          <el-form-item prop="followup_time">
            <el-date-picker
              v-model="form.followup_time"
              type="date"
              class="w-full"
              :placeholder="transformI18n('common.select')"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="form.result == 'sold'"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >{{ transformI18n("common.soldAmount") }}</el-col
        >
        <el-col v-if="form.result == 'sold'">
          <el-form-item prop="sold">
            <el-input-number
              v-model="form.sold"
              :min="1"
              :precision="2"
              :placeholder="transformI18n('common.soldAmount')"
              controls-position="right"
              class="grow"
            />
          </el-form-item>
        </el-col>

        <el-col
          v-if="form.result == 'sold'"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >Estimated Comm</el-col
        >
        <el-col v-if="form.result == 'sold'">
          <el-form-item prop="estimated_comm">
            <el-input
              v-model="form.estimated_comm"
              :placeholder="'Estimated Comm'"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>

        <el-col
          v-if="form.result == 'quoted'"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >{{ transformI18n("common.quotedAmount") }}</el-col
        >
        <el-col v-if="form.result == 'quoted'">
          <el-form-item prop="quoted">
            <el-input-number
              v-model="form.quoted"
              :min="1"
              :precision="2"
              :placeholder="transformI18n('common.quotedAmount')"
              controls-position="right"
              class="grow"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="
            form.type == 'leads' &&
            (form.result == 'sold' || form.result == 'quoted')
          "
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >{{ transformI18n("common.retailAmount") }}</el-col
        >
        <el-col
          v-if="
            form.type == 'leads' &&
            (form.result == 'sold' || form.result == 'quoted')
          "
        >
          <el-form-item prop="retail">
            <el-input-number
              v-model="form.retail"
              :min="1"
              :precision="2"
              :placeholder="transformI18n('common.retailAmount')"
              controls-position="right"
              class="grow"
            />
          </el-form-item>
        </el-col>
        <el-col
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          v-if="form.type === 'leads'"
          >{{ transformI18n("common.comments") }}</el-col
        >
        <el-col :span="24" v-if="form.type === 'leads'">
          <el-form-item prop="comment" class="!mr-2">
            <el-input
              v-model="form.comment"
              type="textarea"
              :rows="3"
              :placeholder="transformI18n('common.changeComments')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>

        <!-- order form filed -->
        <el-col
          v-if="form.result == 'onProduction'"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >{{ transformI18n("orders.on_product_date") }}</el-col
        >
        <el-col v-if="form.result == 'onProduction'" class="!mb-5">
          <el-form-item prop="on_product_date">
            <el-date-picker
              v-model="form.on_product_date"
              type="date"
              class="w-full"
              :placeholder="transformI18n('common.select')"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="form.result == 'ready' && !showFollowUp"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >{{ transformI18n("orders.ready_date") }}</el-col
        >
        <el-col v-if="form.result == 'ready' && !showFollowUp" class="!mb-5">
          <el-form-item prop="ready_date">
            <el-date-picker
              v-model="form.ready_date"
              type="date"
              class="w-full"
              :placeholder="transformI18n('common.select')"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="form.result == 'inCouncil'"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >Submit Date</el-col
        >
        <el-col v-if="form.result == 'inCouncil'" class="!mb-5">
          <el-form-item prop="submit_date">
            <el-date-picker
              v-model="form.submit_date"
              type="date"
              class="w-full"
              :placeholder="transformI18n('common.select')"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="showApprovedDate"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >Approved Date</el-col
        >
        <el-col v-if="showApprovedDate" class="!mb-5">
          <el-form-item prop="approved_date">
            <el-date-picker
              v-model="form.approved_date"
              type="date"
              class="w-full"
              :placeholder="transformI18n('common.select')"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="form.type === 'order' || form.type === 'service'"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >{{ transformI18n("orders.note") }}</el-col
        >
        <el-col
          :span="24"
          v-if="form.type === 'order' || form.type === 'service'"
        >
          <el-form-item prop="note" class="!mr-2">
            <el-input
              v-model="form.note"
              type="textarea"
              :rows="3"
              :placeholder="transformI18n('common.changeComments')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="form.result == 'contract'"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >Contract</el-col
        >
        <el-col :span="8" v-if="form.result == 'sold'">
          <UploadFile
            ref="uploadFileRef"
            :otherParam="uploadParam"
            @fileChange="uploadFileChange"
            :btnTxt="'Upload Contract'"
          />
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide">{{
          transformI18n("buttons.hsCancel")
        }}</el-button>
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :disabled="loading"
          :loading="loading"
          @click="onSave"
          >{{ transformI18n("leads.confirm") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<style lang="scss" scoped>
.title {
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  color: var(--el-color-primary);
}
</style>
