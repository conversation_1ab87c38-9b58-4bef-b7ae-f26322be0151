import { requiredField } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";

export const rules = {
  retail: [requiredField(transformI18n("common.retailAmount"))],
  sold: [requiredField(transformI18n("common.soldAmount"))],
  quoted: [requiredField(transformI18n("common.quotedAmount"))],
  followup_time: [requiredField(transformI18n("common.followupDate"))],
  submit_date: [requiredField("Submit Date")],
  approved_date: [requiredField("Approved Date")],
  on_product_date: [requiredField("Product Date")],
  note: []
};
