import { requiredField } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";

export const rules = {
  object_id: [requiredField(transformI18n("appointment.objectId"))],
  date: [requiredField(transformI18n("appointment.date"))],
  endDate: [requiredField(transformI18n("appointment.date"))],
  time: [requiredField(transformI18n("appointment.time"))],
  end_time: [requiredField(transformI18n("appointment.time"))],
  address: [requiredField(transformI18n("client.address"))]
};

export const typeList = [
  {
    title: transformI18n("appointment.cm"),
    checked: true,
    type: "cm"
  },
  {
    title: transformI18n("appointment.install"),
    checked: false,
    type: "install"
  }
];

export const serviceTypeList = [
  {
    title: transformI18n("appointment.service"),
    checked: false,
    type: "service"
  }
];
