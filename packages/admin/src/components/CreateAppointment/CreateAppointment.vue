<script setup lang="ts">
import { reactive, ref } from "vue";
import {
  appointmentControllerStore,
  appointmentControllerUpdate
} from "@/api/admin/appointment";
import { leadsControllerIndex } from "@/api/admin/leads";
import { sendFormFilter } from "@/utils/form";
import dayjs from "dayjs";
import { transformI18n } from "@/plugins/i18n";
import { rules, typeList, serviceTypeList } from "./create_appointment";
import GoogelMap from "@/components/GoogleAmap/mapDialog.vue";
import LocationIcon from "@/assets/svg/location.svg?component";
import { hasAuth } from "@/router/utils";
import { ElMessage } from "element-plus";

const form = reactive({
  id: null,
  user_id: null,
  object_id: null,
  date: null, // leads or order
  endDate: null,
  comment: null,
  time: null,
  address: null,
  type: null,
  longitude: null,
  latitude: null,
  status: null,
  service_id: null,
  end_time: null,
  appt_duration: null
});
const notifyStr = ref(null);
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const googelMapRef = ref(null);
const selectObject = ref(false);
const client = ref("");
const isOrder = ref(false);
const isService = ref(false);
const getLeadsOptionsLoading = ref(false);
const leadsOptions = ref([]);
const startTime = ref("07:00");
const actionFromType = ref("leads"); // parent componet type:leads ,appointment,order,service,edit(=appointment)
function show(data, fromType = "leads") {
  resetForm();
  selectObject.value = false;
  actionFromType.value = fromType;
  if (!data) {
    selectObject.value = true;
    getLeadsOptions();
  } else if (fromType == "leads") {
    // come from leads create sales type appointment.only leads page sold result lead can create appt
    handleDefaultLeadsData(data);
  } else if (fromType == "order") {
    // come from order detail
    handleDefaultOrderData(data);
  } else if (fromType == "service") {
    // come from order detail
    handleDefaultServiceData(data);
  } else {
    // data from appointment
    handleDefaultAppointmentData(data);
  }
  dialogVisible.value = true;
  startTime.value = "07:00";
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

// data form appointment detail
function handleDefaultAppointmentData(data) {
  Object.assign(form, data);
  client.value =
    data.client.title +
    " " +
    data.client.surname +
    " " +
    data.client.given_name;
  isOrder.value = data.leads.order_id;
  if (data.service_id || data.order?.result == "completion") {
    isService.value = true;
    form.type = "service";
  }
  form.date = dayjs(data.date).format("YYYY-MM-DD 00:00");
  form.time = dayjs(data.date).format("hh:mm A");
  form.end_time = dayjs(data.date).add(0.5, "hour").format("hh:mm A");
  if (data.end_time) {
    form.endDate = dayjs(data.end_time).format("YYYY-MM-DD 00:00");
    form.end_time = dayjs(data.end_time).format("hh:mm A");
  } else {
    form.endDate = dayjs(data.date).format("YYYY-MM-DD 00:00");
    form.end_time = dayjs(data.date).add(0.5, "hour").format("hh:mm A");
  }
}

function handleDefaultOrderData(orderInfo) {
  const data = orderInfo.lead;
  form.object_id = data.id;
  form.address = data.address ? data.address : data.client.address;
  if (
    orderInfo.result == "new_order" ||
    orderInfo.result == "cmReceived" ||
    orderInfo.result == "onProduction" ||
    orderInfo.result == "outstanding" ||
    orderInfo.result == "extra"
  ) {
    form.type = "cm";
  } else if (orderInfo.result == "ready") {
    form.type = "install";
  }
  form.longitude = data.client.longitude;
  form.latitude = data.client.latitude;
  client.value =
    data.client.title +
    " " +
    data.client.surname +
    " " +
    data.client.given_name;
  isOrder.value = data.order_id;
}

function handleDefaultServiceData(serviceInfo) {
  const data = serviceInfo.lead;
  form.object_id = data.id;
  form.address = data.address ? data.address : data.client.address;
  form.type = "service";
  form.longitude = data.client.longitude;
  form.latitude = data.client.latitude;
  client.value =
    data.client.title +
    " " +
    data.client.surname +
    " " +
    data.client.given_name;
  isService.value = true;
  form.service_id = serviceInfo.id;
}

// data from leads detail
function handleDefaultLeadsData(data) {
  form.object_id = data.id;
  form.address = data.address ? data.address : data.client.address;
  form.type = "sale";
  form.longitude = data.client.longitude;
  form.latitude = data.client.latitude;
  client.value =
    data.client.title +
    " " +
    data.client.surname +
    " " +
    data.client.given_name;
  isOrder.value = null;
}

function resetForm(noResetList = []) {
  Object.keys(form).forEach(key => {
    if (!noResetList.includes(key)) {
      switch (key) {
        case "longitude":
        case "latitude":
          form[key] = 0;
          break;
        default:
          form[key] = null;
          break;
      }
    }
  });
  client.value = "";
  notifyStr.value = "";
  isService.value = false;
  isOrder.value = false;
}

function handleDateChange(value, type = "start") {
  const today = dayjs(new Date()).format("YYYY-MM-DD");
  const checkDay = dayjs(value).format("YYYY-MM-DD");
  startTime.value = "07:00";
  if (checkDay == today) {
    startTime.value = initStartTime();
  }
  if (type == "end" && form.date && form.endDate && form.date > form.endDate) {
    ElMessage.error("Please select the right end date!");
  }
}

function hide() {
  formRef.value.resetFields();
  dialogVisible.value = false;
}

function toUpdateAddress(addressInfo) {
  if (addressInfo) {
    form.address = addressInfo.address;
    form.longitude = addressInfo.longitude;
    form.latitude = addressInfo.latitude;
  }
}

function initStartTime() {
  const currentTime = new Date();
  const hours = currentTime.getHours();
  // const minutes = currentTime.getMinutes();

  const formattedHours = hours.toString().padStart(2, "0");
  // const formattedMinutes = minutes.toString().padStart(2, "0");

  let formattedTime = "07:00";
  if (formattedHours < "07" || formattedHours > "20") {
    return formattedTime;
  }
  formattedTime = formattedHours + ":" + "00";
  return formattedTime;
}

// when click address input open amap
function addressFocus() {
  googelMapRef.value.show(form).then(res => {
    console.log(res, "ditu");
  });
}

function getLeadsOptions(query = "") {
  getLeadsOptionsLoading.value = true;
  const search = query ? "%" + query + "%" : "";
  const filterInfo = [];
  filterInfo.push("result:neq:cancelled");
  const filter = filterInfo.join(",");
  const sort = "";
  const _with =
    "client,order,saleAppointment, appointment, serviceAppointment,service";
  const withCount = "";
  const page = 1;
  const size = 99;
  leadsControllerIndex(search, filter, sort, _with, withCount, page, size)
    .then(res => {
      const { data } = res;
      let leadsDataRes = data || [];
      if (data && actionFromType.value !== "appointment" && !form.id) {
        if (!hasAuth("addLeadsAppointment")) {
          leadsDataRes = leadsDataRes.filter(item => item.order_id);
        }

        if (!hasAuth("addServiceAppointment")) {
          leadsDataRes = leadsDataRes.filter(
            item => !(item.order && item.order.result == "completion")
          );
        }
        if (!hasAuth("addOrderAppointment")) {
          leadsDataRes = leadsDataRes.filter(
            item => !(item.order && item.order.result != "completion")
          );
        }

        // only result in ['new','followup','quoted','new_order','ready']
        const canCreateResultList = [
          "new",
          "followup",
          "quoted",
          "new_order",
          "ready"
        ];
        leadsDataRes = leadsDataRes.filter(item => {
          let checkType = item.result;
          if (item.order) {
            checkType = item.order.result;
            if (item.service) {
              checkType = item.service.result;
            }
          }
          return canCreateResultList.includes(checkType);
        });
      }
      leadsOptions.value = leadsDataRes;
      if (!leadsDataRes.length) {
        notifyStr.value = "Appointment creation requires a valid lead.";
      }
      getLeadsOptionsLoading.value = false;
    })
    .catch(_error => {
      getLeadsOptionsLoading.value = false;
    });
}
function parseTime(timeStr) {
  const [time, modifier] = timeStr.split(" ");
  let minutes = 0;
  let hours = 0;
  [hours, minutes] = time.split(":").map(Number);

  if (modifier === "PM" && hours !== 12) {
    hours += 12;
  } else if (modifier === "AM" && hours === 12) {
    hours += 0;
  }
  return new Date(0, 0, 0, hours, minutes, 0, 0);
}
async function onSave() {
  await formRef.value.validate(valid => {
    if (valid) {
      if (form.date > form.endDate) {
        ElMessage.error("Please select the right end date!");
        return;
      }
      const startDate = parseTime(form.time);
      const endDate = parseTime(form.end_time);
      if (form.date == form.endDate && startDate >= endDate) {
        ElMessage.error("Please select the right end time!");
        return;
      }
      loading.value = true;
      const sendForm = sendFormFilter(form);
      (actionFromType.value === "appointment" || form.id
        ? appointmentControllerUpdate(form.id, sendForm)
        : appointmentControllerStore(sendForm)
      )
        .then(res => {
          loading.value = false;
          if (res.success) {
            ElMessage.success(res.message);
          } else {
            ElMessage.error(res.message || "Create Appointment failed.");
          }
          hide();
          promise.resolve();
        })
        .catch(() => {
          loading.value = false;
          ElMessage.error("Create Appointment failed.");
        });
    }
  });
}

// Get the  time delayed by half an hour

function updateObjectId(value) {
  const noResetList = ["object_id"];
  resetForm(noResetList);
  const data = leadsOptions.value.find(item => item.id === value);
  if (data) {
    form.address = data.client.address;
    form.type = data.order_id ? null : "sale";
    form.longitude = data.client.longitude;
    form.latitude = data.client.latitude;
    client.value =
      data.client.title +
      " " +
      data.client.surname +
      " " +
      data.client.given_name;
    isOrder.value = data.order_id;
    notifyStr.value = "";
    if (!data.order_id && data.sale_appointment.length) {
      notifyStr.value = transformI18n("appointment.hadOutstandingAppNote");
      form.date = dayjs(data.sale_appointment[0].date).format(
        "YYYY-MM-DD 00:00"
      );
      form.time = dayjs(data.sale_appointment[0].date).format("hh:mm A");
      if (data.sale_appointment[0].end_time) {
        form.end_time = dayjs(data.sale_appointment[0].end_time).format(
          "hh:mm A"
        );
      } else {
        form.end_time = dayjs(data.sale_appointment[0].date)
          .add(0.5, "hour")
          .format("hh:mm A");
      }

      form.id = data.sale_appointment[0].id;
    } else if (data.order_id) {
      form.id = null;
      form.date = null;
      form.time = null;
      form.end_time = null;
      form.type = data.order.result == "new" ? "cm" : "install";
      if (data.appointment.length) {
        notifyStr.value = transformI18n("appointment.hadOutstandingAppNote");
        form.type = data.appointment[0].type;
        form.date = dayjs(data.appointment[0].date).format("YYYY-MM-DD 00:00");
        form.time = dayjs(data.appointment[0].date).format("hh:mm A");
        form.id = data.appointment[0].id;
        if (data.appointment[0].end_time) {
          form.end_time = dayjs(data.appointment[0].end_time).format("hh:mm A");
        } else {
          form.end_time = dayjs(data.appointment[0].date)
            .add(0.5, "hour")
            .format("hh:mm A");
        }
      }

      if (data.order.result == "completion") {
        form.id = null;
        form.date = null;
        form.time = null;
        form.end_time = null;
        form.type = "service";
        form.service_id = data.service.id;
        isService.value = true;
        if (data.serviceAppointment && data.serviceAppointment.length) {
          notifyStr.value = transformI18n("appointment.hadOutstandingAppNote");
          form.type = data.serviceAppointment[0].type;
          form.date = dayjs(data.serviceAppointment[0].date).format(
            "YYYY-MM-DD 00:00"
          );
          form.time = dayjs(data.serviceAppointment[0].date).format("hh:mm A");
          form.id = data.serviceAppointment[0].id;
          form.service_id = data.serviceAppointment[0].service_id;
          if (data.serviceAppointment[0].end_time) {
            form.end_time = dayjs(data.serviceAppointment[0].end_time).format(
              "hh:mm A"
            );
          } else {
            form.end_time = dayjs(data.serviceAppointment[0].date)
              .add(0.5, "hour")
              .format("hh:mm A");
          }
        }
      }
    }
  }
}

function typeChange(value) {
  if (form.id) {
    return;
  }
  form.type = value;
}
defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="
      form.id
        ? transformI18n('appointment.edit')
        : transformI18n('appointment.new')
    "
    width="38.75rem"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      class="w-[99/100] ml-7 mr-7"
    >
      <el-row>
        <el-col class="!mb-0 mt-5 text-sm text-[#2A2E34]" :span="24"
          >{{ transformI18n("appointment.objectId") }}
        </el-col>
        <el-col class="!mb-5">
          <el-form-item v-if="selectObject" prop="object_id">
            <el-select
              v-model="form.object_id"
              @change="updateObjectId"
              placeholder="Select"
              filterable
              :remote-method="getLeadsOptions"
              :loading="getLeadsOptionsLoading"
              remote
              clearable
            >
              <el-option
                v-for="item in leadsOptions"
                :key="item.id"
                :label="'#' + item.id"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="bg-[#F7F8F9]" v-else>
            #{{ form.object_id }}
          </el-form-item>
        </el-col>
        <el-col
          v-if="notifyStr"
          class="mb-0 !mt-[-5px] text-xs text-[#EA4335]"
          :span="24"
          >{{ notifyStr }}
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("client.client")
        }}</el-col>
        <el-col>
          <el-form-item class="bg-[#F7F8F9]">
            <span v-if="client">{{ client }}</span>
            <span class="empty" v-else> Empty</span>
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("client.address")
        }}</el-col>
        <el-col>
          <el-form-item prop="address" @click="addressFocus">
            <el-input
              v-model="form.address"
              :placeholder="transformI18n('leads.addressOfTheClient')"
              autocomplete="off"
              :readonly="true"
            >
              <template #append>
                <LocationIcon class="mr-1" />
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col v-if="isOrder" class="text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("appointment.type")
        }}</el-col>
        <el-col v-if="isService">
          <el-check-tag
            class="mr-5 !text-xs !font-medium !rounded-[20px] !leading-10 !pt-0 !pb-0"
            v-for="(typeItem, index) in serviceTypeList"
            :key="index"
            :checked="form.type == typeItem.type"
            @change="form.type = typeItem.type"
          >
            {{ typeItem.title }}
          </el-check-tag>
        </el-col>
        <el-col v-else-if="isOrder">
          <el-check-tag
            class="mr-5 !text-xs !font-medium !rounded-[20px] !leading-10 !pt-0 !pb-0"
            v-for="(typeItem, index) in typeList"
            :key="index"
            :checked="form.type == typeItem.type"
            @change="typeChange(typeItem.type)"
          >
            {{ typeItem.title }}
          </el-check-tag>
        </el-col>
        <el-col :span="12">
          <el-row>
            <el-col class="!mb-0 pr-2 text-sm text-[#2A2E34]" :span="24">
              Start Date
            </el-col>
            <el-col :span="24" class="pr-2">
              <el-form-item prop="date">
                <el-date-picker
                  v-model="form.date"
                  @change="handleDateChange"
                  type="date"
                  :picker-options="{
                    firstDayOfWeek: 1
                  }"
                  :placeholder="transformI18n('common.select')"
                  format="YYYY/MM/DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row>
            <el-col class="!mb-0 pl-2 text-sm text-[#2A2E34]" :span="24">
              Start Time
            </el-col>
            <el-col :span="24" class="pl-2">
              <el-form-item prop="time">
                <el-time-select
                  v-model="form.time"
                  :step="'00:15'"
                  :end="'20:00'"
                  :start="startTime"
                  format="hh:mm A"
                  :placeholder="transformI18n('common.select')"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row>
            <el-col class="!mb-0 pr-2 text-sm text-[#2A2E34]" :span="24">
              End Date
            </el-col>
            <el-col :span="24" class="pr-2">
              <el-form-item prop="date">
                <el-date-picker
                  v-model="form.endDate"
                  @change="handleDateChange($event, 'end')"
                  type="date"
                  :picker-options="{
                    firstDayOfWeek: 1
                  }"
                  :placeholder="transformI18n('common.select')"
                  format="YYYY/MM/DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="12">
          <el-row>
            <el-col class="!mb-0 pl-2 text-sm text-[#2A2E34]" :span="24">
              End Time
            </el-col>
            <el-col :span="24" class="pl-2">
              <el-form-item prop="end_time">
                <el-time-select
                  :clearable="false"
                  v-model="form.end_time"
                  :step="'00:15'"
                  :end="'20:30'"
                  :start="form.time || startTime"
                  format="hh:mm A"
                  :placeholder="transformI18n('common.select')"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">Notes</el-col>
        <el-col :span="24">
          <el-form-item prop="appt_duration" class="!mr-2">
            <el-input
              v-model="form.appt_duration"
              placeholder="Input"
              autocomplete="off"
              maxlength="25"
              style="width: 100%"
              type="text"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("common.comments")
        }}</el-col>
        <el-col :span="24">
          <el-form-item prop="comment" class="!mr-2">
            <el-input
              v-model="form.comment"
              type="textarea"
              :rows="3"
              :placeholder="transformI18n('common.changeComments')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide">{{
          transformI18n("buttons.hsCancel")
        }}</el-button>
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :disabled="loading"
          :loading="loading"
          @click="onSave"
          >{{
            form.id
              ? transformI18n("buttons.hssave")
              : transformI18n("common.create")
          }}</el-button
        >
      </span>
    </template>
    <GoogelMap ref="googelMapRef" @toUpdateAddress="toUpdateAddress" />
  </el-dialog>
</template>
