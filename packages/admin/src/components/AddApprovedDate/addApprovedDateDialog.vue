<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { sendFormFilter } from "@/utils/form";
import { ElMessage } from "element-plus";
import { orderControllerUpdate } from "@/api/admin/order";

const dialogVisible = ref(false);
const promise: any = {};
const btnLoading = ref(false);
const form = reactive({
  order_id: 0,
  approved_date: "",
  decision: null
});
const formRef = ref(null);
const rules = {
  approved_date: [
    {
      required: true,
      message: "Please enter the approved date",
      trigger: "change"
    }
  ]
};

function show(data) {
  if (!data.id) return;
  initFormData();
  form.order_id = data.id;
  form.decision = data.decision;
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function initFormData() {
  Object.keys(form).forEach(key => {
    form[key] = null;
  });
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  promise.resolve(actionOver);
}

function addApprovedDate() {
  formRef.value.validate(valid => {
    if (valid) {
      btnLoading.value = true;
      const sendForm = sendFormFilter(form);
      orderControllerUpdate(form.order_id, sendForm)
        .then(res => {
          btnLoading.value = false;
          if (res.success) {
            ElMessage.success(res.message);
            hide(true);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_err => {
          btnLoading.value = false;
        });
    }
  });
  return;
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'Update Approved Date'"
    align-center
    :width="'550px'"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="200"
      class="w-[99/100]"
      label-position="top border-form"
    >
      <div class="center-box">
        <el-row class="mt-[20px]">
          <el-col :span="24">
            <el-form-item prop="approved_date" label="Approved/Update Date">
              <el-date-picker
                v-model="form.approved_date"
                type="date"
                class="grow"
                :placeholder="transformI18n('common.select')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          class="w-52 !text-sm h-10 !bg-[#E9EBF0]"
          @click="hide(false)"
          >{{ transformI18n("buttons.hsCancel") }}</el-button
        >
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :disabled="btnLoading"
          :loading="btnLoading"
          @click="addApprovedDate"
          >{{ transformI18n("buttons.hssave") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.center-box {
  padding: 0 20px;
}
</style>
