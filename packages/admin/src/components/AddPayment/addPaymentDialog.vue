<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { orderControllerAddPayment } from "@/api/admin/order";
import { transformI18n } from "@/plugins/i18n";
import { sendFormFilter } from "@/utils/form";
import { ElMessage } from "element-plus";

const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const form = reactive({
  order_id: null,
  name: "",
  type: "Cash",
  amount: null,
  detail: "",
  received_date: "",
  payment_type: null,
  service_id: null
});
const formRef = ref(null);
const rules = {
  type: [
    { required: true, message: "Please select a type", trigger: "change" }
  ],
  amount: [
    { required: true, message: "Please enter the amount", trigger: "change" }
  ],
  detail: [
    { required: true, message: "Please enter the detail", trigger: "change" }
  ],
  received_date: [
    {
      required: true,
      message: "Please enter the received date",
      trigger: "change"
    }
  ]
};
const typeOption = [
  "Cash",
  "Cheque",
  "Credit",
  "CreditCard",
  "Direct Tsf",
  "Finn",
  "Finn Cost",
  "Refund",
  "Unpaid"
];
// function disabledDate(time) {
//   return time.getTime() < Date.now();
// }
function show(data, type = "order") {
  if (!data.id) return;
  initFormData();
  form.payment_type = type;
  form.order_id = data.id;
  if (data.service_id) {
    form.service_id = data.service_id;
  }
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function initFormData() {
  Object.keys(form).forEach(key => {
    switch (key) {
      case "type":
        form[key] = "Cash";
        break;
      default:
        form[key] = null;
        break;
    }
  });
}

function disabledDate(time) {
  return time.getTime() > Date.now();
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  promise.resolve(actionOver);
}

function addPayment() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      const sendForm = sendFormFilter(form);
      delete sendForm.order_id;
      orderControllerAddPayment(form.order_id, sendForm)
        .then(res => {
          loading.value = false;
          if (res.success) {
            ElMessage.success(res.message);
            hide(true);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_err => {
          loading.value = false;
        });
    }
  });
  return;
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'Add Payment'"
    align-center
    :width="'550px'"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="center-box">
        <el-row class="mt-[20px]">
          <el-col :span="24">
            <el-form-item prop="name" label="Name">
              <el-input
                v-model="form.name"
                size="small"
                :placeholder="transformI18n('common.input')"
                autocomplete="off"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px]"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="type" label="Type">
              <el-select
                v-model="form.type"
                :placeholder="transformI18n('common.select')"
                size="small"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] w-[100%]"
              >
                <el-option
                  v-for="item in typeOption"
                  :label="item"
                  :value="item"
                  :key="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="received_date" label="Date Received">
              <el-date-picker
                v-model="form.received_date"
                type="date"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] grow"
                :placeholder="transformI18n('common.select')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="amount" label="Amount Received">
              <el-input-number
                v-model="form.amount"
                :min="1"
                :precision="2"
                :placeholder="transformI18n('common.input')"
                controls-position="right"
                class="border border-[#E9EBF0] rounded-[6px] grow"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="detail" label="Receipt Details">
              <el-input
                v-model="form.detail"
                type="textarea"
                size="small"
                :rows="3"
                :placeholder="transformI18n('common.input')"
                autocomplete="off"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px]"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button
          class="w-52 !text-sm h-10 mb-1 !bg-[#E9EBF0]"
          @click="hide(false)"
          >{{ transformI18n("buttons.hsCancel") }}</el-button
        >
        <el-button
          class="w-52 !text-sm mb-1 h-10"
          type="primary"
          :disabled="loading"
          :loading="loading"
          @click="addPayment"
          >{{ transformI18n("buttons.hssave") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.result-icon {
  width: 50px;
  height: 50px;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
}

.warning {
  color: #ff9000;
}

.iconfont {
  font-size: 24px;
}
</style>
