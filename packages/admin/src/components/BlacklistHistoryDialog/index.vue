<script setup lang="ts">
import { ref, onMounted } from "vue";

const dialogVisible = ref(false);
const promise: any = {};
const title = ref("The blacklist of the client ");

const list = ref([]);

function show(data) {
  if (!data.blacklist_record.length) return;
  const res = [];
  data.blacklist_record.forEach(item => {
    res.push({
      lead_id: item.lead_id,
      type: item.type == "in" ? "Add" : "Remove",
      date: item.created_at,
      reason: item.reason,
      operator: item.created_by.name
    });
  });
  list.value = res;
  console.log(list);
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  promise.resolve(actionOver);
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog v-model="dialogVisible" :title="''" align-center :width="'550px'">
    <div class="center-box">
      <div class="mb-5 title level-1">{{ title }}</div>
      <el-table :data="list" style="width: 100%">
        <el-table-column prop="lead_id" label="Lead" />
        <el-table-column prop="type" label="Type" />
        <el-table-column prop="date" label="Date" />
        <el-table-column prop="reason" label="Reason" />
        <el-table-column prop="operator" label="Operator" />
      </el-table>
    </div>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}
</style>
