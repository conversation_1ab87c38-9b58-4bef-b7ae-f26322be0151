<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { roleControllerIndex } from "@/api/admin/admin-role";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();

const props = defineProps({
  value: Array,
  type: String,
  companyRegion: String
});

const emit = defineEmits(["select", "update:modelValue"]);
const roleSelector = ref(null);
const options = ref([]);
const modelValue = ref([]);
const loading = ref(false);

onMounted(() => {
  loadOptions();
});

function loadOptions() {
  options.value = [];
  modelValue.value = [];
  loading.value = true;
  let filter = "";
  if (props.type == "pad") {
    filter = "is_pad:eq:1";
  } else if (props.type == "pc") {
    filter = "is_pad:eq:0";
  }
  if (props.companyRegion && props.type == "pc") {
    if (props.companyRegion == "platform") {
      filter += (filter ? "," : "") + "is_platform:eq:1";
    } else {
      filter +=
        (filter ? "," : "") + "company_region:eq:" + props.companyRegion;
    }
  }
  roleControllerIndex("", filter).then(res => {
    if (Array.isArray(res.data)) {
      options.value = res.data.map(item => ({
        value: item.id,
        label:
          item.translation.en +
          (userBaseInfo.value.isPlatformUser && item.company_region
            ? "(" + item.company_region + ")"
            : "")
      }));
    }
    loading.value = false;
  });
}

watch(
  () => props.value,
  value => {
    console.log("watch", value);
    modelValue.value = value;
  },
  {
    immediate: true,
    deep: true
  }
);
watch(
  () => props.type,
  type => {
    if (type) {
      loadOptions();
    }
  }
);
watch(
  () => props.companyRegion,
  companyRegion => {
    if (companyRegion) {
      loadOptions();
    }
  }
);

function changeValue(value) {
  emit("update:modelValue", value);
  emit(
    "select",
    options.value.find(option => {
      console.log(option);
      return option.value === value;
    })
  );
}

defineExpose({});
</script>

<template>
  <el-select
    v-if="props.type == 'pc'"
    ref="roleSelector"
    :loading="loading"
    @change="changeValue"
    v-model="modelValue"
    multiple
    :multiple-limit="2"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
  <el-select
    v-else
    ref="roleSelector"
    :loading="loading"
    @change="changeValue"
    v-model="modelValue"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>
