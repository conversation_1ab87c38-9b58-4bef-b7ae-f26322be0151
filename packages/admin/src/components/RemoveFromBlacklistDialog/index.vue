<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { clientsControllerRemoveFromBlacklist } from "@/api/admin/clients";
import { sendFormFilter } from "@/utils/form";
import { ElMessage } from "element-plus";

const dialogVisible = ref(false);
const promise: any = {};
const title = ref("Do you want to remove the client of from blacklist?");
const content = ref(
  "After the client is removed from blacklist, the new draft leads of the client can be confirmed. Please confirm the operation."
);
const removeFromBlacklistLoading = ref(false);
const form = reactive({
  client_id: null,
  reason: null
});
const formRef = ref(null);
const rules = {
  reason: [
    { required: true, message: "Please enter the reason", trigger: "change" }
  ]
};

// function disabledDate(time) {
//   return time.getTime() < Date.now();
// }
function show(data) {
  if (!data.client_id) return;
  form.reason = null;
  form.client_id = data.client_id;
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  promise.resolve(actionOver);
}

function doRemove() {
  formRef.value.validate(valid => {
    if (valid) {
      removeFromBlacklistLoading.value = true;
      const sendForm = sendFormFilter(form);
      delete sendForm.client_id;
      clientsControllerRemoveFromBlacklist(form.client_id, sendForm)
        .then(res => {
          removeFromBlacklistLoading.value = false;
          if (res.success) {
            ElMessage.success(res.message);
            hide(true);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_err => {
          removeFromBlacklistLoading.value = false;
          ElMessage.error("Remove the client from blacklist fail.");
        });
    }
  });
  return;
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog v-model="dialogVisible" :title="''" align-center :width="'550px'">
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="center-box">
        <div class="title level-1">{{ title }}</div>
        <div class="content">
          {{ content }}
        </div>

        <el-form-item prop="reason" label="Reason" style="width: 80%">
          <el-input
            v-model="form.reason"
            type="textarea"
            :rows="3"
            :placeholder="transformI18n('common.input')"
            autocomplete="off"
            class="border border-[#E9EBF0] rounded-[6px] p-[5px] reason-item"
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide(false)"
          >Later</el-button
        >
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :loading="removeFromBlacklistLoading"
          @click="doRemove"
          >{{ transformI18n("leads.confirm") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.result-icon {
  width: 50px;
  height: 50px;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.reason-item {
  width: 100%;
}
</style>
