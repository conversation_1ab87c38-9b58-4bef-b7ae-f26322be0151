<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { orderControllerCreateService } from "@/api/admin/order";
import { transformI18n } from "@/plugins/i18n";
import { sendFormFilter } from "@/utils/form";
import { ElMessage } from "element-plus";

const dialogVisible = ref(false);
const promise: any = {};
const title = ref("Do you want to create a service for the order?");
const content = ref("Please enter the estimated amount.");
const createLoading = ref(false);
const form = reactive({
  order_id: 0,
  amount: 0
});
const formRef = ref(null);
const rules = {
  amount: [
    {
      required: true,
      message: "Please enter the amount for the service",
      trigger: "change"
    }
  ]
};

function show(data) {
  if (!data.order_id) return;
  initFormData();
  form.order_id = data.order_id;
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function initFormData() {
  Object.keys(form).forEach(key => {
    form[key] = null;
  });
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  promise.resolve(actionOver);
}

function doCreateService() {
  formRef.value.validate(valid => {
    if (valid) {
      createLoading.value = true;
      const sendForm = sendFormFilter(form);
      delete sendForm.order_id;
      orderControllerCreateService(form.order_id, sendForm)
        .then(res => {
          createLoading.value = false;
          if (res.success) {
            ElMessage.success(res.message);
            hide(true);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_err => {
          createLoading.value = false;
          ElMessage.error("Create service for this order fail.");
        });
    }
  });
  return;
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog v-model="dialogVisible" :title="''" align-center :width="'550px'">
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="center-box">
        <span class="mr-1 primary-color iconfont just-icon-new_order" />
        <div class="title level-1">{{ title }}</div>
        <div class="content">
          {{ content }}
        </div>
        <el-row class="mt-[20px] w-full">
          <el-col :span="24">
            <el-form-item prop="amount" label="Amount">
              <el-input-number
                v-model="form.amount"
                :min="0"
                :placeholder="transformI18n('common.input')"
                controls-position="right"
                class="border border-[#E9EBF0] rounded-[6px] grow"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide(false)"
          >Later</el-button
        >
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :loading="createLoading"
          @click="doCreateService"
          >{{ transformI18n("leads.confirm") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.iconfont {
  font-size: 24px;
}
</style>
