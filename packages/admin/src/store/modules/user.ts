import { defineStore } from "pinia";
import { store } from "@/store";
import { userType } from "./types";
import { routerArrays } from "@/layout/types";
import { router, resetRouter } from "@/router";
import { storageSession, storageLocal } from "@pureadmin/utils";
import { getAuthMe, getLogin, refreshTokenApi } from "@/api/user";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import {
  type DataInfo,
  setToken,
  removeToken,
  sessionKey,
  setUserInfo
} from "@/utils/auth";
import { AdminLoginResponse } from "@/model";

export const useUserStore = defineStore({
  id: "pure-user",
  state: (): userType => ({
    // 用户名
    username:
      storageSession().getItem<DataInfo<number>>(sessionKey)?.username ?? "",
    // 页面级别权限
    roles: storageSession().getItem<DataInfo<number>>(sessionKey)?.roles ?? [],
    permissions:
      storageSession().getItem<DataInfo<number>>(sessionKey)?.permissions ?? [],
    reportLook:
      storageSession().getItem<DataInfo<number>>(sessionKey)?.reportLook ?? [],
    userBaseInfo:
      storageSession().getItem<DataInfo<number>>(sessionKey)?.userBaseInfo ??
      [],
    // 是否勾选了登录页的免登录
    isRemembered: false
  }),
  actions: {
    /** 存储用户名 */
    SET_USERNAME(username: string) {
      this.username = username;
    },
    /** 存储角色 */
    SET_ROLES(roles: Array<string>) {
      this.roles = roles;
    },
    /** 存储是否勾选了登录页的免登录 */
    SET_ISREMEMBERED(bool: boolean) {
      this.isRemembered = bool;
    },
    /** 存储权限码 */
    SET_PERMISSIONS(permissions: Array<string>) {
      this.permissions = permissions;
    },
    SET_REPORTLOOK(reportLook: Array<string>) {
      this.reportLook = reportLook;
    },
    SET_USERBASEINFO(userBaseInfo: object) {
      this.userBaseInfo = userBaseInfo;
    },
    /** 登入 */
    async loginByUsername(data) {
      return new Promise<AdminLoginResponse>((resolve, reject) => {
        getLogin(data)
          .then(data => {
            if (data.success) {
              setToken(data);
              getAuthMe().then(me => {
                if (me) {
                  setUserInfo(data, me.data);
                  const isFirstLoginAfterReset =
                    me.data.isFirstLoginAfterReset || false;
                  resolve({ ...data, isFirstLoginAfterReset });
                }
              });
            } else {
              resolve(data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    /** 前端登出（不调用接口） */
    logOut() {
      //清空缓存信息（过滤条件）
      storageLocal().clear();
      this.username = "";
      this.roles = [];
      removeToken();
      useMultiTagsStoreHook().handleTags("equal", [...routerArrays]);
      resetRouter();
      router.push("/login");
    },
    /** 刷新`token` */
    async handRefreshToken() {
      console.log('=====1111222222');
      return new Promise<AdminLoginResponse>((resolve, reject) => {
        refreshTokenApi()
          .then(data => {
            console.log('=====111144444');
            if (data) {
              setToken(data);
              resolve(data);
            }
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
});

export function useUserStoreHook() {
  return useUserStore(store);
}
