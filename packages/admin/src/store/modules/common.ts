import { defineStore } from "pinia";
import { store } from "@/store";
import { storageSession } from "@pureadmin/utils";

export const useCommonStore = defineStore({
  id: "pure-common",
  state: (): any => ({
    basicData: storageSession().getItem("basicData") ?? {},
    productCategoryList: storageSession().getItem("productCategoryList") ?? [],
    subProductCategoryList:
      storageSession().getItem("subProductCategoryList") ?? {},
    sourceList: storageSession().getItem("sourceList") ?? [],
    subSourceList: storageSession().getItem("subSourceList") ?? {},
    promotionList: storageSession().getItem("promotionList") ?? {},
    costSource: storageSession().getItem("costSource") ?? {}
  }),
  getters: {
    getBasicData(state) {
      return state.basicData;
    },
    getAllHandleData(state) {
      let BColorList = [];
      if (
        state.basicData["BColorList"] &&
        state.basicData["BColorList"].length
      ) {
        BColorList = state.basicData["BColorList"].map(item => item.value);
      }
      const BMeshList = [];
      if (state.basicData["BMeshList"] && state.basicData["BMeshList"].length) {
        state.basicData["BMeshList"].map(item => {
          const children = [];
          if (item.sub_options) {
            item.sub_options.map(sub => {
              children.push({
                value: sub,
                label: sub
              });
            });
          }
          BMeshList.push({
            value: item.value,
            label: item.value,
            children: children
          });
        });
      }
      return {
        all: state.basicData,
        BColorList: BColorList,
        BMeshList: BMeshList,
        productCategoryList: state.productCategoryList,
        subProductCategoryList: state.subProductCategoryList,
        sourceList: state.sourceList,
        subSourceList: state.subSourceList,
        promotionList: state.promotionList,
        costSource: state.costSource
      };
    }
  },
  actions: {
    SET_SETBASICDATA(data: any) {
      this.basicData = data;
      storageSession().setItem("basicData", data);
      const productCategoryList = [];
      // const picUrlList = [
      //   "landscaping.png",
      //   "blinds.png",
      //   "roof.png",
      //   "garageDoor.png"
      // ];
      const subProductCategoryList = {};
      if (this.basicData && this.basicData.category) {
        const category = this.basicData.category;
        category.map(item => {
          productCategoryList.push(item);
          // productCategoryList.push({ ...item, picUrl: item.image });
          subProductCategoryList[item.id] = item.products;
        });
      }
      this.productCategoryList = productCategoryList;
      this.subProductCategoryList = subProductCategoryList;
      const sourceList = [];
      const subSourceList = {};
      if (this.basicData && this.basicData.source) {
        this.basicData.source.map(sourceItem => {
          sourceList.push({ id: sourceItem.id, name: sourceItem.name });
          subSourceList[sourceItem.id] = sourceItem.sub_source;
        });
      }
      this.sourceList = sourceList;
      this.subSourceList = subSourceList;
      const promotionList = [];
      if (this.basicData && this.basicData.promotion) {
        this.basicData.promotion.map(promotionItem => {
          promotionList.push({
            id: promotionItem.id,
            name: promotionItem.name
          });
        });
      }
      this.promotionList = promotionList;
      const costSource = [];
      if (this.basicData && this.basicData.costSource) {
        const sourceList = this.basicData.costSource;
        sourceList.map(item => {
          costSource.push(item);
        });
      }
      this.costSource = costSource;
      const basicDataSessionKeys = [
        "productCategoryList",
        "subProductCategoryList",
        "sourceList",
        "subSourceList",
        "promotionList",
        "costSource"
      ];
      basicDataSessionKeys.map(key => {
        storageSession().setItem(key, this[key]);
      });
    }
  }
});

export function useCommonStoreHook() {
  return useCommonStore(store);
}
