<script setup lang="ts">
import Logo from "./logo.vue";
import { useRoute } from "vue-router";
import { emitter } from "@/utils/mitt";
import SidebarItem from "./sidebarItem.vue";
import { useNav } from "@/layout/hooks/useNav";
import { responsiveStorageNameSpace } from "@/config";
import { storageLocal, isAllEmpty } from "@pureadmin/utils";
import { findRouteByPath, getParentPaths } from "@/router/utils";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { ref, computed, watch, onMounted, onBeforeUnmount } from "vue";
import MoreFilled from "@iconify-icons/ri/more-fill";
import { transformI18n } from "@/plugins/i18n";
import changePassword from "@/components/ChangePassword/ChangePassword.vue";
import Profile from "@/components/Profile/Profile.vue";
const route = useRoute();
const showLogo = ref(
  storageLocal().getItem<StorageConfigs>(
    `${responsiveStorageNameSpace()}configure`
  )?.showLogo ?? true
);
const changePasswordDialog = ref();
const {
  device,
  pureApp,
  isCollapse,
  menuSelect,
  logout,
  username,
  userAvatar
} = useNav();

const subMenuData = ref([]);
const ProfileDialogRef = ref(null);
const menuData = computed(() => {
  return pureApp.layout === "mix" && device.value !== "mobile"
    ? subMenuData.value
    : usePermissionStoreHook().wholeMenus;
});

const loading = computed(() =>
  pureApp.layout === "mix" ? false : menuData.value.length === 0 ? true : false
);

const defaultActive = computed(() =>
  !isAllEmpty(route.meta?.activePath) ? route.meta.activePath : route.path
);

function getSubMenuData() {
  let path = "";
  path = defaultActive.value;
  subMenuData.value = [];
  // path的上级路由组成的数组
  const parentPathArr = getParentPaths(
    path,
    usePermissionStoreHook().wholeMenus
  );
  // 当前路由的父级路由信息
  const parenetRoute = findRouteByPath(
    parentPathArr[0] || path,
    usePermissionStoreHook().wholeMenus
  );
  if (!parenetRoute?.children) return;
  subMenuData.value = parenetRoute?.children;
}

function changePwd() {
  changePasswordDialog.value.show();
}

function viewProfile() {
  ProfileDialogRef.value.show();
}
watch(
  () => [route.path, usePermissionStoreHook().wholeMenus],
  () => {
    if (route.path.includes("/redirect")) return;
    getSubMenuData();
    menuSelect(route.path);
  }
);

onMounted(() => {
  getSubMenuData();

  emitter.on("logoChange", key => {
    showLogo.value = key;
  });
});

onBeforeUnmount(() => {
  // 解绑`logoChange`公共事件，防止多次触发
  emitter.off("logoChange");
});
</script>

<template>
  <div
    v-loading="loading"
    :class="['sidebar-container', showLogo ? 'has-logo' : '']"
  >
    <Logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar
      wrap-class="scrollbar-wrapper"
      :class="[device === 'mobile' ? 'mobile' : 'pc']"
    >
      <el-menu
        router
        unique-opened
        mode="vertical"
        class="outer-most select-none"
        :collapse="isCollapse"
        :default-active="defaultActive"
        :collapse-transition="false"
      >
        <sidebar-item
          v-for="routes in menuData"
          :key="routes.path"
          :item="routes"
          :base-path="routes.path"
          class="outer-most select-none"
        />
      </el-menu>
    </el-scrollbar>
    <!-- <leftCollapse
      v-if="device !== 'mobile'"
      :is-active="pureApp.sidebar.opened"
      @toggleClick="toggleSideBar"
    /> -->
    <div class="user-info-item">
      <span class="flex items-center">
        <img :src="userAvatar" class="avatar-img" />
        <p v-if="username" class="dark:text-white text-xs pl-2.5">
          {{ username }}
        </p>
      </span>
      <el-dropdown trigger="click">
        <span class="arrow-down">
          <IconifyIconOffline :icon="MoreFilled" class="dark:text-white" />
        </span>
        <template #dropdown>
          <el-dropdown-menu class="logout">
            <el-dropdown-item @click="changePwd">
              {{ transformI18n("common.changePwd") }}
            </el-dropdown-item>
            <el-dropdown-item @click="viewProfile">
              {{ transformI18n("common.viewProfile") }}
            </el-dropdown-item>
            <el-dropdown-item @click="logout">
              {{ transformI18n("buttons.hsLoginOut") }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <changePassword ref="changePasswordDialog" />
    <Profile ref="ProfileDialogRef" />
  </div>
</template>

<style scoped>
:deep(.el-loading-mask) {
  opacity: 0.45;
}

.sidebar-container {
  background: #f7f8f9 !important;
}

.user-info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 18px;
  border-top: 1px solid #e9ebf0;
}

.user-info-item .avatar-img {
  width: 24px;
  height: 24px;
  border-radius: 12px;
}
</style>
