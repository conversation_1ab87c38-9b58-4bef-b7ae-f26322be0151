// Dashboard permission code
export enum DashboardPermission {
  viewDashboard = "viewDashBoard"
}

// appointment permission code
export enum AppointmentPermission {
  viewAnyAppointment = "viewAnyAppointment",
  editAppointment = "editAppointment",
  addAppointment = "addAppointment",
  cancelAppointment = "cancelAppointment",
  assignAppointment = "assignAppointment",
  revokeAppointment = "revokeAppointment",
  completeAppointment = "completeAppointment"
}

// leads permission code
export enum LeadsPermission {
  viewAnyLeads = "viewAnyLeads",
  addLeads = "addLeads",
  editManagement = "editManagement",
  confirmLeads = "confirmLeads",
  changeLeadsResult = "changeLeadsResult",
  followupLeads = "followupLeads",
  addLeadsAppointment = "addLeadsAppointment",
  cancelLeads = "cancelLeads",
  convertToOrder = "convertToOrder",
  delLeadsUploadFile = "delLeadsUploadFile",
  uploadFileForLeads = "uploadFileForLeads",
  moveToBlacklist = "moveToBlacklist",
  changeSource = "changeSource",
  revokeLeads = "revokeLeads",
  changeLeadsCompany = "changeLeadsCompany"
}

// orders permission code
export enum OrdersPermission {
  viewAnyOrders = "viewAnyOrders",
  editOrderManagement = "editOrderManagement",
  viewPayment = "viewPayment",
  updatePayment = "updatePayment",
  orderSpec = "orderSpec",
  uploadFileForOrder = "uploadFileForOrder",
  delOrderUploadFile = "delOrderUploadFile",
  changeOrderResult = "changeOrderResult",
  addOrderAppointment = "addOrderAppointment",
  createService = "createService",
  cancelOrder = "cancelOrder",
  canRevokeOrder = "canRevokeOrder",
  addSupplierForOrder = "addSupplierForOrder",
  removeSupplierForOrder = "removeSupplierForOrder"
}

export enum ServicePermission {
  viewAnyService = "viewAnyService",
  editServiceManagement = "editServiceManagement",
  // addServicePayment = "addServicePayment",
  viewServicePayment = "viewServicePayment",
  updateServicePayment = "updateServicePayment",
  serviceSpec = "serviceSpec",
  uploadFileForService = "uploadFileForService",
  delServiceUploadFile = "delServiceUploadFile",
  changeServiceResult = "changeServiceResult",
  addServiceAppointment = "addServiceAppointment",
  cancelService = "cancelService",
  addSupplierForService = "addSupplierForService",
  removeSupplierForService = "removeSupplierForService"
}

// client permission code
export enum ClientsPermission {
  viewAnyClient = "viewAnyClient",
  editClient = "editClient",
  delClient = "delClient",
  addClient = "addClient",
  removeFromBlacklist = "removeFromBlacklist"
}
// base data permission code
export enum BasicDataPermission {
  viewAnySource = "viewAnySource",
  viewAnyPromotion = "viewAnyPromotion",
  viewAnySupplier = "viewAnySupplier",
  addSupplier = "addSupplier",
  editSupplier = "editSupplier",
  delSupplier = "delSupplier",
  viewAnyCost = "viewAnyCost",
  viewAnyTarget = "viewAnyTarget"
}

// product category permission code
export enum ProductPermission {
  viewAnyProduct = "viewAnyProduct"
}

// role permission code
export enum RolePermission {
  viewAnyRole = "viewAnyRole",
  addRole = "addRole",
  editRole = "editRole",
  deleteRole = "delRole"
}

// Account permission code
export enum AccountPermission {
  viewAnyAccount = "viewAnyAccount",
  addAccount = "addAccount",
  editAccount = "editAccount",
  delAccount = "delAccount",
  resetPassword = "resetPassword"
}

export enum ReportPermission {
  viewAnyReportManager = "viewAnyReportManager",
  addReportManager = "addReportManager",
  editReportManager = "editReportManager",
  delReportManager = "delReportManager"
}
