import {
  AdminNewsSection<PERSON><PERSON>,
  <PERSON>minAuthApi,
  Configuration,
  AdminUploadA<PERSON>,
  AdminAccountApi,
  AdminRoleApi,
  AdminPermissionApi,
  AdminClientsApi,
  AdminProductCategoryApi,
  AdminPromotionApi,
  AdminSourceApi,
  AdminDraftLeadsApi,
  AdminCommonApi,
  AdminSupplierApi,
  AdminLeadsApi,
  AdminNotesApi,
  AdminAppointmentApi,
  AdminOrderApi,
  AdminServiceApi,
  AdminDashboardApi,
  AdminGoogleApi,
  AdminImportTasksApi,
  AdminReportApi,
  AdminCostApi,
  AdminTargetApi
} from "@/model";

const basePath =
  process.env.NODE_ENV === "development" ? "http://justcrm.local/api" : "/api";

const configuration = new Configuration({
  basePath
});

export const adminAuthApi = new AdminAuthApi(configuration);
export const adminNewsSectionApi = new AdminNewsSectionApi(configuration);
export const adminUploadApi = new AdminUploadApi(configuration);
export const adminAccountApi = new AdminAccountApi(configuration);
export const adminRoleApi = new AdminRoleApi(configuration);
export const adminPermissionApi = new AdminPermissionApi(configuration);
export const adminClientsApi = new AdminClientsApi(configuration);
export const adminProductCategoryApi = new AdminProductCategoryApi(
  configuration
);
export const adminPromotionApi = new AdminPromotionApi(configuration);
export const adminSourceApi = new AdminSourceApi(configuration);
export const adminDraftLeadsApi = new AdminDraftLeadsApi(configuration);
export const adminCommonApi = new AdminCommonApi(configuration);
export const adminSupplierApi = new AdminSupplierApi(configuration);
export const adminLeadsApi = new AdminLeadsApi(configuration);
export const adminNotesApiFp = new AdminNotesApi(configuration);
export const adminAppointmentApi = new AdminAppointmentApi(configuration);
export const adminOrderApi = new AdminOrderApi(configuration);
export const adminServiceApi = new AdminServiceApi(configuration);
export const adminDashboardApi = new AdminDashboardApi(configuration);
export const adminGoogleApi = new AdminGoogleApi(configuration);
export const adminReportApi = new AdminReportApi(configuration);
export const adminImportTasksApi = new AdminImportTasksApi(configuration);
export const adminCostApi = new AdminCostApi(configuration);
export const adminTargetApi = new AdminTargetApi(configuration);
