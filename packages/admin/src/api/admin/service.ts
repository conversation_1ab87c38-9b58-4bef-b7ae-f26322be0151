import { adminServiceApi } from "..";
import {
  AdminService,
  AdminServiceChangeResultRequestSchema,
  AdminServiceSaveSpecRequestSchema,
  AdminServiceGeneratorPDFRequestSchema
} from "@/model";
import { AxiosRequestConfig } from "axios";

/**
 *
 * @param search
 * @param filter
 * @param sort
 * @param _with
 * @param withCount
 * @param page
 * @param size
 * @param options
 * @returns
 */
export const serviceControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  subCategoryIds?: string,
  options?: AxiosRequestConfig
) => {
  return adminServiceApi.serviceControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    subCategoryIds,
    options
  );
};

/***
 * @param id
 * @param _with
 * @param options
 * @returns
 */
export const serviceControllerShow = (
  id: number,
  _with?: Array<string>,
  options?: AxiosRequestConfig
) => {
  return adminServiceApi.serviceControllerShow(id, _with, options);
};

export const serviceControllerChangeResult = (
  id: number,
  adminServiceChangeResultRequestSchema: AdminServiceChangeResultRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminServiceApi.serviceControllerChangeResult(
    id,
    adminServiceChangeResultRequestSchema,
    options
  );
};

/**
 *
 * @param id
 * @param adminService
 * @param options
 * @returns
 */
export const serviceControllerUpdate = (
  id: number,
  adminService: AdminService,
  options?: AxiosRequestConfig
) => {
  return adminServiceApi.serviceControllerUpdate(id, adminService, options);
};

export const serviceControllerGetSummaryInfo = (
  options?: AxiosRequestConfig
) => {
  return adminServiceApi.serviceControllerGetSummaryInfo(options);
};

export const serviceControllerSaveSpec = (
  adminServiceSaveSpecRequestSchema: AdminServiceSaveSpecRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminServiceApi.serviceControllerSaveSpec(
    adminServiceSaveSpecRequestSchema,
    options
  );
};

export const serviceControllerGeneratorPDF = (
  id: number,
  adminServiceGeneratorPDFRequestSchema: AdminServiceGeneratorPDFRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminServiceApi.serviceControllerGeneratorPDF(
    id,
    adminServiceGeneratorPDFRequestSchema,
    options
  );
};
