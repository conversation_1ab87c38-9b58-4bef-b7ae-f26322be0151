import { adminReportApi } from "..";
import { AxiosRequestConfig } from "axios";
import { AdminReport } from "@/model";

export const reportControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminReportApi.reportControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};

export const reportControllerStore = (
  adminReport: AdminReport,
  options?: AxiosRequestConfig
) => {
  return adminReportApi.reportControllerStore(adminReport, options);
};

export const reportControllerUpdate = (
  id: number,
  adminReport: AdminReport,
  options?: AxiosRequestConfig
) => {
  return adminReportApi.reportControllerUpdate(id, adminReport, options);
};

export const reportControllerDestroy = (
  id: number,
  options?: AxiosRequestConfig
) => {
  return adminReportApi.reportControllerDestroy(id, options);
};
