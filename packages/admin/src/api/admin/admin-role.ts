import { adminRoleApi, adminPermission<PERSON>pi } from "..";
import { AxiosRequestConfig } from "axios";
import { AdminRole } from "@/model";

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const roleControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminRoleApi.roleControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};

/**
 *  @return
 * @param adminRole
 * */
export const roleControllerStore = (adminRole: AdminRole) => {
  return adminRoleApi.roleControllerStore(adminRole);
};

/**
 * @param id
 * @param adminRole
 *  @return
 * */
export const roleControllerUpdate = (id: number, adminRole: AdminRole) => {
  return adminRoleApi.roleControllerUpdate(id, adminRole);
};

/**
 * @param id
 *  @return
 * */
export const roleControllerDestroy = (id: number) => {
  return adminRoleApi.roleControllerDestroy(id);
};

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const permissionControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminPermissionApi.permissionControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};
