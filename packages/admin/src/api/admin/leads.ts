import { adminDraftLeadsApi, adminLeads<PERSON>pi } from "..";
import { AxiosRequestConfig } from "axios";
import {
  AdminDraftLeads,
  AdminLeads,
  AdminLeadsChangeResultRequestSchema,
  AdminLeadsChangeSalesConsultantRequestSchema,
  AdminLeadsConvertRequestSchema,
  AdminLeadsMoveToBlacklistRequestSchema,
  AdminLeadsRevokedLeadsRequestSchema
} from "@/model";

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const draftLeadsControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminDraftLeadsApi.draftLeadsControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};

/**
 *  @return
 * @param adminDraftLeads
 * */
export const draftLeadsControllerStore = (adminDraftLeads: AdminDraftLeads) => {
  return adminDraftLeadsApi.draftLeadsControllerStore(adminDraftLeads);
};

/**
 *  @return
 * @param adminDraftLeads
 * */
export const draftLeadsControllerSetspoken = (id: number) => {
  return adminDraftLeadsApi.draftLeadsControllerSetspoken(id);
};

/**
 *  @return
 * @param adminDraftLeads
 * */
export const draftLeadsControllerConfirm = (
  id: number,
  adminDraftLeads: AdminDraftLeads
) => {
  return adminDraftLeadsApi.draftLeadsControllerConfirm(id, adminDraftLeads);
};

/**
 *  @return
 * @param adminDraftLeads
 * */
export const draftLeadsControllerClose = (id: number) => {
  return adminDraftLeadsApi.draftLeadsControllerCancel(id);
};

/**
 * @param id
 * @param adminDraftLeads
 *  @return
 * */
export const draftLeadsControllerUpdate = (
  id: number,
  adminDraftLeads: AdminDraftLeads
) => {
  return adminDraftLeadsApi.draftLeadsControllerUpdate(id, adminDraftLeads);
};

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const leadsControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  otherParam?: string,
  options?: AxiosRequestConfig
) => {
  return adminLeadsApi.leadsControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    otherParam,
    options
  );
};

/**
 * @param id
 * @param adminLeads
 *  @return
 * */
export const leadsControllerUpdate = (id: number, adminLeads: AdminLeads) => {
  return adminLeadsApi.leadsControllerUpdate(id, adminLeads);
};

/**
 * @param id
 * @param adminLeads
 *  @return
 * */
export const leadsControllerChangeResult = (
  id: number,
  adminLeadsChangeResultRequestSchema: AdminLeadsChangeResultRequestSchema
) => {
  return adminLeadsApi.leadsControllerChangeResult(
    id,
    adminLeadsChangeResultRequestSchema
  );
};

/**
 * @param id
 * @param _with?
 * @param options?
 * return
 */
export const leadsControllerShow = (
  id: number,
  _with?: Array<string>,
  options?: AxiosRequestConfig
) => {
  return adminLeadsApi.leadsControllerShow(id, _with, options);
};

/**
 * @param id,
 * @param adminLeadsConvertRequestSchema,
 * @param option
 * return
 */
export const leadsControllerConvert = (
  id: number,
  adminLeadsConvertRequestSchema: AdminLeadsConvertRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminLeadsApi.leadsControllerConvert(
    id,
    adminLeadsConvertRequestSchema,
    options
  );
};

export const leadsControllerGetSummaryInfo = (
  companyRegion: string,
  options?: AxiosRequestConfig
) => {
  return adminLeadsApi.leadsControllerGetSummaryInfo(companyRegion, options);
};

/**
 * @param id
 * @param adminLeads
 *  @return
 * */
export const leadsControllerChangeSalesConsultant = (
  id: number,
  adminLeadsChangeSalesConsultantRequestSchema: AdminLeadsChangeSalesConsultantRequestSchema
) => {
  return adminLeadsApi.leadsControllerChangeSalesConsultant(
    id,
    adminLeadsChangeSalesConsultantRequestSchema
  );
};

/**
 * @param id
 * @param adminLeads
 *  @return
 * */
export const leadsControllerMoveToBlacklist = (
  id: number,
  adminLeadsMoveToBlacklistRequestSchema: AdminLeadsMoveToBlacklistRequestSchema
) => {
  return adminLeadsApi.leadsControllerMoveToBlacklist(
    id,
    adminLeadsMoveToBlacklistRequestSchema
  );
};

/**
 * @param id
 * @param adminLeads
 *  @return
 * */
export const leadsControllerRevokeLeads = (
  id: number,
  AdminLeadsRevokedLeadsRequestSchema: AdminLeadsRevokedLeadsRequestSchema
) => {
  return adminLeadsApi.leadsControllerRevokedLeads(
    id,
    AdminLeadsRevokedLeadsRequestSchema
  );
};
