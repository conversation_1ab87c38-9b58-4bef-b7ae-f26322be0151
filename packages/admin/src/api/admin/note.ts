import { adminNotesApiFp } from "..";
import { AxiosRequestConfig } from "axios";
import { AdminNotes } from "@/model";

/**
 * @param type
 * @param id
 * @param filter?
 * @param _with?
 * @param options?
 */
export const notesControllerList = (
  type: string,
  id: number,
  filter?: string,
  _with?: string,
  sort?: string,
  options?: AxiosRequestConfig
) => {
  return adminNotesApiFp.notesControllerList(
    type,
    id,
    filter,
    _with,
    sort,
    options
  );
};

export const notesControllerStore = (adminNotes: AdminNotes, options?) => {
  return adminNotesApiFp.notesControllerStore(adminNotes, options);
};
