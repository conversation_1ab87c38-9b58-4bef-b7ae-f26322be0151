import { adminAppointment<PERSON><PERSON> } from "..";
import { AxiosRequestConfig } from "axios";
import {
  AdminAppointment,
  AdminAppointmentCancelRequestSchema,
  AdminAppointmentCompleteRequestSchema,
  AdminAppointmentSendToGoogleCalendarRequestSchema,
  AdminAppointmentNotifyAssignRequestSchema
} from "@/model";

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param subCategoryIds?
 * @param options?
 */
export const appointmentControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  subCategoryIds?: string,
  options?: AxiosRequestConfig
) => {
  return adminAppointmentApi.appointmentControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    subCategoryIds,
    options
  );
};

/**
 * @param id
 * @param AdminAppointment
 *  @return
 * */
export const appointmentControllerUpdate = (
  id: number,
  adminAppointment: AdminAppointment
) => {
  return adminAppointmentApi.appointmentControllerUpdate(id, adminAppointment);
};

/**
 * @param AdminAppointment
 *  @return
 * */
export const appointmentControllerStore = (
  adminAppointment: AdminAppointment
) => {
  return adminAppointmentApi.appointmentControllerStore(adminAppointment);
};

/**
 *
 * @param id
 * @param adminAppointment
 * @returns
 */
export const appointmentControllerAssign = (
  id: number,
  adminAppointment: AdminAppointment
) => {
  return adminAppointmentApi.appointmentControllerAssign(id, adminAppointment);
};

/**
 *
 * @param id
 * @param body
 * @param options
 * @returns
 */
export const appointmentControllerNotifyClient = (
  id: number,
  body: object,
  options?: AxiosRequestConfig
) => {
  return adminAppointmentApi.appointmentControllerNotifyClient(
    id,
    body,
    options
  );
};

/**
 * @param id
 * @param adminAppointmentCancelRequestSchema
 * @param options
 * @returns
 */
export const appointmentControllerCancel = (
  id: number,
  adminAppointmentCancelRequestSchema: AdminAppointmentCancelRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminAppointmentApi.appointmentControllerCancel(
    id,
    adminAppointmentCancelRequestSchema,
    options
  );
};

/**
 * @param id
 * @param adminAppointmentCancelRequestSchema
 * @param options
 * @returns
 */
export const appointmentControllerComplete = (
  id: number,
  adminAppointmentCompleteRequestSchema: AdminAppointmentCompleteRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminAppointmentApi.appointmentControllerComplete(
    id,
    adminAppointmentCompleteRequestSchema,
    options
  );
};

export const appointmentControllerSendToGoogleCalendar = (
  adminAppointmentSendToGoogleCalendarRequestSchema: AdminAppointmentSendToGoogleCalendarRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminAppointmentApi.appointmentControllerSendToGoogleCalendar(
    adminAppointmentSendToGoogleCalendarRequestSchema,
    options
  );
};

export const appointmentControllerNotifyAssign = (
  id: number,
  adminAppointmentNotifyAssignRequestSchema: AdminAppointmentNotifyAssignRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminAppointmentApi.appointmentControllerNotifyAssign(
    id,
    adminAppointmentNotifyAssignRequestSchema,
    options
  );
};

export const appointmentControllerConfirm = (
  id: number,
  options?: AxiosRequestConfig
) => {
  return adminAppointmentApi.appointmentControllerConfirm(id, options);
};

export const appointmentControllerAssignAccept = (
  id: number,
  options?: AxiosRequestConfig
) => {
  return adminAppointmentApi.appointmentControllerAssignAccept(id, options);
};

/**
 *
 * @param id
 * @param options
 * @returns
 */
export const appointmentControllerRevokeCancel = (
  id: number,
  options?: AxiosRequestConfig
) => {
  return adminAppointmentApi.appointmentControllerRevokeCancel(id, options);
};

/**
 *
 * @param id
 * @param options
 * @returns
 */
export const appointmentControllerSyncCalendar = (
  id: number,
  options?: AxiosRequestConfig
) => {
  return adminAppointmentApi.appointmentControllerSyncCalendar(id, options);
};
