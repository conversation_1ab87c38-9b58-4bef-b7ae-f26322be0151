import { admin<PERSON><PERSON><PERSON><PERSON><PERSON> } from "..";
import {
  Ad<PERSON><PERSON><PERSON><PERSON>,
  AdminOrderAddPaymentRequestSchema,
  AdminOrderSaveSpecRequestSchema,
  AdminOrderChangeResultRequestSchema,
  AdminOrderAddSupplierRequestSchema,
  AdminOrderRemoveSupplierRequestSchema,
  AdminOrderCreateServiceRequestSchema,
  AdminOrderGeneratorPDFRequestSchema,
  AdminOrderDelPaymentRequestSchema,
  AdminOrderGeneratorSatisfactionPDFRequestSchema,
  AdminOrderGeneratorWarrantyCardPDFRequestSchema,
  AdminOrderGeneratorReportRequestSchema,
  AdminOrderEmailToClientRequestSchema
} from "@/model";
import { AxiosRequestConfig } from "axios";

/**
 *
 * @param search
 * @param filter
 * @param sort
 * @param _with
 * @param withCount
 * @param page
 * @param size
 * @param options
 * @returns
 */
export const orderControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  subCategoryIds?: string,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    subCategoryIds,
    options
  );
};

/**
 *
 * @param id
 * @param adminOrder
 * @param options
 * @returns
 */
export const orderControllerUpdate = (
  id: number,
  adminOrder: AdminOrder,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerUpdate(id, adminOrder, options);
};

/**
 *
 * @param id
 * @param adminOrderAddPaymentRequestSchema
 * @param options
 * @returns
 */
export const orderControllerAddPayment = (
  id: number,
  adminOrderAddPaymentRequestSchema: AdminOrderAddPaymentRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerAddPayment(
    id,
    adminOrderAddPaymentRequestSchema,
    options
  );
};

export const orderControllerDelPayment = (
  id: number,
  adminOrderDelPaymentRequestSchema: AdminOrderDelPaymentRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerDelPayment(
    id,
    adminOrderDelPaymentRequestSchema,
    options
  );
};

/***
 * @param id
 * @param _with
 * @param options
 * @returns
 */
export const orderControllerShow = (
  id: number,
  _with?: Array<string>,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerShow(id, _with, options);
};

/**
 *
 * @param adminOrderSaveSpecRequestSchema
 * @param options
 * @returns
 */
export const orderControllerSaveSpec = (
  adminOrderSaveSpecRequestSchema: AdminOrderSaveSpecRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerSaveSpec(
    adminOrderSaveSpecRequestSchema,
    options
  );
};

export const orderControllerChangeResult = (
  id: number,
  adminOrderChangeResultRequestSchema: AdminOrderChangeResultRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerChangeResult(
    id,
    adminOrderChangeResultRequestSchema,
    options
  );
};

export const orderControllerGetSummaryInfo = (
  companyRegion: string,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerGetSummaryInfo(companyRegion, options);
};

/**
 *
 * @param id
 * @param adminOrderAddSupplierRequestSchema
 * @param options
 * @returns
 */
export const orderControllerAddSupplier = (
  id: number,
  adminOrderAddSupplierRequestSchema: AdminOrderAddSupplierRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerAddSupplier(
    id,
    adminOrderAddSupplierRequestSchema,
    options
  );
};

/**
 *
 * @param id
 * @param adminOrderRemoveSupplierRequestSchema
 * @param options
 * @returns
 */
export const orderControllerRemoveSupplier = (
  id: number,
  adminOrderRemoveSupplierRequestSchema: AdminOrderRemoveSupplierRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerRemoveSupplier(
    id,
    adminOrderRemoveSupplierRequestSchema,
    options
  );
};

export const orderControllerCreateService = (
  id: number,
  adminOrderCreateServiceRequestSchema: AdminOrderCreateServiceRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerCreateService(
    id,
    adminOrderCreateServiceRequestSchema,
    options
  );
};

export const orderControllerGeneratorPDF = (
  id: number,
  adminOrderGeneratorPDFRequestSchema: AdminOrderGeneratorPDFRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerGeneratorPDF(
    id,
    adminOrderGeneratorPDFRequestSchema,
    options
  );
};

export const orderControllerGeneratorSatisfactionPDF = (
  id: number,
  adminOrderGeneratorSatisfactionPDFRequestSchema: AdminOrderGeneratorSatisfactionPDFRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerGeneratorSatisfactionPDF(
    id,
    adminOrderGeneratorSatisfactionPDFRequestSchema,
    options
  );
};

export const orderControllerGeneratorWarrantyCardPDF = (
  id: number,
  adminOrderGeneratorWarrantyCardPDFRequestSchema: AdminOrderGeneratorWarrantyCardPDFRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerGeneratorWarrantyCardPDF(
    id,
    adminOrderGeneratorWarrantyCardPDFRequestSchema,
    options
  );
};

export const orderControllerGeneratorReportPDF = (
  adminOrderGeneratorReportRequestSchema: AdminOrderGeneratorReportRequestSchema,
  companyRegion: string,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerGeneratorReport(
    adminOrderGeneratorReportRequestSchema,
    companyRegion,
    options
  );
};

/**
 *
 * @param id
 * @param options
 * @returns
 */
export const orderControllerRevokeOrder = (
  id: number,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerRevokeOrder(id, options);
};

/**
 *
 * @param id
 * @param options
 * @returns
 */
export const orderControllerEmailToClient = (
  id: number,
  adminOrderEmailToClientRequestSchema: AdminOrderEmailToClientRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminOrderApi.orderControllerEmailToClient(
    id,
    adminOrderEmailToClientRequestSchema,
    options
  );
};
