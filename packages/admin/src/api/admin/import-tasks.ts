import { adminImportTasksApi } from "..";
import { AxiosRequestConfig } from "axios";

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const importTasksControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminImportTasksApi.importTasksControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};

/**
 * @param id
 *  @return
 * */
export const importTasksControllerDestroy = (id: number) => {
  return adminImportTasksApi.importTasksControllerDestroy(id);
};
