import { adminProductCategoryApi } from "..";
import { AxiosRequestConfig } from "axios";

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const productCategoryControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminProductCategoryApi.productCategoryControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};

export const productCategoryControllerExportBcolor = (
  type: string,
  options?: AxiosRequestConfig
) => {
  return adminProductCategoryApi.productCategoryControllerExportBcolor(
    type,
    options
  );
};

export const productCategoryControllerImportBcolorFromExcel = (
  adminProductCategoryImportBcolorFromExcelRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminProductCategoryApi.productCategoryControllerImportBcolorFromExcel(
    adminProductCategoryImportBcolorFromExcelRequestSchema,
    options
  );
};

export const productCategoryControllerDataSynchronizationFromOld = (
  adminProductCategoryDataSynchronizationFromOldRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminProductCategoryApi.productCategoryControllerDataSynchronizationFromOld(
    adminProductCategoryDataSynchronizationFromOldRequestSchema,
    options
  );
};

export const productCategoryControllerDataSynchronizationToOld = (
  type: string,
  options?: AxiosRequestConfig
) => {
  return adminProductCategoryApi.productCategoryControllerDataSynchronizationToOld(
    type,
    options
  );
};
