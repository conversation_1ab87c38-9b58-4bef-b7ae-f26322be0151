import { adminAccount<PERSON><PERSON> } from "..";
import { AxiosRequestConfig } from "axios";
import { AdminAccount, AdminAccountChangePasswordRequestSchema } from "@/model";

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const accountControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  isList?: string,
  companyRegionStr?: string,
  options?: AxiosRequestConfig
) => {
  return adminAccountApi.accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    isList,
    companyRegionStr,
    options
  );
};

/**
 *  @return
 * @param adminAccount
 * */
export const accountControllerStore = (adminAccount: AdminAccount) => {
  return adminAccountApi.accountControllerStore(adminAccount);
};

/**
 * @param id
 * @param adminAccount
 *  @return
 * */
export const accountControllerUpdate = (
  id: number,
  adminAccount: AdminAccount
) => {
  return adminAccountApi.accountControllerUpdate(id, adminAccount);
};

/**
 * @param id
 *  @return
 * */
export const accountControllerDestroy = (id: number) => {
  return adminAccountApi.accountControllerDestroy(id);
};

/**
 * @param adminAccountChangePasswordRequestSchema
 *  @return
 * */
export const accountControllerChangePassword = (
  adminAccountChangePasswordRequestSchema: AdminAccountChangePasswordRequestSchema
) => {
  return adminAccountApi.accountControllerChangePassword(
    adminAccountChangePasswordRequestSchema
  );
};

/**
 * @param id
 *  @return
 * */
export const accountControllerResetPassword = (id: number) => {
  return adminAccountApi.accountControllerResetPassword(id);
};

/**
 *
 * @param email
 * @param options
 * @returns
 */
export const accountControllerResetPasswordByEmail = (
  email?: string,
  options?: AxiosRequestConfig
) => {
  return adminAccountApi.accountControllerResetPasswordByEmail(email, options);
};
