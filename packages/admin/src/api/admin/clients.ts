import { adminClientsApi } from "..";
import { AxiosRequestConfig } from "axios";
import { AdminClients } from "@/model";
import { AdminClientsRemoveFromBlacklistRequestSchema } from "@/model";
/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const clientsControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminClientsApi.clientsControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};

/**
 * @param id
 * @param AdminClients
 *  @return
 * */
export const clientControllerUpdate = (
  id: number,
  adminClients: AdminClients
) => {
  return adminClientsApi.clientsControllerUpdate(id, adminClients);
};

/**
 * @param AdminClients
 *  @return
 * */
export const clientControllerStore = (adminClients: AdminClients) => {
  return adminClientsApi.clientsControllerStore(adminClients);
};

/**
 * @param id
 *  @return
 * */
export const clientControllerDestroy = (id: number) => {
  return adminClientsApi.clientsControllerDestroy(id);
};

/**
 * @param id
 * @param adminLeads
 *  @return
 * */
export const clientsControllerRemoveFromBlacklist = (
  id: number,
  adminClientsRemoveFromBlacklistRequestSchema: AdminClientsRemoveFromBlacklistRequestSchema
) => {
  return adminClientsApi.clientsControllerRemoveFromBlacklist(
    id,
    adminClientsRemoveFromBlacklistRequestSchema
  );
};
