import { adminDashboard<PERSON><PERSON> } from "..";
import { AxiosRequestConfig } from "axios";

/**
 * @param type
 * @param id
 * @param filter?
 * @param _with?
 * @param options?
 */
export const dashboardControllerSummary = (
  timeRangeType?: string,
  productCategoryId?: string,
  start?: string,
  end?: string,
  componyRegion?: string,
  options?: AxiosRequestConfig
) => {
  return adminDashboardApi.dashboardControllerSummary(
    timeRangeType,
    productCategoryId,
    start,
    end,
    componyRegion,
    options
  );
};

/**
 * Group leads by  time range(Suspend use)
 * @param timeRangeType?
 * @param options?
 */
export const dashboardControllerGetNewLeadStatistic = (
  timeRangeType?: string,
  options?: AxiosRequestConfig
) => {
  return adminDashboardApi.dashboardControllerGetNewLeadStatistic(
    timeRangeType,
    options
  );
};
/**
 * @param options?
 */
export const dashboardControllerGetAppointmentStatistic = (
  options?: AxiosRequestConfig
) => {
  return adminDashboardApi.dashboardControllerGetAppointmentStatistic(options);
};

/**
 * @param timeRangeType?
 * @param options?
 */
export const dashboardControllerGetLeadStatisticByTimeRange = (
  timeRangeType?: string,
  start?: string,
  end?: string,
  componyRegion?: string,
  options?: AxiosRequestConfig
) => {
  return adminDashboardApi.dashboardControllerGetLeadStatisticByTimeRange(
    timeRangeType,
    start,
    end,
    componyRegion,
    options
  );
};

/**
 * @param timeRangeType?
 * @param options?
 */
export const dashboardControllerGetSalesAmountStatisticBySoldDateRange = (
  timeRangeType?: string,
  start?: string,
  end?: string,
  echartType?: string,
  componyRegion?: string,
  options?: AxiosRequestConfig
) => {
  return adminDashboardApi.dashboardControllerGetSalesAmountStatisticBySoldDateRange(
    timeRangeType,
    start,
    end,
    echartType,
    componyRegion,
    options
  );
};

/**
 * @param componyRegion?
 * @param month?
 */

export const dashboardControllerGetTarget = (
  componyRegion?: string,
  month?: string,
  options?: AxiosRequestConfig
) => {
  return adminDashboardApi.dashboardControllerTarget(
    componyRegion,
    month,
    options
  );
};
