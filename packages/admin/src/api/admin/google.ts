import { adminGoogleA<PERSON> } from "..";
import { AxiosRequestConfig } from "axios";
import { AdminGoogleSaveGoogleAccessionRequestSchema } from "@/model";

export const googleControllerSaveGoogleAccession = (
  adminGoogleSaveGoogleAccessionRequestSchema: AdminGoogleSaveGoogleAccessionRequestSchema,
  options?: AxiosRequestConfig
) => {
  return adminGoogleApi.googleControllerSaveGoogleAccession(
    adminGoogleSaveGoogleAccessionRequestSchema,
    options
  );
};
