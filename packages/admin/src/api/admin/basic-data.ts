import {
  adminPromotion<PERSON>pi,
  adminSource<PERSON>pi,
  adminCommon<PERSON>pi,
  adminSupplier<PERSON>pi,
  adminUpload<PERSON>pi,
  adminCost<PERSON>pi,
  adminTargetApi
} from "..";
import { AxiosRequestConfig } from "axios";
import {
  AdminSupplier,
  AdminCost,
  AdminTarget,
  AdminTargetCopyTargetRequestSchema,
  AdminTargetUpdateDateRequestSchema
} from "@/model";

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const promotionControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminPromotionApi.promotionControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const sourceControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminSourceApi.sourceControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const supplierControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminSupplierApi.supplierControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};

/**
 * @param id
 * @param AdminSupplier
 *  @return
 * */
export const supplierControllerUpdate = (
  id: number,
  adminSupplier: AdminSupplier
) => {
  return adminSupplierApi.supplierControllerUpdate(id, adminSupplier);
};

/**
 * @param AdminClients
 *  @return
 * */
export const supplierControllerStore = (adminSupplier: AdminSupplier) => {
  return adminSupplierApi.supplierControllerStore(adminSupplier);
};

/**
 * @param id
 *  @return
 * */
export const supplierControllerDestroy = (id: number) => {
  return adminSupplierApi.supplierControllerDestroy(id);
};

export const commonControllerBasicData = (options?: AxiosRequestConfig) => {
  return adminCommonApi.commonControllerBasicData(options);
};

export const uploadControllerDestroy = (
  id: number,
  options?: AxiosRequestConfig
) => {
  return adminUploadApi.uploadControllerDestroy(id, options);
};

export const uploadControllerShow = (
  id: number,
  _with?: string[],
  options?: AxiosRequestConfig
) => {
  return adminUploadApi.uploadControllerShow(id, _with, options);
};

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const costControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  otherParam?: string,
  options?: AxiosRequestConfig
) => {
  return adminCostApi.costControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    otherParam,
    options
  );
};

/**
 * @param id
 * @param AdminCost
 *  @return
 * */
export const costControllerUpdate = (id: number, adminCost: AdminCost) => {
  return adminCostApi.costControllerUpdate(id, adminCost);
};

/**
 * @param AdminCost
 *  @return
 * */
export const costControllerStore = (adminCost: AdminCost) => {
  return adminCostApi.costControllerStore(adminCost);
};

/**
 * @param id
 *  @return
 * */
export const costControllerDestroy = (id: number) => {
  return adminCostApi.costControllerDestroy(id);
};

/**
 *  @return
 * */
export const costControllerSyncReport = () => {
  return adminCostApi.costControllerSyncReport();
};

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const targetControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminTargetApi.targetControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};

/**
 * @param id
 * @param AdminTarget
 *  @return
 * */
export const targetControllerUpdate = (id: number, adminCost: AdminCost) => {
  return adminTargetApi.targetControllerUpdate(id, adminCost);
};

/**
 * @param AdminTarget
 *  @return
 * */
export const targetControllerStore = (adminCost: AdminTarget) => {
  return adminTargetApi.targetControllerStore(adminCost);
};

/**
 * @param id
 *  @return
 * */
export const targetControllerDestroy = (id: number) => {
  return adminTargetApi.targetControllerDestroy(id);
};

/**
 * @param adminTargetCopy
 *  @return
 * */
export const targetControllerCopyTarget = (
  adminTargetCopy: AdminTargetCopyTargetRequestSchema
) => {
  return adminTargetApi.targetControllerCopyTarget(adminTargetCopy);
};

/**
 * @param adminTargetUpdateDate
 *  @return
 * */
export const targetControllerUpdateDate = (
  adminTargetUpdateDate: AdminTargetUpdateDateRequestSchema
) => {
  return adminTargetApi.targetControllerUpdateDate(adminTargetUpdateDate);
};
