import { http } from "@/utils/http";
import { AxiosRequestConfig } from "axios";
// type Result = {
//   success: boolean;
//   data?: Array<any>;
// };
type ResultTable = {
  success: boolean;
  data?: Array<any>;
  links?: {
    first?: string;
    last?: string;
    prev?: string;
    next?: string;
  };
  meta?: {
    current_page?: number;
    from?: number;
    last_page?: number;
    path?: string;
    per_page?: number;
    to?: number;
    total?: number;
  };
};

export const getDraftLeadsList = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return http.request<ResultTable>(
    "get",
    "/draftleads/list",
    {
      params: { search, filter, sort, _with, withCount, page, size }
    },
    options
  );
};

export const getLeadsList = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return http.request<ResultTable>(
    "get",
    "/leads/list",
    {
      params: { search, filter, sort, _with, withCount, page, size }
    },
    options
  );
};
