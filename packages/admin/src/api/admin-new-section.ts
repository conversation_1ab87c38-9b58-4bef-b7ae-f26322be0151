import { adminNewsSectionApi } from ".";
import { AxiosRequestConfig } from "axios";

/**
 * @param search?
 * @param filter?
 * @param sort?
 * @param _with?
 * @param withCount?
 * @param page?
 * @param size?
 * @param options?
 */
export const newsSectionControllerIndex = (
  search?: string,
  filter?: string,
  sort?: string,
  _with?: string,
  withCount?: string,
  page?: number,
  size?: number,
  options?: AxiosRequestConfig
) => {
  return adminNewsSectionApi.newsSectionControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    options
  );
};
