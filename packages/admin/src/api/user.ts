import { http } from "@/utils/http";
import { adminAuthApi } from "@/api/index";
import { AdminLoginResponse } from "@/model";

export type UserResult = {
  access_token: string;
  success: boolean;
  data: {
    /** 用户名 */
    username: string;
    /** 当前登陆用户的角色 */
    roles: Array<string>;
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  access_token: string;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

/** login */
export const getLogin = (data?: object) => {
  return adminAuthApi.authControllerLogin(data.login_name, data.password);
};

/** refresh token */
export const refreshTokenApi = () => {
  return adminAuthApi.authControllerRefresh();
};

/** Get the current user info */
export const getAuthMe = () => {
  return adminAuthApi.authControllerMe();
};
