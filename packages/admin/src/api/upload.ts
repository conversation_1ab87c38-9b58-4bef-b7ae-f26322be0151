import { adminUploadApi } from ".";
import { AdminUploadCreateSchema } from "@/model";
import { AxiosRequestConfig } from "axios";

/**
 * @param adminUploadCreateSchema
 */
export const uploadControllerStore = (
  adminUploadCreateSchema: AdminUploadCreateSchema
) => {
  return adminUploadApi.uploadControllerStore(adminUploadCreateSchema);
};

/**
 */
export const uploadControllerShow = (
  id: number,
  _with?: Array<string>,
  options?: AxiosRequestConfig
) => {
  return adminUploadApi.uploadControllerShow(id, _with, options);
};
