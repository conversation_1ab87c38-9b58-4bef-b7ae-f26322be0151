import dayjs from "dayjs";
export function removeElesFromArray(arr, elementsToRemove) {
  if (elementsToRemove.some(element => arr.includes(element))) {
    const filteredArr = arr.filter(
      element => !elementsToRemove.includes(element)
    );
    return filteredArr;
  } else {
    return arr;
  }
}

export function removeElseFromObjArr(objArr, elementsToRemove) {
  if (!elementsToRemove.length) {
    return objArr;
  }
  const res = objArr.filter(item => !elementsToRemove.includes(item.name));
  return res;
}

export function handleAddress(address) {
  const regex = /,(.*?)(SA|ACT)/;
  const match = address.match(regex);

  if (match && match[1]) {
    return match[1].trim();
  } else {
    return address;
  }
}

export function getSupplierReceivedDays(orderSupplier) {
  if (!(orderSupplier && orderSupplier.length)) {
    return 0;
  }

  // Initializes a date to infinity
  let earliestDate = dayjs().subtract(10, "year");
  let earliestRecord = null;

  for (const item of orderSupplier) {
    if (item.supplier_received_date === null) {
      const orderDate = dayjs(item.order_date);
      if (orderDate.isAfter(earliestDate)) {
        earliestDate = orderDate;
        earliestRecord = item;
      }
    }
  }
  // If no matching record is found, null is returned
  if (earliestRecord === null) {
    return 0;
  }
  // Calculate the number of days since order_date
  const now = dayjs();
  const differenceInDays = now.diff(earliestDate, "day");
  return differenceInDays;
}

// Calculates the number of days between the specified time and the present
export function getDaysFromToday(dateString) {
  const inputDate = dayjs(dateString).startOf("day");
  const now = dayjs().startOf("day");
  const differenceInDays = now.diff(inputDate, "day");
  return differenceInDays;
}
