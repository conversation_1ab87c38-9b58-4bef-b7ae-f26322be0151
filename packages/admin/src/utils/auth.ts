import Cookies from "js-cookie";
import { storageSession } from "@pureadmin/utils";
import { useUserStoreHook } from "@/store/modules/user";
import { AdminLoginResponse } from "@/model";

export interface DataInfo<T> {
  /** token */
  access_token: string;
  /** `accessToken`的过期时间（时间戳） */
  expires: T;
  /** 用于调用刷新accessToken的接口时所需的token */
  refresh_token: string;
  /** 用户名 */
  username?: string;
  /** 当前登陆用户的角色 */
  roles?: Array<string>;
  /** 当前登陆用户的权限码 */
  permissions?: Array<string>;
  reportLook?: Array<string>;
  userBaseInfo?: object;
}

export const sessionKey = "user-info";
export const TokenKey = "authorized-token";

/** 获取`token` */
export function getToken(): DataInfo<number> {
  // 此处与`TokenKey`相同，此写法解决初始化时`Cookies`中不存在`TokenKey`报错
  return Cookies.get(TokenKey)
    ? JSON.parse(Cookies.get(TokenKey))
    : storageSession().getItem(sessionKey);
}

/**
 * @description 设置`token`以及一些必要信息并采用无感刷新`token`方案
 * 无感刷新：后端返回`accessToken`（访问接口使用的`token`）、`refreshToken`（用于调用刷新`accessToken`的接口时所需的`token`，`refreshToken`的过期时间（比如30天）应大于`accessToken`的过期时间（比如2小时））、`expires`（`accessToken`的过期时间）
 * 将`accessToken`、`expires`这两条信息放在key值为authorized-token的cookie里（过期自动销毁）
 * 将`username`、`roles`、`refreshToken`、`expires`这四条信息放在key值为`user-info`的sessionStorage里（浏览器关闭自动销毁）
 */
export function setToken(data: AdminLoginResponse) {
  let expires = 0;

  const { access_token } = data;
  // expires = new Date(data.expires).getTime(); // 如果后端直接设置时间戳，将此处代码改为expires = data.expires，然后把上面的DataInfo<Date>改成DataInfo<number>即可
  expires = data.expires; // 如果后端直接设置时间戳，将此处代码改为expires = data.expires，然后把上面的DataInfo<Date>改成DataInfo<number>即可
  const cookieString = JSON.stringify({ access_token, expires });
  expires > 0
    ? Cookies.set(TokenKey, cookieString, {
        expires: (expires * 1000 - Date.now()) / 86400000
      })
    : Cookies.set(TokenKey, cookieString);

  function setSessionKey(username: string, roles: Array<string>) {
    useUserStoreHook().SET_USERNAME(username);
    useUserStoreHook().SET_ROLES(roles);
    storageSession().setItem(sessionKey, {
      access_token,
      expires,
      username,
      roles
    });
  }

  if (data.username && data.roles) {
    const { username, roles } = data;
    setSessionKey(username, roles);
  } else {
    const username =
      storageSession().getItem<DataInfo<number>>(sessionKey)?.username ?? "";
    const roles =
      storageSession().getItem<DataInfo<number>>(sessionKey)?.roles ?? [];
    setSessionKey(username, roles);
  }
}

export function setUserInfo(data, me) {
  const { access_token, expires } = data;
  function setSessionKey(
    username: string,
    roles: Array<string>,
    permissions: Array<string>,
    reportLook: Array<string>,
    userBaseInfo: object
  ) {
    useUserStoreHook().SET_USERNAME(username);
    useUserStoreHook().SET_ROLES(roles);
    useUserStoreHook().SET_PERMISSIONS(permissions);
    useUserStoreHook().SET_REPORTLOOK(reportLook);
    useUserStoreHook().SET_USERBASEINFO(userBaseInfo);
    storageSession().setItem(sessionKey, {
      access_token,
      expires,
      username,
      roles,
      permissions,
      reportLook,
      userBaseInfo
    });
  }
  if (me.login_name && me.role_names && me.permission_names) {
    const { login_name, role_names, permission_names, reportLookName } = me;
    const userBaseInfo = {
      id: me.id,
      login_name: me.login_name,
      is_active: me.is_active,
      last_login_at: me.last_login_at,
      products: me.products,
      name: me.name,
      position: me.position,
      mobile: me.mobile,
      email: me.email,
      type: me.type,
      color: me.color,
      role: role_names,
      isPlatformUser: me.is_platform,
      companyRegion: me.companyRegion,
      accountCompanyRegion: me.company_region,
      reportLookIds: me.report_look
    };
    setSessionKey(
      login_name,
      role_names,
      permission_names,
      reportLookName,
      userBaseInfo
    );
  } else {
    const username =
      storageSession().getItem<DataInfo<number>>(sessionKey)?.username ?? "";
    const roles =
      storageSession().getItem<DataInfo<number>>(sessionKey)?.roles ?? [];
    const permissions =
      storageSession().getItem<DataInfo<number>>(sessionKey)?.permissions ?? [];
    const reportLook =
      storageSession().getItem<DataInfo<number>>(sessionKey)?.reportLook ?? [];
    const userBaseInfo =
      storageSession().getItem<DataInfo<number>>(sessionKey)?.userBaseInfo ??
      [];
    setSessionKey(username, roles, permissions, reportLook, userBaseInfo);
  }
}

/** 删除`token`以及key值为`user-info`的session信息 */
export function removeToken() {
  Cookies.remove(TokenKey);
  sessionStorage.clear();
}

/** 格式化token（jwt格式） */
export const formatToken = (token: string): string => {
  return "Bearer " + token;
};
