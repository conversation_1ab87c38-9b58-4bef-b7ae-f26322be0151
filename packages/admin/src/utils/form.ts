import { isNull } from "@pureadmin/utils";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
dayjs.extend(customParseFormat);

export function requiredField(name: string, trigger = "submit") {
  return { required: true, message: `Please enter the ${name}`, trigger };
}

export function sendFormFilter(form: any) {
  const data = JSON.parse(JSON.stringify(form));
  Object.keys(data).forEach(key => {
    if (isNull(data[key])) {
      delete data[key];
    }
  });
  return data;
}

export function formatFilterDates(date, format = "YYYY-MM-DD") {
  if (!date) {
    return [];
  }
  const startDate = dayjs(date[0]).format(format);
  const endDate = dayjs(date[1]).add(1, "day").format(format);
  return [startDate, endDate];
}
//二进制数据转换*/
export function Uint8ArrayToString(array: Uint8Array) {
  let out, i, c;
  let char2, char3;
  out = "";
  const len = array.length;
  i = 0;
  while (i < len) {
    c = array[i++];
    switch (c >> 4) {
      case 0:
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
      case 6:
      case 7:
        out += String.fromCharCode(c);
        break;
      case 12:
      case 13:
        char2 = array[i++];
        out += String.fromCharCode(((c & 0x1f) << 6) | (char2 & 0x3f));
        break;
      case 14:
        char2 = array[i++];
        char3 = array[i++];
        out += String.fromCharCode(
          ((c & 0x0f) << 12) | ((char2 & 0x3f) << 6) | ((char3 & 0x3f) << 0)
        );
        break;
    }
  }
  return out;
}

export function mqttMessageToJson(array: Uint8Array) {
  return JSON.parse(Uint8ArrayToString(array));
}

export function formatTime(time: string, formatStr = "YYYY-MM-DD HH:mm:ss") {
  if (!time) {
    return "";
  }
  return dayjs(time).format(formatStr);
}

// 解析日期
export function parseCustomDate(dateStr, formatStr = "YYYY/MM/DD") {
  try {
    // 尝试使用给定的格式来解析日期字符串
    dayjs(dateStr, "MMM DD, YYYY", true); // 第三个参数为 true 表示严格模式
    // 如果没有抛出异常，则日期字符串是有效的
    let formattedDate = dayjs(dateStr, "MMM DD, YYYY").format(formatStr);
    if (formattedDate == "Invalid Date") {
      formattedDate = dayjs(dateStr).format(formatStr);
      if (formattedDate != "Invalid Date") {
        return formattedDate;
      }
      return dateStr;
    } else {
      return formattedDate;
    }
  } catch (error) {
    // 如果有异常，则日期字符串是无效的
    return dateStr;
  }
}
