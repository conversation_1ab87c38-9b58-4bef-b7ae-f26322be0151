import { transformI18n } from "@/plugins/i18n";
import { clients } from "@/router/enums";
import { ClientsPermission } from "@/enum/permission";

export default {
  path: "/clients",
  meta: {
    icon: "IF-just-icon-client",
    title: transformI18n("menus.hsClients"),
    rank: clients
  },
  children: [
    {
      path: "/clients/index",
      name: transformI18n("menus.hsClients"),
      component: () => import("@/views/clients/index.vue"),
      meta: {
        title: transformI18n("menus.hsClients"),
        permissions: [ClientsPermission.viewAnyClient]
      }
    }
  ]
};
