import { transformI18n } from "@/plugins/i18n";
import { service } from "@/router/enums";
import { ServicePermission } from "@/enum/permission";

export default {
  path: "/service",
  meta: {
    title: transformI18n("menus.hsService"),
    rank: service,
    icon: "IF-just-icon-service"
  },
  children: [
    {
      path: "/service/index",
      name: transformI18n("menus.hsService"),
      component: () => import("@/views/service/index.vue"),
      meta: {
        title: transformI18n("menus.hsService"),
        permissions: [ServicePermission.viewAnyService]
      }
    }
  ]
};
