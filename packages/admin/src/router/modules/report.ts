import { transformI18n } from "@/plugins/i18n";
import { reportManage, report } from "@/router/enums";
import { ReportPermission } from "@/enum/permission";

export default [
  {
    path: "/report",
    meta: {
      title: transformI18n("menus.hasReport"),
      rank: report,
      icon: "IF-just-icon-report"
    },
    component: () => import("@/views/report/index.vue")
  },
  {
    path: "/report/manage",
    meta: {
      title: transformI18n("menus.hasReportManage"),
      rank: reportManage,
      icon: "IF-just-icon-t-manger"
    },
    children: [
      {
        path: "/report/manage/index",
        name: transformI18n("menus.hasReportManage"),
        component: () => import("@/views/reportManage/index.vue"),
        meta: {
          title: transformI18n("menus.hasReportManage"),
          permissions: [ReportPermission.viewAnyReportManager]
        }
      }
    ]
  }
];
