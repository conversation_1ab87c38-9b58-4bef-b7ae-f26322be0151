import { transformI18n } from "@/plugins/i18n";
import { appointment } from "@/router/enums";
import { AppointmentPermission } from "@/enum/permission";

export default {
  path: "/appointment",
  meta: {
    title: transformI18n("menus.hsAppointment"),
    rank: appointment,
    icon: "IF-just-icon-appointed"
  },
  children: [
    {
      path: "/appointment/index",
      name: transformI18n("menus.hsAppointment"),
      component: () => import("@/views/appointment/index.vue"),
      meta: {
        title: transformI18n("menus.hsAppointment"),
        permissions: [AppointmentPermission.viewAnyAppointment]
      }
    }
  ]
};
