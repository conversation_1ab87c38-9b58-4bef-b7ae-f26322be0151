import { transformI18n } from "@/plugins/i18n";
import { product } from "@/router/enums";
import { ProductPermission } from "@/enum/permission";

export default {
  path: "/prodcut",
  meta: {
    icon: "IF-just-icon-product",
    title: transformI18n("menus.hsProduct"),
    rank: product
  },
  children: [
    {
      path: "/prodcut/category",
      name: transformI18n("productSpec.productCategory"),
      component: () => import("@/views/productSpec/category/index.vue"),
      meta: {
        title: transformI18n("productSpec.productCategory"),
        permissions: [ProductPermission.viewAnyProduct]
      }
    }
  ]
};
