import { transformI18n } from "@/plugins/i18n";
const { VITE_HIDE_HOME } = import.meta.env;
const Layout = () => import("@/layout/index.vue");
import { DashboardPermission } from "@/enum/permission";

export default {
  path: "/",
  name: "Dashboard",
  component: Layout,
  redirect: "/dashboard",
  meta: {
    icon: "IF-just-icon-dashboard",
    title: transformI18n("menus.hshome"),
    rank: 0
  },
  children: [
    {
      path: "/dashboard",
      name: "Dashboard",
      component: () => import("@/views/welcome/index.vue"),
      meta: {
        title: transformI18n("menus.hshome"),
        showLink: VITE_HIDE_HOME === "true" ? false : true,
        permissions: [DashboardPermission.viewDashboard]
      }
    }
  ]
} as RouteConfigsTable;
