import { transformI18n } from "@/plugins/i18n";
import { dataSynchronizationManage } from "@/router/enums";

export default [
  {
    path: "/dataSynchronizationManage",
    meta: {
      title: transformI18n("menus.hasDataSynchronizationManage"),
      rank: dataSynchronizationManage,
      icon: "IF-just-icon-export-import"
    },
    component: () => import("@/views/dataSynchronization/index.vue")
  }
];
