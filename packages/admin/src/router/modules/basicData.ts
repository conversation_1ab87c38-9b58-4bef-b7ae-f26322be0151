import { transformI18n } from "@/plugins/i18n";
import { basicData } from "@/router/enums";
import { BasicDataPermission } from "@/enum/permission";

export default {
  path: "/basic",
  meta: {
    icon: "IF-just-icon-data",
    title: transformI18n("menus.hsbasic"),
    rank: basicData
  },
  children: [
    {
      path: "/basic/index",
      name: transformI18n("menus.hsbasic"),
      component: () => import("@/views/basicData/index.vue"),
      meta: {
        title: transformI18n("menus.hsbasic"),
        permissions: [
          BasicDataPermission.viewAnyPromotion,
          BasicDataPermission.viewAnySource,
          BasicDataPermission.viewAnySupplier,
          BasicDataPermission.viewAnyCost
        ]
      }
    }
  ]
};
