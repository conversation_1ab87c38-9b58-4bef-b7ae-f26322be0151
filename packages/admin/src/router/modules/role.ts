import { transformI18n } from "@/plugins/i18n";
import { systemRole } from "@/router/enums";
import { RolePermission } from "@/enum/permission";

export default {
  path: "/role",
  meta: {
    icon: "IF-just-icon-permission",
    title: transformI18n("menus.hsRole"),
    rank: systemRole
  },
  children: [
    {
      path: "/role/index",
      name: transformI18n("menus.hsRole"),
      component: () => import("@/views/system/role/index.vue"),
      meta: {
        title: transformI18n("menus.hsRole"),
        permissions: [RolePermission.viewAnyRole]
      }
    }
  ]
};
