import { transformI18n } from "@/plugins/i18n";
import { orders } from "@/router/enums";
import { OrdersPermission } from "@/enum/permission";

export default {
  path: "/orders",
  meta: {
    title: transformI18n("menus.hsOrders"),
    rank: orders,
    icon: "IF-just-icon-order"
  },
  children: [
    {
      path: "/orders/index",
      name: transformI18n("menus.hsOrders"),
      component: () => import("@/views/orders/index.vue"),
      meta: {
        title: transformI18n("menus.hsOrders"),
        permissions: [OrdersPermission.viewAnyOrders]
      }
    }
  ]
};
