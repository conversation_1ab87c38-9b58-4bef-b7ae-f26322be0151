import { transformI18n } from "@/plugins/i18n";
import { leads } from "@/router/enums";
import { LeadsPermission } from "@/enum/permission";

export default {
  path: "/leads",
  meta: {
    title: transformI18n("menus.hsLeads"),
    rank: leads,
    icon: "IF-just-icon-lead"
  },
  children: [
    {
      path: "/leads/index",
      name: transformI18n("menus.hsLeads"),
      component: () => import("@/views/leads/index.vue"),
      meta: {
        title: transformI18n("menus.hsLeads"),
        permissions: [LeadsPermission.viewAnyLeads]
      }
    }
  ]
};
