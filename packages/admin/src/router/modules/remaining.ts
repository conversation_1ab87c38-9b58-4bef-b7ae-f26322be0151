import { transformI18n } from "@/plugins/i18n";

const Layout = () => import("@/layout/index.vue");

export default [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: transformI18n("login.login"),
      showLink: false,
      rank: 101
    }
  },
  {
    path: "/reset-password",
    name: "ResetPassword",
    component: () => import("@/views/login/resetPassword.vue"),
    meta: {
      title: transformI18n("login.resetPassword"),
      showLink: false,
      rank: 99
    }
  },
  {
    path: "/privacy-policy",
    name: "PrivacyPolicy",
    component: () => import("@/views/welcome/policy.vue"),
    meta: {
      title: transformI18n("login.privacyPolicy"),
      showLink: false,
      rank: 88
    }
  },
  {
    path: "/redirect",
    component: Layout,
    meta: {
      title: "加载中...",
      showLink: false,
      rank: 102
    },
    children: [
      {
        path: "/redirect/:path(.*)",
        name: "Redirect",
        component: () => import("@/layout/redirect.vue")
      }
    ]
  },
  {
    path: "/lead",
    name: "Lead",
    component: () => import("@/views/leads/pad_form.vue"),
    meta: {
      title: transformI18n("menus.hsLeads"),
      showLink: false,
      rank: 103
    }
  },
  {
    path: "/appointment",
    name: "Appointment",
    component: () => import("@/views/appointment/pad.vue"),
    meta: {
      title: transformI18n("menus.hsAppointment"),
      showLink: false,
      rank: 104
    }
  },
  {
    path: "/leadHistory",
    name: "Lead History",
    component: () => import("@/views/leads/pad_leads.vue"),
    meta: {
      title: transformI18n("menus.hsLeads"),
      showLink: false,
      rank: 105
    }
  }
] as Array<RouteConfigsTable>;
