import { transformI18n } from "@/plugins/i18n";
import { systemAccount } from "@/router/enums";
import { AccountPermission } from "@/enum/permission";
export default {
  path: "/account",
  meta: {
    icon: "IF-just-icon-account",
    title: transformI18n("menus.hsUser"),
    rank: systemAccount
  },
  children: [
    {
      path: "/account/index",
      name: transformI18n("menus.hsUser"),
      component: () => import("@/views/system/account/index.vue"),
      meta: {
        title: transformI18n("menus.hsUser"),
        permissions: [AccountPermission.viewAnyAccount]
      }
    }
  ]
};
