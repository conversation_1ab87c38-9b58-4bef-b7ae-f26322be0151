.wave {
  position: fixed;
  height: 100%;
  width: 100%;
  left: 0;
  bottom: 0;
  z-index: -1;
}

.login-container {
  width: 100vw;
  height: 100vh;
  align-items: center;
  justify-content: center;
  display: flex;
  /* display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 18rem;
  padding: 0 2rem; */
}

.img {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.img img {
  width: 500px;
}

.login-box {
  display: flex;
  align-items: center;
  text-align: center;
  width: 600px;
  height: 600px;
  background-color: #fff;
  border-radius: 20px;
  margin: 0 auto;
  margin-top: 47px;
  box-shadow: 0 32px 64px -12px rgba(20, 9, 26, 0.1);
}

.login-form {
  width: 360px;
  margin: 0 auto;
  height: 100%;
}

.login-title {
  font-size: 30px;
  font-weight: 500;
  color: #000;
  margin-top: 50px;
}
.login-desc {
  font-size: 16px;
  font-weight: 400;
  color: #656f7d;
  margin-top: 12px;
  margin-bottom: 40px;
}
.avatar {
  width: 320px;
  height: 73px;
  margin: 0 auto;
}
:root .el-form-item {
  margin-bottom: 30px;
}
:root .remembber-me {
  border: none;
  margin-bottom: 8px;
}
.login-form .el-button--primary {
  height: 44px;
  border-radius: 8px;
}
@media screen and (max-width: 1180px) {
  .login-container {
    grid-gap: 9rem;
  }

  .login-form {
    width: 290px;
  }

  .login-form h2 {
    font-size: 2.4rem;
    margin: 8px 0;
  }

  .img img {
    width: 360px;
  }

  .avatar {
    width: auto;
    height: 73px;
  }
}

@media screen and (max-width: 968px) {
  .wave {
    display: none;
  }

  .img {
    display: none;
  }

  .login-container {
    grid-template-columns: 1fr;
  }

  .login-box {
    justify-content: center;
  }
}
