@import "./transition";
@import "./element-plus";
@import "./sidebar";
@import "./dark";

// @import "./amap.css";

/* 自定义全局 CssVar */
:root {
  /* 左侧菜单展开、收起动画时长 */
  --pure-transition-duration: 0.3s;
  --el-input-focus-border: transparent !important;
  --el-select-input-focus-border-color: transparent !important;
  --el-input-focus-border-color: transparent !important;
  --font-color-level1: #2a2e34;
  --font-color-level2: #656f7d;
  --border-color-lecel1: #e9ebf0;
  --el-color-primary: #9b3ce5 !important;
  --el-color-primary-dark-1: #a54fe7 !important;
  --el-color-primary-dark-2: #af63ea !important;
  --el-color-primary-light-1: #a54fe7 !important;
  --el-color-primary-light-2: #af63ea !important;
  --el-color-primary-light-3: #b976ec !important;
  --el-color-primary-light-4: #c38aef !important;
  --el-color-primary-light-5: #cd9df2 !important;
  --el-color-primary-light-6: #d7b1f4 !important;
  --el-color-primary-light-7: #e1c4f7 !important;
  --el-color-primary-light-8: #ebd8f9 !important;
  --el-color-primary-light-9: #f5ebfc !important;

  .el-button--info {
    background-color: #f7f8f9;
    border-color: #e9ebf0;
  }

  .el-form--label-right .el-form-item,
  .border-form.el-form--label-top .el-form-item__content {
    padding: 0 0 0 5px;
    margin: 10px 10px 0 8px;
    border: 1px solid #e9ebf0;
    border-radius: 6px;
  }

  .el-form--label-right .el-form-item.action-box {
    border: 0;
  }

  .el-form--label-right .el-form-item__label {
    font-weight: 400;
    color: var(--font-color-level2);
  }

  .el-input__wrapper,
  .el-textarea__inner {
    padding: 0 10px 0 0;
    box-shadow: 0 0 0 0 !important;
  }

  .el-form--label-right .el-input__wrapper:hover,
  .el-form--label-right .el-textarea__inner:hover {
    border: 0;
    box-shadow: 0 0 0 0 !important;
  }

  .el-form--label-top .el-input__wrapper:hover,
  .el-form--label-top .el-textarea__inner:hover {
    background-color: #f0f4f7;

    // background-color: #F7F8F9;
  }

  .el-select .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 0 !important;
  }

  .el-select .el-input.is-focus .el-input__wrapper {
    box-shadow: 0 0 0 0 !important;
  }

  .el-input--large .el-input__wrapper {
    padding: 1px 8px;
  }
}

/* 灰色模式 */
.html-grey {
  filter: grayscale(100%);
}

/* 色弱模式 */
.html-weakness {
  filter: invert(80%);
}

// result option css
.cascader-label {
  display: flex;
  align-items: center;

  .iconfont {
    margin-right: 5px;
    color: var(--el-color-primary);
  }
}
