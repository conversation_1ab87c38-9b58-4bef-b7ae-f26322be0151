@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .flex-c {
    @apply flex justify-center items-center;
  }

  .flex-ac {
    @apply flex justify-around items-center;
  }

  .flex-bc {
    @apply flex justify-between items-center;
  }

  .navbar-bg-hover {
    @apply dark:text-white dark:hover:!bg-[#242424];
  }
}

/*  */
.el-card__body {
  --el-card-padding: 0;
}

/* table CSS */
.pure-table thead {
  z-index: 3 !important;
}
.pure-table {
  .el-table--border {
    border-radius: 8px;
    border: 1px solid #ebeef5;
  }
  .el-table__border-left-patch {
    width: 0;
  }
  .el-table--border::before {
    width: 0;
  }
  .el-table--border::after {
    width: 0;
  }
}
.header-row {
  border: 1px solid #e9ebf0;
  background-color: red;
}
.header-row .header-cell {
  background: #f7f8f9 !important;
  border-right: 0 !important;
  color: #656f7d !important;
  font-weight: 400;
  font-size: 12px;
  padding: 3px 0 !important;
}
.tab-row .tab-cell {
  border-right: 0 !important;
}
.el-tabs__item {
  font-size: 13px;
}
.empty {
  color: #9da7b8;
}

/* dialog CSS */
.el-dialog {
  border-radius: 10px !important;
  overflow: hidden;
}
.el-dialog .el-dialog__header {
  padding: 11px 15px;
  background: #f7f8f9;
  margin: 0;
  border-bottom: 1px solid #e9ebf0;
  height: 40px;
  display: flex;
}
.el-dialog .el-dialog__title {
  color: var(--font-color-level1);
  font-size: 14px;
  line-height: 17px;
}
.el-dialog .el-dialog__headerbtn {
  height: 40px;
  width: 40px;
  top: 0;
}
.el-dialog .el-dialog__body {
  padding: 0px 0px 20px;
  overflow-y: auto;
  max-height: calc(100vh - 230px);
}

.no-footer-dialog.el-dialog .el-dialog__body {
  max-height: calc(100vh - 200px);
  padding: 0;
}
.creat-dialog .el-dialog__body {
  padding: 0px 30px 20px;
  .el-form-item__label {
    height: 40px;
    line-height: 40px;
  }
  .el-input {
    height: 40px;
    line-height: 40px;
  }
}

.el-dialog .el-col {
  margin-bottom: 15px;
  .el-select {
    width: 100%;
  }
}
.el-dialog .el-form--label-right .el-col:nth-child(odd) {
  .el-form-item {
    margin-left: 0;
  }
}

.el-dialog {
  .el-form--label-right .el-form-item {
    margin: 10px 0 0 0;
  }
  .el-form--label-right .el-form-item__label {
    font-size: 13px;
  }
  .el-input__inner {
    font-size: 13px;
  }
  .el-button {
    font-weight: 700;
  }
}

/* list page search form style */
.search-icon {
  width: 16px;
}
.list-form {
  align-items: flex-end;
  height: max-content;
  margin-bottom: -31px;
  padding: 0 110px 0px 8px;
  .el-form--inline .el-form-item {
    margin-left: 0;
    margin-right: 8px;
    width: 200px;
  }
  .el-form-item:first-child {
    margin-left: 0px;
  }
  /* .el-select {
    width: 135px;
  } */
}

.page-action-box {
  position: absolute;
  top: 0;
  right: 20px;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 50px;
}
.el-button {
  font-weight: 500;
}

.el-button--info {
  --el-button-bg-color: #e9ebf0 !important;
  --el-button-text-color: #656f7d !important;
  --el-button-border-color: #e9ebf0 !important;
}

.result-container {
  position: relative;
  .el-cascader .el-input__inner {
    padding-left: 25px;
  }
  .el-input__wrapper {
    padding-left: 5px;
  }
  .input-result-icon {
    position: absolute;
    top: 5px;
    left: 10px;
    z-index: 2;
  }
  .iconfont {
    color: var(--el-color-primary);
  }
}
.el-radio.is-disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.input-result-icon {
  color: var(--el-color-primary);
}

/* pagination style */
.pure-table .el-pagination {
  --el-pagination-border-radius: 6px;
  --el-pagination-button-bg-color: #f7f8f9;
  .el-pager {
    li.is-active {
      background: none;
    }
    li {
      border: 1px solid #e9ebf0;
    }
  }

  button {
    border: 1px solid #e9ebf0;
    background-color: #f7f8f9;
    color: #656f7d;
  }
}

/* datatable company action box */
.info-action-box {
  background-color: #fff;
  color: #656f7d;
  border: 1px solid #e9ebf0;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  padding: 10px 8px;
  height: 32px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: bolder;
  svg {
    color: var(--el-color-primary);
    margin-right: 5px;
  }
}

.table-search-input {
  height: 32px;
  border-radius: 6px;
  margin-right: 10px;
  border: 1px solid #e9ebf0;
}

.table-header-action {
  margin-bottom: 10px;
}
.el-checkbox.is-bordered.el-checkbox--small {
  background: #f7f8f9;
  height: 40px;
  padding: 12px 15px;
  border: 1px solid #e9ebf0;
  font-size: 13px;
  margin-right: 10px;
}

.w-200 {
  width: 200px;
}

.el-input-group__append {
  box-shadow: none !important;
}

/* font style */
/* detail title */
.level-1 {
  color: var(--font-color-level1);
  font-size: 18px;
  line-height: 27px;
  font-weight: 600;
}
/* detail item label */
.level-2 {
  font-size: 13px;
  color: var(--font-color-level2);
  line-height: 15px;
}
.el-form--label-top .el-form-item {
  margin-bottom: 0px;
}
.el-form--default.el-form--label-top .el-form-item .el-form-item__label {
  font-size: 13px;
  color: var(--font-color-level2);
  line-height: 15px !important;
  font-weight: 400;
  margin-bottom: 8px !important;
}

.product-select .el-select .el-input .el-select__icon {
  font-size: 28px !important;
}

.el-form--label-top .el-select__tags .el-tag {
  border-radius: 10px;
}

/* detail item content */
.level-3 {
  font-size: 13px;
  color: var(--font-color-level1);
  line-height: 15px;
  font-weight: 500;
}

.el-form--label-top .el-input__inner {
  font-size: 13px;
  color: var(--font-color-level1);
  line-height: 15px;
  font-weight: 500;
}

.level-4 {
  font-size: 12px;
  color: #9da7b8;
  line-height: 14px;
  font-weight: 400;
}

/* result-dialog */
.result-dialog {
  .el-dialog__header {
    border-bottom: 0;
    height: 30px;
  }
  .el-dialog__body {
    padding: 0px 30px 30px;
  }
}

.leads-note {
  padding: 20px;
  .el-tabs__item {
    font-weight: 400;
    font-size: 18px;
    line-height: 27px;
    color: #2a2e24;
  }
  .el-tabs__item.is-active {
    font-weight: 600;
  }
  .el-radio-group {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 5px 0 20px;
    flex-wrap: nowrap;
  }
  .el-radio-button .el-radio-button__inner {
    border: 0 !important;
    box-shadow: none !important;
    border-radius: 20px !important;
    background: #f7f8f9;
    color: var(--font-color-level1);
    font-weight: 500;
    font-size: 13px;
    line-height: 15px;
  }

  .el-radio-button.is-active,
  .el-radio-button:hover {
    .el-radio-button__inner {
      background: #9937e51a;
      color: var(--el-color-primary);
    }
  }
}

.leads-note .time-line .el-timeline-item {
  margin-bottom: 10px;
  padding-bottom: 0;
}

.el-cascader-node > .el-radio {
  position: absolute;
  background: transparent;
  width: 70%;
  .el-radio__input {
    display: none;
  }
}

.resultChangeChange {
  background: var(--el-color-primary);
  border-radius: 6px;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 8px;
  width: 128px;
  height: 34px;
  .el-form--label-top .el-input__wrapper:hover {
    background-color: transparent;
  }
  .el-input__wrapper .el-input__suffix {
    color: #fff;
  }
  .el-cascader .el-input {
    background-color: transparent;
  }
  .el-input__wrapper {
    background-color: transparent !important;
  }
  .el-cascader .el-input .el-input__inner {
    color: #fff;
    background-color: transparent;
  }
  .el-input.is-disabled .el-input__inner {
    -webkit-text-fill-color: #fff;
  }
}

/* address map style */
#pac-card {
  .el-input__wrapper {
    width: 100%;
    height: 40px;
    padding-left: 20px;
    border-radius: 32px;
  }
  .el-input__inner {
    font-size: 16px;
    font-weight: 500;
    color: #202124;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background-color: #fff;
    border: 0;
    /* border-radius: 32px;
    box-shadow: 0 1px 2px rgb(60 64 67 / 30%), 0 1px 3px 1px rgb(60 64 67 / 15%); */
  }
  .locationIcon {
    margin: 0 30px 0px 20px;
    flex-shrink: 0;
  }
}

/* list el-tab style */
.page-header {
  position: absolute;
  top: 10px;
  left: 15px;
  /* width: 73px; */
  height: 27px;
  padding: 0 20px 0 0;
  font-size: 18px;
  border-right: 1px solid #e9ebf0;
}
.demo-tabs.el-tabs {
  --el-tabs-header-height: 50px;
  .el-tabs__header {
    padding: 0 120px;
    margin: 0;
    border-bottom: 1px solid #e9ebf0;

    .el-tabs__nav-wrap::after {
      background-color: transparent;
    }

    .el-tabs__item {
      font-weight: normal;
    }

    .el-tabs__item.is-active {
      font-weight: 500;
      color: #000;
    }

    .el-tag {
      width: max-content;
      min-width: 15px;
      height: 15px;
      padding: 1px;
      margin-left: 5px;
      font-size: 11px;
      font-weight: 500;
      color: #656f7d;
      background-color: #f0f4f7;
      border: 0;
    }
  }
  .el-tabs__nav-prev,
  .el-tabs__nav-next {
    display: flex;
    align-items: center;
    height: 100%;
  }
  .custom-tabs-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .el-tabs__nav-scroll {
    overflow: auto;
  }
  .el-tabs__nav-wrap.is-scrollable {
    box-sizing: border-box;
    padding: 0 60px 0 20px;
    overflow: auto;
  }
}

.leads-list-page .pure-table .el-table--fit {
  max-height: calc(100vh - 220px);
  overflow: auto;
}

/* table menu down */
.layout-theme-default body[layout="vertical"] .table-menu.el-menu--horizontal {
  border: 0;
  justify-content: center;
  overflow: hidden;
  .el-sub-menu__icon-arrow {
    display: none;
  }
  .is-active > .el-sub-menu__title {
    border: 0;
    color: #2a2e34 !important;
  }

  .el-menu-item.is-active {
    color: #2a2e34 !important;
  }
}
.layout-theme-default
  body[layout="vertical"]
  .el-menu--horizontal
  .el-menu-item.is-active {
  color: #2a2e34 !important;
}

/* appoint calendar view style */
.app-calendar .fc-list-table .fc-list-event {
  line-height: 17px;
  .fc-list-event-time {
    width: 125px;
    min-width: 125px;
    text-align: center;
  }
  .fc-list-event-graphic {
    display: none;
  }
  td.fc-list-event-title {
    padding: 0 !important;
  }
}
.fc-view-harness.fc-view-harness-active {
  overflow: auto;
}
.fc-theme-standard .fc-popover.fc-more-popover {
  max-height: 200px;
  overflow: auto;
}
.fc .fc-popover {
  z-index: 999;
}
/* week view */
.fc-dayGridWeek-view {
  .type,
  .product,
  .circle-text {
    margin-top: 2px;
  }
}
/* day view */
.app-calendar.fc .fc-timeGridDay-view {
  .fc-timegrid-slot {
    height: 40px;
  }
  --fc-event-bg-color: transparent;
  --fc-event-border-color: transparent;
  /* .fc-v-event{}*/
  .fc-timegrid-event-harness-inset .fc-timegrid-event {
    box-shadow: none;
  }

  .day-event-box {
    height: 100%;
    box-shadow: 0 0 0 1px rgba(233, 235, 240, 0.9);
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    flex-grow: 0;
    min-height: 50px;
    /* flex-direction: column; */
    overflow: hidden;
    padding: 2px;

    .event-left {
      flex-grow: 0;
      flex-shrink: 1;
      flex-wrap: nowrap;
      justify-content: flex-start;
      height: 28px;
      margin-right: 3px;
    }
    .time {
      white-space: nowrap;
    }
    .day-content-box {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      max-width: 100%;
      overflow: auto;
    }
  }
}
.text-over {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}
.fc-listMonth-view {
  .fc-scroller.fc-scroller-liquid {
    overflow: auto !important;
  }
  .fc-cell-shaded {
    background-color: transparent !important;
  }
  .fc-list-table {
    border-style: solid;
  }
}

.calendar-list-header-text {
  text-align: left;
  padding: 8px 14px;
}
.list-view-page,
.calendar-list-header {
  .item {
    padding: 8px 11px;
    border-left: 1px solid #dcdfe6;
    height: 34px;
    flex-shrink: 0;
  }
}
.calendar-list-header {
  width: 100%;
  .item {
    border-top: 1px solid #dcdfe6;
    height: 45px;
  }
  .item:last-child {
    border-right: 1px solid #dcdfe6;
  }
  .calendar-list-header-address {
    flex-grow: 1;
    min-width: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  ::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.fc-list-day-cushion {
  padding: 0 !important;
}
.fc-listDay-view {
  .fc-scroller-liquid {
    overflow: auto !important;
  }
  .fc-cell-shaded {
    background-color: transparent !important;
  }
  .fc-list-table {
    border-style: solid;
  }
}

.primary-color {
  color: var(--el-color-primary);
}
.btn {
  cursor: pointer;
}
.just-icon-user-smile {
  font-size: 24px !important;
  color: #9da7b8;
}
/* datatable style */
.table-header-popper {
  overflow-y: auto;
  max-height: calc(100vh - 220px);
}

.el-dialog .el-input-number .el-input__inner {
  text-align: left;
}

/* appointment deail  */
.appointment-detail-drawer .el-drawer__header {
  margin-bottom: 0;
}

.appointment-detail-drawer .subBtn {
  color: var(--el-color-primary);
  background: rgb(153 55 229 / 10%);
  border: none;

  .iconfont {
    font-size: 15px;
  }

  &.is-disabled {
    color: var(--el-color-primary);
    background: rgb(153 55 229 / 10%);
    border: none;
    opacity: 0.5;

    .iconfont {
      color: var(--el-color-primary);
    }
  }
}
.appointment-detail-drawer .el-upload {
  width: 100%;
}

.uploadBtn {
  background: #ffffff !important;
  color: #9b3ce5 !important;
  border-color: #e9ebf0 !important;
}

/* dashboard css */
.echartTitle {
  font-weight: 500;
  font-size: 16px;
}
.chart-header .action {
  .el-radio-button__inner {
    padding: 6px 10px;
    line-height: 20px;
  }
  .el-radio-button {
    --el-border-radius-base: 8px;
  }
}

.custom-page {
  position: relative;
  padding: 15px 0 5px;
  .btn-prev,
  .btn-next {
    padding: 8px 14px 8px 14px;
    border-radius: 8px;
    border: 1px solid #d0d5dd;
    font-size: 14px;
    font-weight: 500;
    color: #656f7d;
    margin-right: 12px;
  }

  .page-right {
    position: absolute;
    right: 0;
  }
}

.el-button--info.el-button:focus,
.el-button--info.el-button:hover {
  color: var(--el-button-hover-text-color);
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  outline: 0;

  .iconfont {
    color: #fff;
  }
}

.cascader-label.disabled {
  color: var(--el-text-color-placeholder);
  cursor: not-allowed;
}
.el-cascader-node.is-active {
  .cascader-label.disabled {
    color: var(--el-color-primary);
  }
}
