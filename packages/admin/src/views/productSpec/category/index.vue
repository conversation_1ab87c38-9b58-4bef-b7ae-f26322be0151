<script setup lang="ts">
import {
  productCategoryControllerIndex,
  productCategoryControllerExportBcolor,
  productCategoryControllerImportBcolorFromExcel
} from "@/api/admin/product";
import { ref, onMounted, nextTick } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { ElLoading } from "element-plus";
import ResultDialog from "./ImportDialog.vue";
import { ElMessage } from "element-plus";

defineOptions({
  name: "Category"
});

const categoryList = ref([]);
const dialogPanel = ref();
const mv = ref();
const resultDialogRef = ref(null);
function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}
function openMeshDialog() {
  const data = {
    title: "Landscaping Mesh Color List",
    content: "You can download or update the color list",
    type: "custom",
    icon: "new_order",
    width: "400px",
    importBtnText: "Import Mesh Color List",
    importType: "BMeshList",
    buttons: [
      {
        text: "Download Mesh Color List",
        onClick: downloadMeshList
      }
    ]
  };
  nextTick(() => {
    resultDialogRef.value.show(data).then(_res => {
      // console.log("close result", _res);
    });
  });
}
// To open import and down Bcolors Dialog
function openBColorDialog() {
  const data = {
    title: "Landscaping Color List",
    content: "You can download or update the color list",
    type: "custom",
    icon: "new_order",
    width: "400px",
    importBtnText: "Import Color List",
    importType: "BColorList",
    buttons: [
      {
        text: "Download Color List",
        onClick: downloadBcolorList
      }
    ]
  };
  nextTick(() => {
    resultDialogRef.value.show(data).then(_res => {
      // console.log("close result", _res);
    });
  });
}

function openImportSuccessfulDiaolog() {
  const data = {
    title: "Import Successful",
    content: "The color list has been updated successfully",
    type: "success",
    buttons: [
      {
        text: "Close",
        onClick: hideResultDialog
      }
    ]
  };
  nextTick(() => {
    resultDialogRef.value.show(data).then(_res => {
      // console.log("close result", _res);
    });
  });
}

function downloadBcolorList() {
  exportColorList();
}

function exportColorList(type = "BColorList") {
  resultDialogRef.value?.setLoading(true);
  productCategoryControllerExportBcolor(type, { responseType: "blob" })
    .then(res => {
      const link = document.createElement("a");
      link.href = URL.createObjectURL(new Blob([res]));
      link.download = type + ".xlsx";
      link.click();
      resultDialogRef.value?.setLoading(false);
    })
    .catch(_err => {
      ElMessage.error(_err.message || "Export failed.");
    });
  hideResultDialog();
}

function downloadMeshList() {
  exportColorList("BMeshList");
}

function hideResultDialog() {
  resultDialogRef.value.hide(true);
}

function uploadFileChange(param) {
  resultDialogRef.value?.setLoading(true, "import");
  productCategoryControllerImportBcolorFromExcel(param)
    .then(_res => {
      resultDialogRef.value?.setLoading(false, "import");
      openImportSuccessfulDiaolog();
      hideResultDialog();
    })
    .catch(_err => {
      resultDialogRef.value?.setLoading(false, "import");
      hideResultDialog();
      const msg = _err.response?.data?.message || "Import failed.";
      ElMessage.error(msg);
    });
}
onMounted(() => {
  nextTick(() => {
    dialogLoading();
    productCategoryControllerIndex("", "", "id", "products")
      .then(res => {
        categoryList.value = res.data;
        dialogPanel.value.close();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
});
</script>

<template>
  <el-card shadow="never">
    <template #header>
      <div class="card-header">
        <span class="font-medium">
          {{ transformI18n("productSpec.productCategory") }}
        </span>
      </div>
    </template>
    <div
      class="flex justify-end items-center w-full h-[30px] mt-[10px] px-[10px]"
    >
      <div class="action-box">
        <el-button type="info" @click="openBColorDialog()">
          <span class="mr-1 primary-color iconfont just-icon-new_order" />{{
            transformI18n("productSpec.importAndExportBColor")
          }}
        </el-button>
        <el-button type="info" @click="openMeshDialog()">
          <span class="mr-1 primary-color iconfont just-icon-new_order" />{{
            transformI18n("productSpec.importAndExportBMesh")
          }}
        </el-button>
      </div>
    </div>
    <div ref="mv" class="mt-2 px-2 pb-2 bg-bg_color category-item">
      <el-row
        class="border border-[#E9EBF0] p-2.5 bg-[#F7F8F9] rounded-t-md text-xs"
      >
        <el-col :span="12">{{ transformI18n("productSpec.category") }}</el-col>
        <el-col :span="12">{{ transformI18n("productSpec.product") }}</el-col>
      </el-row>
      <el-row
        class="border-x border-b border-[#E9EBF0] pl-2.5 text-xs"
        v-for="(category, key) in categoryList"
        :key="key"
      >
        <el-col
          class="pt-4 border-r border-[#E9EBF0]"
          v-if="category.products.length"
          :span="12"
        >
          {{ category.name }}
        </el-col>
        <el-col class="product-list" v-if="category.products.length" :span="12">
          <el-row
            class="product-item pl-2.5 pr-2.5 pb-4 border-[#E9EBF0] pt-4 text-xs"
            v-for="(product, index) in category.products"
            :key="index"
          >
            {{ product.name }}
          </el-row>
        </el-col>
        <el-col v-else :span="24">
          {{ category.name }}
        </el-col>
      </el-row>
      <!-- <data-table ref="dataTableRef"
                  :columns="columns"
                  :source="productControllerIndex"
                  :form="form"
                  :spanMethod="objectSpanMethod"
                  :query-params="tableQueryParams"
                  :cell-class-name="'span-cell'"
                  :header-cell-style="{
          background: 'var(--el-table-row-hover-bg-color)',
          color: 'var(--el-text-color-primary)'
        }" /> -->
    </div>
    <ResultDialog ref="resultDialogRef" @uploadFileChange="uploadFileChange" />
  </el-card>
</template>
<style lang="scss" scoped>
.category-item {
  height: calc(100vh - 160px);
  overflow-y: auto;
}

.product-list .product-item {
  border-bottom: 1px solid #e9ebf0;
}

.product-list .product-item:last-child {
  border: none;
}

.el-button--info.el-button:focus,
.el-button--info.el-button:hover {
  color: var(--el-button-hover-text-color);
  background-color: var(--el-color-primary);
  border-color: var(--el-color-primary);
  outline: 0;

  .iconfont {
    color: #fff;
  }
}
</style>
