<script setup lang="ts">
import SuccessIcon from "@/assets/svg/success.svg?component";
import { WarningFilled } from "@element-plus/icons-vue";
import { ref, onMounted } from "vue";
import { ImportExcel } from "@/components/Upload";

const dialogVisible = ref(false);
const promise: any = {};
const title = ref("Success");
const content = ref("content...");
const type = ref("success"); // success, warning, error,custom
const buttons = ref([{ text: "Create Another New", onClick: () => {} }]);
const icon = ref(null);
const width = ref("300px");
const btnLayout = ref("column"); //row column
const importExcelRef = ref(null);
const importBtnText = ref("Import");
const uploadLoading = ref(false);
const loading = ref(false);
const importType = ref(null);

const emit = defineEmits<{
  (e: "uploadFileChange", val: Object): void;
  (e: "importList", val: Object): void;
}>();

function show(data) {
  resetData();
  title.value = data.title;
  content.value = data.content;
  type.value = data.type;
  buttons.value = data.buttons;
  const keys = ["width", "icon", "btnLayout", "importBtnText", "importType"];
  const refs = { width, icon, btnLayout, importBtnText, importType };
  for (const key of keys) {
    if (data[key]) {
      refs[key].value = data[key];
    }
  }
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function resetData() {
  title.value = "Success";
  type.value = "content...";
  buttons.value = [];
  icon.value = null;
  width.value = "300px";
  btnLayout.value = "column";
  importBtnText.value = "Import";
  uploadLoading.value = false;
  loading.value = false;
  importType.value = null;
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  loading.value = false;
  uploadLoading.value = false;
  if (actionOver) {
    promise.resolve();
  }
}

function setLoading(loading, type = "import") {
  if (type == "import") {
    uploadLoading.value = loading;
  } else {
    loading.value = loading;
  }
}

function uploadFileChange(res) {
  emit("uploadFileChange", { ...res, type: importType.value });
}
onMounted(() => {});
defineExpose({ show, hide, setLoading });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="''"
    :width="width"
    align-center
    class="result-dialog"
  >
    <div class="center-box">
      <SuccessIcon class="result-icon" v-if="type === 'success'" />
      <span
        v-else-if="icon"
        :class="`mr-1 primary-color iconfont just-icon-${icon}`"
      />
      <WarningFilled class="result-icon warning" v-else />

      <div class="title level-1">{{ title }}</div>
      <div class="content">
        {{ content }}
      </div>
      <div :class="btnLayout == 'row' ? 'btnRow' : 'btnColumn'">
        <el-button
          type="info"
          class="mt-[20px] w-[100%] !mx-[0px]"
          v-for="(btn, index) in buttons"
          :key="index"
          @click="btn.onClick"
          :loading="loading"
          >{{ btn.text }}</el-button
        >
        <ImportExcel
          ref="importExcelRef"
          @fileChange="uploadFileChange"
          :loading="uploadLoading"
          :btnTxt="importBtnText"
          class="mt-[20px] w-[100%]"
          v-if="importType"
        />
      </div>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.result-icon {
  width: 50px;
  height: 50px;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.el-button {
  font-size: 14px;
  font-weight: 500;
  color: #2a2e34;
  background: #f7f8f9;
  border: 1px soild #f7f8f9;
}

.warning {
  color: #ff9000;
}

.btnRow {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-betweens;
}

.btnColumn {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.iconfont {
  font-size: 24px;
}
</style>
