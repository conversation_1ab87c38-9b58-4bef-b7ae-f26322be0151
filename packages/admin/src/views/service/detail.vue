<script setup lang="ts">
import { reactive, ref, computed, onMounted, nextTick, watch } from "vue";
import {
  detailActionList,
  allResults,
  calculatedAmount
} from "./components/data";
import {
  serviceControllerShow,
  serviceControllerUpdate,
  serviceControllerSaveSpec,
  serviceControllerGeneratorPDF
} from "@/api/admin/service";
import { sendFormFilter } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
import DetailGeneral from "./components/DetailGeneral.vue";
import DetailPayment from "./components/DetailPayment.vue";
import DetailSpecs from "./components/DetailSpecs.vue";
import DetailInstallation from "./components/DetailInstall.vue";
import LeadsNote from "./components/LeadsNote.vue";
import { useCommonStoreHook } from "@/store/modules/common";
import { ElLoading, ElMessage } from "element-plus";
import dayjs from "dayjs";
import ChangeResult from "@/components/ChangeResult/ChangeResult.vue";
import { hasAuth } from "@/router/utils";
import { removeElseFromObjArr } from "@/utils/common";
import InvoiceDownDialog from "@/components/InvoiceDownDialog";

const activeName = ref("general");
const detailGeneralRef = ref(null);
const detailSpecsRef = ref(null);
const canCreateAppointment = ref(false);
const cascaderKey = ref(null); // use to reflash current result cascader

const form = reactive({
  id: null,
  lead_id: null,
  received_date: "",
  result: "new",
  under_sell: null,
  estimated_comm: null,
  start_date: null,
  product_staff_id: null,
  on_product_date: null,
  ready_date: null,
  install_id: null,
  installation_date: null,
  completion_date: null,
  reason: null,
  payments: [],
  install: null,
  lead: null,
  amount: null
});
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const downInvoiceLoading = ref(false);
const productCategoryList = ref([]);
const subProductCategoryList = ref({});
const allStaffList = ref();
const currentTagResult = ref(null);
const dialogPanel = ref();
const serviceDetailDialogRef = ref();
const resultCascaderProps = {
  checkStrictly: true,
  emitPath: true
};
// for change result
const changeResultValue = ref(null);
const currentTableAction = ref([]);
const resultOptionHandled = ref([]);
const changeResultRef = ref(null);
const detailInstallationRef = ref(null);
const canEdit = computed(() => {
  if (
    form.result !== "cancelled" &&
    form.result !== "completion" &&
    editPage.value
  ) {
    return true;
  }
  return false;
});
const editPage = ref(true);
const firstProduct = computed(() => {
  if (form.lead && form.lead.product) {
    const commaIndex = form.lead.product.indexOf(",");
    if (commaIndex === -1) {
      return form.lead.product; // 没有逗号，返回整个字符串
    } else {
      return form.lead.product.substring(0, commaIndex); // 返回第一个逗号前的内容
    }
  }
  return "";
});

const props = defineProps({
  allStaff: {
    type: Array,
    default() {
      return [];
    }
  }
});

watch(
  () => props.allStaff,
  _newVal => {
    allStaffList.value = _newVal;
  },
  {
    deep: true,
    immediate: true
  }
);
const InvoiceDownDialogRef = ref(null);

function show(data = null, isEditPage = true) {
  editPage.value = isEditPage;
  initFormData();
  activeName.value = "general";
  if (data) {
    if (typeof data === "number") {
      form.id = data;
      reloadServiceInfo();
    } else {
      form.id = data.id;
      reloadServiceInfo();
    }
  } else {
    dialogVisible.value = false;
  }
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

// Get the current row  operation
function getCurrentAction(result) {
  let actionList = [];
  if (result && detailActionList[result]) {
    actionList = detailActionList[result];
  } else {
    actionList = detailActionList["default"];
  }
  if (!actionList.length) {
    return [];
  }
  // Filter actions by role
  const removeList = [];
  const canCancel = hasAuth("cancelService");
  if (!canCancel) {
    removeList.push("cancel");
  }
  if (removeList.length) {
    actionList = removeElseFromObjArr(actionList, removeList);
  }
  return actionList;
}

function defaultDataHandle(data) {
  Object.assign(form, data);
  changeResultValue.value = form.result;
  currentTagResult.value = form.result;
  currentTableAction.value = getCurrentAction(currentTagResult.value);
  canCreateAppointment.value = data.appointment.length ? false : true;
  if (
    ["new", "ready"].includes(data.result) &&
    hasAuth("addServiceAppointment")
  ) {
    canCreateAppointment.value = canCreateAppointment.value = data.appointment
      .length
      ? false
      : true;
  } else {
    canCreateAppointment.value = false;
  }
  resultOptionHandled.value = getResultOptions(form.result);
  dialogVisible.value = true;
}

// Gets a resultList based on the current lead result and the login role
function getResultOptions(result) {
  if (!result) {
    return [];
  }
  const resultsListNoHandle = JSON.parse(JSON.stringify(allResults));
  //Change the result disabled property based on the permission
  const resultsListHandle = [];
  let canToResultsList = [];
  if (hasAuth("changeServiceResult")) {
    switch (result) {
      case "new":
        canToResultsList = ["received"];
        break;
      case "received":
        canToResultsList = ["onProduction"];
        break;
      case "onProduction":
        canToResultsList = ["ready"];
        break;
      case "ready":
        canToResultsList = [];
        break;
      case "service":
        canToResultsList = ["ready"];
        break;
      case "outstanding":
        canToResultsList = ["completion"];
        break;
      // case "hold":
      //   canToResultsList = [
      //     "ready",
      //     "installation",
      //     "outstanding",
      //     "onProduction"
      //   ];
      //   break;
    }
  }
  resultsListNoHandle.map(resultItem => {
    if (
      canToResultsList.length &&
      canToResultsList.includes(resultItem.value)
    ) {
      resultItem.disabled = false;
    } else {
      resultItem.disabled = true;
    }
    resultsListHandle.push(resultItem);
  });
  return resultsListHandle;
}

function initFormData() {
  Object.keys(form).forEach(key => {
    form[key] = null;
    // switch (key) {
    //   default:
    //     form[key] = null;
    //     break;
    // }
  });
}

// 获取当前result icon
function getCurrentResultIcon(result) {
  if (!result) {
    return;
  }
  if (typeof result === "string") {
    return allResults.find(item => item.value === result)?.icon;
  } else if (result instanceof Array) {
    return allResults.find(item => item.value === result[0])?.icon;
  }
}

// Change service result
function handleResultChange(value, row) {
  const result = value[0];
  // If the remaining balance is changed from outstanding to completion, the remaining balance must be 0
  if (result == "completion" && calculatedAmount(row) !== 0) {
    const info = transformI18n("common.resultCompletionInfo");
    ElMessage.error(info);
    handleResultClose(false);
    return;
  }
  const data = {
    id: row.id,
    type: "service",
    result: result,
    row: row
  };
  changeResultRef.value
    .show(data)
    .then(overAction => {
      if (overAction) {
        hide();
        promise.resolve();
      } else {
        changeResultValue.value = currentTagResult.value;
      }
    })
    .catch(() => {
      changeResultValue.value = currentTagResult.value;
    });
}

function getDetailGeneral() {
  const newGeneralData = detailGeneralRef.value.getNewOrderGeneralInfo();
  form.product_staff_id = newGeneralData.product_staff_id;
  form.lead.sub_category_ids = newGeneralData.lead.sub_category_ids;
  form.received_date = newGeneralData.received_date;
  form.start_date = newGeneralData.start_date;
  form.estimated_comm = newGeneralData.estimated_comm;
  form.amount = newGeneralData.amount;

  if (form.received_date) {
    form.received_date = dayjs(form.received_date).format("YYYY-MM-DD");
  }
  if (form.start_date && form.start_date !== "Invalid date") {
    form.start_date = dayjs(form.start_date).format("YYYY-MM-DD");
  } else {
    form.start_date = null;
  }
}

// update service general info
function updateGeneral() {
  nextTick(() => {
    getDetailGeneral();
    loading.value = true;
    const sendForm = sendFormFilter(form);
    dialogLoading();
    serviceControllerUpdate(form.id, sendForm)
      .then(() => {
        dialogPanel.value.close();
        loading.value = false;
      })
      .catch(() => {
        dialogPanel.value.close();
        loading.value = false;
      });
  });
}
function updateOrderSpecs() {
  nextTick(() => {
    const specsInfo = detailSpecsRef.value.getNewInfo();
    loading.value = true;
    const sendForm = sendFormFilter(specsInfo);
    dialogLoading();
    serviceControllerSaveSpec(sendForm)
      .then(_res => {
        dialogPanel.value.close();
        loading.value = false;
        reloadServiceInfo();
      })
      .catch(() => {
        dialogPanel.value.close();
        loading.value = false;
      });
  });
}
function toUpdate() {
  if (["general", "payment", "contracts"].includes(activeName.value)) {
    updateGeneral();
  } else if (activeName.value == "orderSpecs") {
    updateOrderSpecs();
  }
}

function hide() {
  dialogVisible.value = false;
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: serviceDetailDialogRef.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function handleAction(actionName) {
  if (actionName == "cancel") {
    leadsCanceled();
  }
}

function leadsCanceled() {
  const data = {
    id: form.id,
    type: "service",
    result: "cancelled",
    row: form
  };
  changeResultRef.value
    .show(data)
    .then(overAction => {
      if (overAction) {
        hide();
        promise.resolve();
      }
    })
    .catch(() => {
      hide();
    });
}

function initData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  subProductCategoryList.value = basicData["subProductCategoryList"];
}
const changeTab = (_tab, _event) => {};

function reloadServiceInfo() {
  const _with = [
    "serviceFile",
    "payments",
    "install",
    "productSpec",
    "productStaff",
    "lead.client",
    "lead.category",
    "lead.apptSetter",
    "lead.spokenTo",
    "appointment.leads",
    "appointment.client",
    "supplierList.supplier",
    "serviceFile",
    "invoice"
  ];

  serviceControllerShow(form.id, _with).then(res => {
    const { data } = res;
    if (data) {
      defaultDataHandle(data);
    }
  });
}
function handleResultClose(isShow) {
  if (!isShow) {
    changeResultValue.value = currentTagResult.value;
    cascaderKey.value = Math.floor(Math.random() * 100);
  }
}

// down invoice file
function downInvoice() {
  InvoiceDownDialogRef.value.show(form).then(data => {
    console.log(data);
    if (data && data.type) {
      const id = form.id;
      downInvoiceLoading.value = true;
      serviceControllerGeneratorPDF(id, data)
        .then(res => {
          downInvoiceLoading.value = false;
          if (res.success && res.data) {
            window.open(res.data.url, "_blank");
            ElMessage.success(res.message);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_res => {
          downInvoiceLoading.value = false;
          ElMessage.error("Failed to generate invoice!");
        });
    }
  });
}

onMounted(() => {
  initData();
});
defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="Service Details"
    :close-on-click-modal="false"
    width="90%"
    ref="serviceDetailDialogRef"
    class="no-footer-dialog order-detail-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="detail-header">
        <div class="left">
          <div class="level-1">
            Service:<span class="ml-1">{{ form.id }}</span>
          </div>
          <div class="sub-title mr-2">
            <span class="mr-1 iconfont just-icon-client" />
            <span v-if="form.lead && form.lead.client"
              >{{ form.lead.client.given_name }}
              {{ form.lead.client.surname }}</span
            >
          </div>
          <div class="sub-title mr-2">
            <span class="mr-1 iconfont just-icon-product" />
            <span v-if="firstProduct">{{ firstProduct || "Empty" }}</span>
          </div>
        </div>
        <div class="action-box" v-if="form.result !== 'cancelled' && editPage">
          <el-button
            type="info"
            :loading="downInvoiceLoading"
            @click="downInvoice"
            class="ml-2"
          >
            <i
              class="mr-2 cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
            />
            Download</el-button
          >
          <el-button
            type="primary"
            :loading="loading"
            @click="toUpdate"
            class="ml-2"
            v-if="activeName == 'general' && canEdit"
          >
            {{ transformI18n("buttons.hssave") }}</el-button
          >
          <el-button
            type="primary"
            :loading="loading"
            @click="toUpdate"
            class="ml-2"
            v-else-if="activeName == 'orderSpecs' && canEdit"
          >
            {{ transformI18n("buttons.hssave") }}</el-button
          >
          <!-- change result button -->
          <div
            class="resultChangeChange ml-2"
            v-if="editPage && resultOptionHandled.length"
          >
            <span
              :class="
                'mr-1 iconfont just-icon-' +
                getCurrentResultIcon(changeResultValue)
              "
            />
            <el-cascader
              :options="resultOptionHandled"
              :show-all-levels="false"
              :model-value="changeResultValue"
              @change="value => handleResultChange(value, form)"
              :props="resultCascaderProps"
              :disabled="
                form.result === 'cancelled' || form.result === 'completion'
              "
              @visible-change="isShow => handleResultClose(isShow)"
              :key="cascaderKey"
            >
              <template #default="{ data }">
                <span class="cascader-label">
                  <span
                    :class="'mr-1 iconfont just-icon-' + data.icon"
                    v-if="data.icon"
                  />
                  {{ data.label }}</span
                >
              </template>
            </el-cascader>
          </div>
          <el-dropdown
            trigger="click"
            class="ml-2"
            v-if="
              editPage &&
              form.result !== 'cancelled' &&
              currentTableAction.length
            "
          >
            <span class="iconfont just-icon-gengduo" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  @click.stop="handleAction(actionItem.name)"
                  v-for="(actionItem, actionIndex) in currentTableAction"
                  :key="actionIndex"
                >
                  {{ actionItem.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="detail-content">
        <div class="detail-info">
          <el-tabs
            v-model="activeName"
            class="order-tabs"
            @tab-click="changeTab"
          >
            <el-tab-pane label="General" name="general">
              <DetailGeneral
                ref="detailGeneralRef"
                :allStaff="allStaff"
                :serviceInfo="form"
                :editPage="editPage"
              />
            </el-tab-pane>
            <el-tab-pane
              label="Payment"
              name="payment"
              v-if="hasAuth('viewServicePayment')"
            >
              <DetailPayment
                ref="detailPaymentRef"
                :allStaff="allStaff"
                :serviceInfo="form"
                :editPage="editPage"
                @reloadServiceInfo="reloadServiceInfo"
              />
            </el-tab-pane>
            <el-tab-pane
              label="Order Specs"
              name="orderSpecs"
              v-if="hasAuth('serviceSpec')"
            >
              <DetailSpecs
                ref="detailSpecsRef"
                :serviceInfo="form"
                :editPage="editPage"
                @reloadServiceInfo="reloadServiceInfo"
              />
            </el-tab-pane>
            <el-tab-pane label="Contracts and installation" name="contracts">
              <DetailInstallation
                ref="detailInstallationRef"
                :serviceInfo="form"
                :editPage="editPage"
                @reloadServiceInfo="reloadServiceInfo"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="notes-box">
          <LeadsNote
            ref="leadsNoteRef"
            :id="form.id"
            :type="'service'"
            :result="form.result"
            :row="form"
            :canCreateAppointment="canCreateAppointment && editPage"
            @updateDetail="reloadServiceInfo"
            :canEdit="canEdit"
          />
        </div>
      </div>
    </el-form>
    <ChangeResult ref="changeResultRef" />
    <InvoiceDownDialog ref="InvoiceDownDialogRef" />
  </el-dialog>
</template>
<style>
.order-detail-dialog {
  overflow: auto;
}

.order-detail-dialog .el-dialog__body .detail-info {
  min-width: 1080px;
  max-width: 95vw;
  overflow: auto;
}
</style>
<style lang="scss" scoped>
.help-block {
  font-size: 12px;
  color: #999;
}

.form-item-with {
  width: 80%;
}

.detail-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 15px;
  border-bottom: 1px solid #e9ebf0;

  .left {
    display: flex;
    align-items: center;
  }

  .sub-title {
    margin-left: 10px;
  }
}

.detail-content {
  display: flex;
  justify-content: space-between;

  .detail-info {
    flex-grow: 1;
    padding: 0 20px 30px;
    overflow: auto;
  }

  .notes-box {
    flex-shrink: 0;
    width: 450px;
    background: #fff;
    border: 1px solid #e9ebf0;
  }

  .item-block {
    padding: 0 0 20px;
    margin-top: 20px;
    border: 0;
    border-bottom: 1px solid #e9ebf0;

    &:last-child {
      border-bottom: none;
    }
  }

  .item-box {
    height: 50px;
    margin: 20px 0 0;

    .item-value {
      line-height: 34px;
    }
  }
}

.input-result-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.edit-client {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  .editIcon {
    display: none;
    margin-right: 10px;
  }

  &:hover {
    .editIcon {
      display: block;
    }
  }
}

.action-box {
  display: flex;
  align-items: center;
}

.noEdit {
  cursor: not-allowed;
}

@media screen and (width <= 1200px) {
  .order-detail-dialog .el-dialog__body {
    .detail-content {
      display: block;

      .detail-info {
        width: 100%;
        min-width: 100%;
      }

      .notes-box {
        width: 100%;
        min-width: 100%;
      }
    }
  }
}

@media screen and (width <= 768px) {
  .order-detail-dialog .el-dialog__body {
    .detail-header {
      display: block;
      height: auto;
      padding-bottom: 10px;

      .left {
        flex-wrap: wrap;
        margin-bottom: 10px;
      }

      .action-box {
        flex-wrap: wrap;
        justify-content: right;

        .el-button {
          margin-bottom: 5px;
        }
      }
    }
  }
}

@media screen and (width <= 480px) {
  .order-detail-dialog .el-dialog__body {
    .detail-info {
      padding: 10px 15px;
    }

    .notes-box {
      padding: 10px;
    }
  }
}

@media screen and (width <= 375px) {
  .order-detail-dialog .el-dialog__body {
    .detail-info {
      padding: 5px 10px;
    }
  }
}
</style>
