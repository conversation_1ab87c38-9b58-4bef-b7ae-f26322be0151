<script setup lang="ts">
import { transformI18n } from "@/plugins/i18n";
import { reactive, ref, onMounted, watch } from "vue";
import { useCommonStoreHook } from "@/store/modules/common";
import {
  columns,
  allResults,
  showFields,
  tableActionList,
  calculatedAmount
} from "./data.ts";
import { serviceControllerIndex } from "@/api/admin/service";
import DataTable from "@/components/DataTable";
import CirCleText from "@/components/CircleText";
import SearchIcon from "@/assets/svg/search.svg?component";
import EmptySpan from "@/components/EmptySpan";
import dayjs from "dayjs";
import ChangeResult from "@/components/ChangeResult/ChangeResult.vue";
import CreateAppointment from "@/components/CreateAppointment/CreateAppointment.vue";
import ResultDialog from "@/components/ResultDialog";
import { ElMessageBox, ElMessage } from "element-plus";
import { hasAuth } from "@/router/utils";
import {
  removeElseFromObjArr,
  handleAddress,
  getSupplierReceivedDays,
  getDaysFromToday
} from "@/utils/common";
import { formatTime } from "@/utils/form";

const productCategoryList = ref([]);
const resultDialogRef = ref(null);
const props = defineProps({
  args: {
    type: Object,
    default: () => ({})
  },
  loadDataFlag: {
    type: String,
    default: null
  }
});

// Listen for changes in props properties
watch(
  () => props.args,
  newValue => {
    if (newValue) {
      initData();
    }
  }
);
watch(
  () => props.loadDataFlag,
  (_newValue, _oldValue) => {
    loadData();
  }
);
const resultCascaderProps = {
  checkStrictly: true,
  emitPath: true
};
const baseForm = {
  product_category_id: null,
  date: null,
  otherParam: {
    clientKeyWords: "",
    subCategoryIds: null,
    staff: null
  }
};
const indexQuery = reactive({
  search: "",
  sort: "-id",
  filter: "",
  _with: "sales,source,category,spokenTo",
  withCount: ""
});
const form = reactive(baseForm);
const formRef = ref(null);
const serviceDataTableRef = ref(null);
const currentTagResult = ref(null);
const mv = ref();
const dialogPanel = ref();
const changeResult = ref(null);
const createAppointment = ref(null);
const newColumns = ref([...columns]);
const showTable = ref(false);
const cascaderKey = ref([]); // use to reflash current result cascader

const emit = defineEmits<{
  (e: "toDetail", val: Object): void;
  (e: "updateListSummary"): void;
}>();

function initData() {
  currentTagResult.value = props.args.tabName;
  showTable.value = false;
  // Initialize show fields data
  let checkShowFields = showFields["default"];
  if (showFields[props.args.tabName]) {
    checkShowFields = showFields[props.args.tabName];
  }
  newColumns.value = columns.map(column => {
    let hide = false;
    hide = !checkShowFields.includes(String(column.prop));
    return { ...column, hide };
  });
  showTable.value = true;
}

// Get the current row  operation todo
function getActionList(result, row) {
  let actionList = [];
  if (result && tableActionList[result]) {
    actionList = tableActionList[result];
  } else {
    actionList = tableActionList["default"];
  }
  if (!actionList.length) {
    return [];
  }
  // Filter actions by permission
  const removeList = [];
  const canCancel = hasAuth("cancelService");
  let canAddServiceAppointment = hasAuth("addServiceAppointment");
  if (canAddServiceAppointment) {
    canAddServiceAppointment =
      row.appointment && row.appointment.length ? false : true;
  }
  if (!canCancel) {
    removeList.push("cancel");
  }
  if (!canAddServiceAppointment) {
    removeList.push("createAppt");
  }
  if (removeList.length) {
    actionList = removeElseFromObjArr(actionList, removeList);
  }
  return actionList;
}

function onSearch() {
  loadData();
}

function onReset() {
  indexQuery.search = "";
  // formRef.value.resetFields();
  Object.keys(form).forEach(key => {
    switch (key) {
      case "otherParam":
        form[key] = {
          clientKeyWords: "",
          subCategoryIds: null,
          staff: null
        };
        break;
      default:
        form[key] = null;
        break;
    }
  });
  loadData();
}

function loadData() {
  serviceDataTableRef.value.loadData();
}

function tableQueryParams(form) {
  const filterInfo = [];
  let subCategoryIds = null;
  if (form.otherParam && form.otherParam.subCategoryIds) {
    subCategoryIds = form.otherParam.subCategoryIds.join(",");
  }
  Object.keys(baseForm).forEach(key => {
    if (form[key]) {
      if (key == "date") {
        const startDate = dayjs(form.date[0]).format("YYYY-MM-DD HH:mm:ss");
        const endDate = dayjs(form.date[1]).format("YYYY-MM-DD 23:59:59");
        filterInfo.push(`created_at:gte:${startDate}`);
        filterInfo.push(`created_at:lte:${endDate}`);
      } else if (key != "product" && key != "otherParam") {
        filterInfo.push(`${key}:eq:` + form[key]);
      }
    }
  });
  if (props.args?.tabName != "all") {
    filterInfo.push(`result:eq:${props.args?.tabName}`);
  }
  if (props.args?.companyRegion?.length) {
    filterInfo.push("company_region:in:" + props.args?.companyRegion.join("|"));
  }
  indexQuery._with =
    "serviceFile,payments,install,productSpec,productStaff,lead.client,lead.category,lead.apptSetter,lead.spokenTo, appointment.leads, appointment.client, supplierList.supplier, serviceFile, invoice, product";

  const param = Object.assign(
    { ...indexQuery },
    {
      filter: filterInfo.length === 0 ? indexQuery.filter : filterInfo.join(),
      otherParam: JSON.stringify({
        clientKeyWords: form.otherParam.clientKeyWords,
        subCategoryIds: subCategoryIds,
        staff: form.otherParam.staff
      })
    }
  );
  return param;
}

function initSearchData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
}

onMounted(() => {
  initData();
  initSearchData();
});

// action processing
function handleAction(row, actionName) {
  if (actionName == "edit") {
    emit("toDetail", row);
  } else if (actionName == "createAppt") {
    createdAppointment(row);
  } else if (actionName == "cancel") {
    closeOrder(row);
  }
  // to do
}

function createdAppointment(row) {
  if (row.appointment.length) {
    ElMessageBox.confirm(
      `There is already an <strong>outstanding</strong> you can modify it for this service?`,
      transformI18n("buttons.hsSystemPrompts"),
      {
        confirmButtonText: transformI18n("buttons.hsConfirm"),
        cancelButtonText: transformI18n("buttons.hsCancel"),
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    ).then(() => {
      createAppointment.value
        .show(row.appointment[0], "edit")
        .then(() => {
          loadData();
          emit("updateListSummary");
        })
        .catch(() => {
          dialogPanel.value.close();
        });
    });
  } else {
    createAppointment.value
      .show(row, "service")
      .then(() => {
        loadData();
        emit("updateListSummary");
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  }
}

function closeOrder(row) {
  const data = {
    id: row.id,
    type: "service",
    result: "cancelled",
    row
  };
  changeResult.value
    .show(data)
    .then(() => {
      loadData();
      emit("updateListSummary");
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}

function handleOrderResultChange(result, row, index) {
  // If the remaining balance is changed from outstanding to completion, the remaining balance must be 0
  if (result == "completion" && calculatedAmount(row) !== 0) {
    const info = transformI18n("common.resultCompletionInfo");
    ElMessage.error(info);
    if (index) {
      // to reset result cascader element
      handleResultClose(false, index);
    }
    return;
  }
  const data = {
    id: row.id,
    type: "service",
    result: result[0],
    row: row
  };
  changeResult.value
    .show(data)
    .then(() => {
      loadData();
      emit("updateListSummary");
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}

function stopBubbling() {
  return;
}

/** row click handlde */
function onRowClick(row) {
  if (!row) {
    return;
  }
  emit("toDetail", row);
}

// get current row result icon
function getCurrentResultIcon(result) {
  if (!result) {
    return;
  }
  const resultAllList = [
    ...allResults,
    {
      value: "cancelled",
      label: "Cancelled",
      icon: "cancelled"
    }
  ];
  if (typeof result === "string") {
    return resultAllList.find(item => item.value === result)?.icon;
  } else if (result instanceof Array) {
    return resultAllList.find(item => item.value === result[0])?.icon;
  }
}

function getResultOptions(result) {
  const resultsListNoHandle = JSON.parse(JSON.stringify(allResults));
  //Change the result disabled property based on the role
  const resultsListHandle = [];
  let canToResultsList = [];
  if (hasAuth("changeServiceResult")) {
    switch (result) {
      case "new":
        canToResultsList = ["received"];
        break;
      case "received":
        canToResultsList = ["onProduction"];
        break;
      case "onProduction":
        canToResultsList = ["ready"];
        break;
      case "ready":
        canToResultsList = [];
        break;
      case "service":
        canToResultsList = ["ready"];
        break;
      case "outstanding":
        canToResultsList = ["completion"];
        break;
      // case "hold":
      //   canToResultsList = [
      //     "onProduction",
      //     "ready",
      //     "installation",
      //     "completion"
      //   ];
      //   break;
    }
  }
  resultsListNoHandle.map(resultItem => {
    if (
      canToResultsList.length &&
      canToResultsList.includes(resultItem.value)
    ) {
      resultItem.disabled = false;
    } else {
      resultItem.disabled = true;
    }
    resultsListHandle.push(resultItem);
  });
  return resultsListHandle;
}

function handleResultClose(isShow, index) {
  if (!isShow) {
    cascaderKey.value[index] = Math.floor(Math.random() * 100);
  }
}

function handleSearch(val) {
  indexQuery.search = val;
  loadData();
}

function getHandleDate(date, row) {
  if (row.result == "completion" || row.result == "cancelled") {
    return dayjs(date).format("DD/MM/YYYY");
  }
  return getDaysFromToday(date) + "d";
}

// table list order change
function sortChange(column) {
  const order = column.order === "ascending" ? "" : "-";
  const orderField = column.prop;
  indexQuery.sort = order + orderField;
  loadData();
}

// function getQuantity(row) {
//   if (!(row.lead.category && row.product_spec && row.product_spec.length)) {
//     return 0;
//   }
//   let num = 0;
//   if (row.lead.category.name == "Roof") {
//     const productSpec = row.product_spec[0] && row.product_spec[0].spec;
//     num = productSpec["RoofArea(m2)"];
//   } else if (row.lead.category.name == "Landscaping") {
//     const productSpec = row.product_spec[0] && row.product_spec[0].spec;
//     num = productSpec["Site area(m2)"];
//   } else {
//     num = row.product_spec.length;
//   }
//   return num;
// }

defineExpose({ loadData });
</script>
<template>
  <div ref="mv" class="leads-list-page">
    <div class="flex justify-between w-full h-[50px] list-form">
      <el-form ref="formRef" :inline="true" :model="form">
        <el-form-item :label="'Product:'" prop="product" class="!w-[240px]">
          <el-select
            v-model="form.otherParam.subCategoryIds"
            placeholder="Select"
            clearable
            multiple
            filterable
            collapse-tags
            collapse-tags-tooltip
            @change="onSearch()"
          >
            <el-option-group
              v-for="group in productCategoryList"
              :key="group.name"
              :label="group.name"
            >
              <el-option
                v-for="item in group.products"
                :key="item.id"
                :label="item.name"
                :value="`${item.id}`"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item :label="'Create Date'" prop="data" class="!w-[300px]">
          <el-date-picker
            v-model="form.date"
            type="daterange"
            start-placeholder="Start date"
            end-placeholder="End date"
            format="DD/MM/YYYY"
            date-format="YYYY/MM/DD"
            class="mr-[10px]"
          />
        </el-form-item>
        <el-form-item :label="'Client:'" prop="otherParam.clientKeyWords">
          <el-input
            v-model="form.otherParam.clientKeyWords"
            autocomplete="off"
            @input="onSearch()"
            :placeholder="transformI18n('common.input')"
            clearable
          />
        </el-form-item>
        <el-form-item :label="'Staff:'" prop="otherParam.staff">
          <el-input
            v-model="form.otherParam.staff"
            autocomplete="off"
            @input="onSearch()"
            :placeholder="transformI18n('common.input')"
            clearable
          />
        </el-form-item>
        <el-form-item class="action-box">
          <el-button type="primary" @click="onSearch()">
            {{ transformI18n("common.filter") }}
          </el-button>
          <el-button @click="onReset()" type="info">{{
            transformI18n("common.clear")
          }}</el-button>
        </el-form-item>
      </el-form>
      <div class="search-box">
        <el-input
          ref="inputRef"
          v-model="indexQuery.search"
          size="large"
          clearable
          :placeholder="transformI18n('orders.searchOrders')"
          @input="handleSearch"
          class="table-search-input"
        >
          <template #prefix>
            <el-tooltip
              class="item"
              effect="dark"
              content="You can search by ID, Order ID, Service Result"
              placement="top"
            >
              <SearchIcon class="search-icon" />
            </el-tooltip>
          </template>
        </el-input>
      </div>
    </div>
    <data-table
      v-if="showTable"
      ref="serviceDataTableRef"
      :columns="newColumns"
      :source="serviceControllerIndex"
      :form="form"
      @sortChange="sortChange"
      :slotNames="[
        'client',
        'phone',
        'email',
        'address',
        'category',
        'product',
        'amount',
        'production_staff',
        'install_staff',
        'production_date',
        'ready_date',
        'installation_date',
        'hold_time',
        'supplier_days',
        'result',
        'operation',
        'created_at',
        'remainingBalance',
        'quantity',
        'updated_at'
      ]"
      :query-params="tableQueryParams"
      :header-cell-style="{
        background: 'var(--el-table-row-hover-bg-color)',
        color: 'var(--el-text-color-primary)'
      }"
      @handleRowClick="onRowClick"
    >
      <template #updated_at="{ row }">
        <span v-if="row.updated_at">{{
          formatTime(row.updated_at, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #client="{ row }">
        <span class="iconfont just-icon-client mr-2" />
        <span v-if="row.lead && row.lead.client && row.lead.client.given_name"
          >{{ row.lead.client.given_name }} {{ row.lead.client.surname }}</span
        >
        <span v-else-if="row.client && row.client.given_name"
          >{{ row.client.given_name }} {{ row.client.surname }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #phone="{ row }">
        <span v-if="row.lead && row.lead.client && row.lead.client.phone">{{
          row.lead.client.phone
        }}</span>
        <span v-else-if="row.client && row.client.phone">{{
          row.client.phone
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #email="{ row }">
        <span v-if="row.lead && row.lead.client && row.lead.client.email">{{
          row.lead.client.email
        }}</span>
        <span v-else-if="row.client && row.client.email">{{
          row.client.email
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #address="{ row }">
        <span v-if="row.lead && row.lead.address">
          {{ handleAddress(row.lead.address) }}</span
        >
        <span
          v-else-if="row.lead && row.lead.client && row.lead.client.address"
        >
          {{ handleAddress(row.lead.client.address) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #category="{ row }">
        <CirCleText
          :text="row.lead.category.name"
          v-if="row.lead && row.lead.category"
          :customBgColor="row.lead.category.color"
          :showNum="1"
        />
        <EmptySpan
          :text="row.lead && row.lead.category ? row.lead.category.name : ''"
        />
      </template>
      <template #quantity="{ row }">
        <EmptySpan :text="row.qty" />
      </template>
      <template #product="{ row }">
        <EmptySpan :text="row.product?.name || ''" />
      </template>
      <template #production_staff="{ row }">
        <div v-if="row.product_staff && row.product_staff.login_name">
          <CirCleText
            :text="row.product_staff.login_name"
            :customBgColor="row.product_staff.color"
          />
          {{ row.product_staff.login_name }}
        </div>
        <span v-else>Empty</span>
      </template>

      <template #install_staff="{ row }">
        <div v-if="row.install && row.install.login_name">
          <!-- <CirCleText :text="row.install.login_name" /> -->
          {{ row.install.login_name }}
        </div>
        <span v-else>Empty</span>
      </template>
      <!--  -->
      <template #amount="{ row }">
        <EmptySpan :text="row.amount ? '$ ' + row.amount : ''" />
      </template>
      <template #created_at="{ row }">
        <span v-if="row.created_at">
          {{ getHandleDate(row.created_at, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #ready_date="{ row }">
        <span v-if="row.ready_date">
          {{ getHandleDate(row.ready_date, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #production_date="{ row }">
        <span v-if="row.on_product_date">
          {{ getHandleDate(row.on_product_date, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #installation_date="{ row }">
        <span v-if="row.installation_date">
          {{ getHandleDate(row.installation_date, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #hold_time="{ row }">
        <span v-if="row.hold_time">
          {{ getHandleDate(row.hold_time, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #completion_date="{ row }">
        <EmptySpan :text="row.completion_date" />
      </template>
      <template #supplier_days="{ row }">
        <EmptySpan :text="getSupplierReceivedDays(row.supplier_list) + 'd'" />
      </template>
      <template #remainingBalance="{ row }">
        {{ calculatedAmount(row) }}
      </template>

      <template #result="{ row, $index }">
        <div class="result-container" @click.stop="stopBubbling()">
          <span
            v-if="row.result === 'cancelled' || row.result === 'completion'"
            class="table-draft-result"
          >
            <span
              :class="
                'mr-1 iconfont just-icon-' + getCurrentResultIcon(row.result)
              "
            />
            <span class="capitalize">{{ row.result }}</span>
          </span>
          <div v-else-if="row.result">
            <span
              :class="
                'mr-1 iconfont just-icon-' +
                getCurrentResultIcon(row.result) +
                ' input-result-icon'
              "
            />
            <el-cascader
              :options="getResultOptions(row.result)"
              :show-all-levels="false"
              :model-value="row.result"
              @change="value => handleOrderResultChange(value, row, $index)"
              @visible-change="isShow => handleResultClose(isShow, $index)"
              :props="resultCascaderProps"
              :key="cascaderKey[$index]"
            >
              <template #default="{ data }">
                <span class="cascader-label">
                  <span
                    :class="'mr-1 iconfont just-icon-' + data.icon"
                    v-if="data.icon"
                  />
                  {{ data.orderPageLabel || data.label }}</span
                >
              </template>
            </el-cascader>
          </div>
        </div>
      </template>

      <template #operation="{ row }">
        <div
          @click.stop="stopBubbling()"
          v-if="
            row.result != 'cancelled' && getActionList(row.result, row).length
          "
        >
          <el-dropdown
            trigger="click"
            v-if="row.result != 'cancelled' && row.result != 'completion'"
          >
            <span class="iconfont just-icon-gengduo" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  @click.stop="handleAction(row, actionItem.name)"
                  v-for="(actionItem, actionIndex) in getActionList(
                    row.result,
                    row
                  )"
                  :key="actionIndex"
                >
                  <span
                    :class="`iconfont just-icon-${actionItem.icon} mr-2 primary-color `"
                  />
                  {{ actionItem.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
    </data-table>
    <ChangeResult ref="changeResult" />
    <ResultDialog ref="resultDialogRef" />
    <CreateAppointment ref="createAppointment" />
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: 700;
}

.result-container {
  :deep(.el-input__wrapper) {
    width: 140px;
    border: 1px solid #e9ebf0;
    border-radius: 8px;
  }
}

.table-draft-result {
  display: flex;
  align-items: center;
}
</style>
./data.js
