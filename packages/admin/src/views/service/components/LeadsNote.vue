<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { notesC<PERSON><PERSON>er<PERSON>ist, notesControllerStore } from "@/api/admin/note";
import CirCleText from "@/components/CircleText";
import { Tickets } from "@element-plus/icons-vue";
import { transformI18n } from "@/plugins/i18n";
import { appointmentControllerIndex } from "@/api/admin/appointment";
import { statusColor, appointmentTypeText } from "@/views/appointment/data";
import dayjs from "dayjs";
import CreateAppointment from "@/components/CreateAppointment/CreateAppointment.vue";
import { ElMessage } from "element-plus";
import Detail from "@/views/appointment/detail.vue";
import cancelAppointmentDialog from "@/views/appointment/components/cancelAppointmentDialog.vue";
import completeAppointmentDialog from "@/views/appointment/components/completeAppointmentDialog.vue";
import { appointmentControllerConfirm } from "@/api/admin/appointment";

const props = defineProps({
  id: {
    type: Number,
    default: null
  },
  // note type :draft leads order service
  type: {
    type: String,
    default: "service"
  },
  // leads result
  result: {
    type: String,
    default: ""
  },
  row: {
    type: Object,
    default: () => {}
  },
  canCreateAppointment: {
    type: Boolean,
    default: false
  },
  canEdit: {
    type: Boolean,
    default: true
  }
});
let noteTypeList = [
  {
    label: "ALL",
    value: "all"
  },
  {
    label: "Activity",
    value: "activity"
  },
  {
    label: "Result",
    value: "result"
  },
  {
    label: "Comment",
    value: "comment"
  },
  {
    label: "Admin Notes",
    value: "adminNotes"
  }
];

const noteType = ref("all");
const activeName = ref("Notes");
const noteList = ref(null);
const commentSend = ref("");
const sendLoading = ref(false);
const loading = ref(false);
const appointmentsList = ref([]);
const appointmentsLoading = ref(false);
const createAppointmentRef = ref(null);
const AppointmentDetailRef = ref(null);
const cancelAppointmentDialogRef = ref(null);
const completeAppointmentDialogRef = ref(null);
const currentAppointmentId = ref(null);

const justResultIconList = [
  "draft",
  "new",
  "followup",
  "appointed",
  "sold",
  "cancelled",
  "new_order",
  "onProduction",
  "ready",
  "installation",
  "outstanding",
  "completion",
  "hold"
];
const emit = defineEmits<{
  (e: "updateDetail"): void;
}>();
watch(
  () => [props.id, props.type, props.result],
  val => {
    if (val) {
      initData();
    }
    if (val && props.type != "draft") {
      getappointmentsList();
    }
  },
  {
    immediate: true
  }
);

function onSend() {
  if (!props.id) {
    return;
  }
  sendLoading.value = true;
  notesControllerStore({
    type: props.type,
    comment: commentSend.value,
    relation_id: props.id
  })
    .then(res => {
      sendLoading.value = false;
      commentSend.value = "";
      if (res) {
        getNotes();
      }
    })
    .catch(_err => {
      sendLoading.value = false;
    });
}

function handleClick(tab, _event) {
  if (tab.name == "Notes") {
    noteType.value = "all";
    getNotes();
  }
}

function getNotes() {
  if (!props.id) {
    return;
  }
  loading.value = true;
  const param = {
    id: props.id,
    type: props.type,
    sort: "-id",
    filter: noteType.value === "all" ? "" : `type:eq:${noteType.value}`,
    with: "createdBy"
  };

  notesControllerList(
    param.type,
    param.id,
    param.filter,
    param.sort,
    param.with
  )
    .then(res => {
      loading.value = false;
      if (res.success === false && res.message) {
        ElMessage.error(res.message);
      }
      noteList.value = res.data || [];
    })
    .catch(_err => {
      loading.value = false;
    });
}

function getappointmentsList() {
  appointmentsList.value = [];
  if (!props.row.lead_id) {
    return;
  }
  appointmentsLoading.value = true;
  const search = "";
  const filter = `object_id:eq:${props.row.lead_id},service_id:eq:${props.row.id}`;
  const sort = "-id ";
  const _with =
    "leads,client,assignTo,leads.cm, leads.install,leads.serviceFile, service,leads.category";
  appointmentControllerIndex(search, filter, sort, _with)
    .then(res => {
      appointmentsLoading.value = false;
      const { data } = res;
      appointmentsList.value = data || [];
      if (!data) {
        if (res.success === false) {
          ElMessage.error(res.message || "Get appointments fail");
        }
      }
    })
    .catch(_err => {
      appointmentsLoading.value = false;
    });
}

function initData() {
  noteType.value = "all";
  activeName.value = "Notes";
  noteTypeList = [
    {
      label: "ALL",
      value: "all"
    },
    {
      label: "Activity",
      value: "activity"
    },
    {
      label: "Result",
      value: "result"
    },
    {
      label: "Comment",
      value: "comment"
    }
  ];
  getNotes();
}

function createdAppointment() {
  if (!props.id) {
    return;
  }
  createAppointmentRef.value.show(props.row, "service").then(() => {
    getappointmentsList();
    emit("updateDetail");
  });
}

function toRefreshAppointment() {
  getappointmentsList();
  emit("updateDetail");
}

// show appointment detail
function toAppointmentDetail(item) {
  if (item) {
    currentAppointmentId.value = item.id;
    AppointmentDetailRef.value.show(item, true).then(() => {
      currentAppointmentId.value = null;
    });
  }
}

// appointment detail Action
function detailAction(value) {
  const { type, item } = value;
  if (type == "edit") {
    const actionFromType = "appointment";
    createAppointmentRef.value.show(item, actionFromType).then(() => {
      toRefreshAppointment();
    });
  } else if (type == "cancel") {
    cancelAppointmentDialogRef.value.show(item).then(actionOver => {
      if (actionOver) {
        AppointmentDetailRef.value.hide();
        toRefreshAppointment();
      }
    });
  } else if (type === "complete") {
    completeAppointmentDialogRef.value.show(item).then(actionOver => {
      if (actionOver) {
        AppointmentDetailRef.value.hide();
        toRefreshAppointment();
      }
    });
  } else if (type === "confirm") {
    appointmentControllerConfirm(item.id).then(res => {
      if (res.success === false) {
        ElMessage.error(res.message);
      } else {
        ElMessage.success(res.message);
        AppointmentDetailRef.value.hide();
        toRefreshAppointment();
      }
    });
  }
}

onMounted(() => {});
defineExpose({});
</script>
<template>
  <div class="note-page">
    <el-tabs v-model="activeName" class="leads-note" @tab-click="handleClick">
      <el-tab-pane label="Notes" name="Notes">
        <el-radio-group v-model="noteType" @change="getNotes">
          <el-radio-button
            class="mr-1"
            :label="noteItem.value"
            v-for="noteItem in noteTypeList"
            :key="noteItem.value"
            >{{ noteItem.label }}</el-radio-button
          >
        </el-radio-group>

        <el-timeline
          style="max-width: 100%"
          class="time-line"
          v-loading="loading"
        >
          <el-timeline-item
            v-for="(noteItem, index) in noteList"
            :key="index"
            :icon="noteItem.icon"
            size="large"
          >
            <div class="note-item-content">
              <div class="item-icon">
                <div
                  class="iconfont-box"
                  v-if="justResultIconList.includes(noteItem.icon)"
                >
                  <span :class="'iconfont  just-icon-' + noteItem.icon" />
                </div>
                <CirCleText
                  :text="noteItem.created_by.login_name"
                  v-else
                  :size="24"
                  :customBgColor="
                    noteItem.created_by ? noteItem.created_by.color : null
                  "
                />
              </div>
              <div class="top">
                <span class="title">{{ noteItem.created_by.login_name }}</span>
                <span class="time">{{ noteItem.created_at }}</span>
              </div>
              <div class="main-content">
                {{ noteItem.comment }}
              </div>
              <div class="sub-content" v-if="noteItem.result_comment">
                <Tickets class="icon mr-2" />
                <span>{{ noteItem.result_comment }}</span>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-tab-pane>
      <el-tab-pane
        label="Appointment"
        name="second"
        v-if="props.type != 'draft'"
      >
        <div class="appointment-box">
          <div
            v-if="canCreateAppointment"
            class="create-appointment flex items-center"
            @click="createdAppointment"
          >
            <span class="iconfont just-icon-circle-add mr-[10px]" />
            <span> {{ transformI18n("appointment.createAppointments") }}</span>
          </div>
          <div
            v-loading="appointmentsLoading"
            class="appointment-list"
            v-if="appointmentsList && appointmentsList.length"
          >
            <div
              class="appointment-item"
              v-for="item in appointmentsList"
              :key="item.id"
              @click.stop="toAppointmentDetail(item)"
            >
              <div class="flex justify-between items-center">
                <div class="flex items-center" v-if="statusColor[item.status]">
                  <el-tag
                    :color="statusColor[item.status].bgColor"
                    class="!w-[12px] !h-[12px] mr-2 !p-[0] border-0 text-xs"
                  />
                  <span
                    :style="{ color: statusColor[item.status].color }"
                    class="text-xs"
                    >{{ statusColor[item.status].label }}</span
                  >
                </div>
                <div class="flex items-center">
                  <span class="mr-[20px]">#{{ item.object_id }}</span>
                  <span>{{ appointmentTypeText[item.type].label }}</span>
                </div>
              </div>
              <div class="level-1 date">
                {{ dayjs(item.date).format("h:mm A, D MMMM YYYY") }}
              </div>
              <div v-if="item.assign_to">
                <CirCleText
                  :text="item.assign_to.login_name"
                  :size="20"
                  :fontSize="'10px'"
                  :customBgColor="item.assign_to.color"
                />
                <span class="level-2">{{ item.assign_to.login_name }}</span>
              </div>
            </div>
          </div>
          <div v-else class="mt-[20px] empty">No appointment yet.</div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div
      class="note-action flex flex-col items-end"
      v-if="canEdit && activeName == 'Notes' && props.result != 'cancelled'"
    >
      <el-input
        v-model="commentSend"
        placeholder="Write your comments here.."
        autocomplete="off"
        :rows="2"
        type="textarea"
      />
      <el-button
        type="primary"
        :loading="sendLoading"
        @click="onSend"
        class="!w-90 mt-2 mr-[20px] mb-[20px]"
        >Send</el-button
      >
    </div>
    <CreateAppointment ref="createAppointmentRef" />
    <Detail
      ref="AppointmentDetailRef"
      @detailAction="detailAction"
      @refresh="toRefreshAppointment"
    />
    <cancelAppointmentDialog ref="cancelAppointmentDialogRef" />
    <completeAppointmentDialog ref="completeAppointmentDialogRef" />
  </div>
</template>
<style lang="scss" scoped>
.note-page {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  .note-action {
    flex-grow: 0;
    flex-shrink: 0;
    width: 100%;
    height: 120px;
    background-color: #f7f8f9;

    :deep(.el-textarea__inner) {
      padding: 20px 0 0 20px;
    }
  }

  .leads-note {
    flex-grow: 1;
  }
}

.note-item-content {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;

  .iconfont-box {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background: var(--el-color-primary);
    border-radius: 50%;

    .iconfont {
      font-size: 14px;
      color: #fff;
    }
  }

  .main-content {
    margin: 5px 0;
    font-weight: bold;
  }

  .sub-content {
    display: flex;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;

    .icon {
      width: 14px;
      height: 14px;
      font-size: 14px;
    }
  }
}

.top {
  display: flex;
  justify-content: space-between;
  height: 24px;
  line-height: 24px;
  text-align: center;

  .title {
    font-size: 12px;
    font-weight: 500;
  }

  .time {
    font-size: 12px;
    font-weight: 400;
  }
}

.item-icon {
  position: absolute;
  top: -5px;
  left: -35px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 34px;
  background: #fff;
}

.time-line {
  min-height: 100px;
  max-height: calc(100vh - 400px);
  padding: 10px 0 0 10px;
  overflow: auto;
}

// appointment list
.create-appointment {
  height: 60px;
  // align-items: center;
  padding: 0 10px;
  font-size: 16px;
  font-weight: 500;
  line-height: 20px;
  color: var(--el-color-primary);
  cursor: pointer;
  border: 1px solid #e9ebf0;
  border-radius: 8px;
}

.appointment-list {
  max-height: calc(100vh - 460px);
  overflow-y: auto;
  font-size: 16px;

  .appointment-item {
    display: flex;
    flex-direction: column;
    padding: 10px;
    margin: 20px 0;
    border: 1px solid #e9ebf0;
    border-radius: 8px;
  }

  .date {
    margin: 10px 0;
  }
}
</style>
