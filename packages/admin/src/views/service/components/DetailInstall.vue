<script setup lang="ts">
import { ref, onMounted, watch, reactive, computed } from "vue";
import addSupplierDialog from "@/components/AddSupplier/addSupplierDialog.vue";
import {
  orderControllerRemoveSupplier,
  orderControllerAddSupplier
} from "@/api/admin/order";
import { ElLoading, ElMessage } from "element-plus";
import CirCleText from "@/components/CircleText";
import { UploadFile } from "@/components/Upload";
import { hasAuth } from "@/router/utils";
import { transformI18n } from "@/plugins/i18n";
import {
  uploadControllerDestroy,
  uploadControllerShow
} from "@/api/admin/basic-data";
import { appointmentTypeText } from "@/views/appointment/data";
import { accountControllerIndex } from "@/api/admin/admin-account";
import { appointmentControllerAssign } from "@/api/admin/appointment";
import { serviceControllerUpdate } from "@/api/admin/service";
import { formatTime } from "@/utils/form";

const addSupplierDialogRef = ref(null);
const dialogPanel = ref();
const loadingDialogRef = ref();
const installId = ref(null);

const props = defineProps({
  serviceInfo: {
    type: Object,
    default() {
      return {};
    }
  },
  editPage: {
    type: Boolean,
    default: true
  }
});
const canEdit = computed(() => {
  if (
    serviceForm.result !== "cancelled" &&
    serviceForm.result !== "completion" &&
    props.editPage
  ) {
    return true;
  }
  return false;
});
const canUpdateDate = computed(() => {
  if (serviceForm.result !== "cancelled" && props.editPage) {
    return true;
  }
  return false;
});
const uploadInvoice = ref({
  type: "serviceInvoice",
  object_id: props.serviceInfo.lead_id
});
const uploadService = ref({
  type: "service",
  object_id: props.serviceInfo.lead_id
});
const staffOptions = ref([]);
const getStaffOptionsLoading = ref(false);
const assignToLoading = ref(false);
const serviceForm = reactive({
  id: null,
  lead_id: null,
  received_date: "",
  result: "new",
  under_sell: null,
  amount: null,
  start_date: null,
  product_staff_id: null,
  product_staff: null,
  on_product_date: null,
  ready_date: null,
  install_id: null,
  installation_date: null,
  completion_date: null,
  reason: null,
  payments: [],
  install: null,
  lead: null,
  order_id: null,
  appointment: null,
  followup_date: null,
  company_region: null
});
const canAssignTo = ref(false);
watch(
  () => props.serviceInfo,
  _newVal => {
    serviceInfoHandle(_newVal);
    uploadInvoice.value.object_id = props.serviceInfo.lead_id;
    uploadService.value.object_id = props.serviceInfo.lead_id;
  },
  {
    deep: true,
    immediate: true
  }
);

const emit = defineEmits<{
  (e: "reloadServiceInfo", val: Object): void;
}>();

function addSupplier() {
  addSupplierDialogRef.value
    .show({ id: serviceForm.order_id }, "service")
    .then(_res => {
      if (_res) {
        emit("reloadServiceInfo", {});
      }
    });
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: loadingDialogRef.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function removeSupplier(item) {
  const data = {
    order_supplier_id: item.id
  };
  dialogLoading();
  orderControllerRemoveSupplier(serviceForm.order_id, data)
    .then(res => {
      dialogPanel.value.close();
      if (res.success) {
        ElMessage.success(res.message);
      } else {
        ElMessage.error(res.message);
      }
      emit("reloadServiceInfo", {});
    })
    .catch(_err => {
      dialogPanel.value.close();
    });
}
function fileChange() {
  emit("reloadServiceInfo", {});
}
function previewFile(file) {
  const fileUrl = file.url;
  if (file.storage == "s3") {
    dialogLoading();
    uploadControllerShow(file.id)
      .then(res => {
        if (res.data.s3Url) {
          window.open(res.data.s3Url, "_blank");
        } else {
          window.open(res.data.url, "_blank");
        }
        dialogPanel.value.close();
      })
      .catch(_err => {
        dialogPanel.value.close();
      });
  } else {
    window.open(fileUrl, "_blank");
  }
}
// delete attachment
function delAttachment(file) {
  uploadControllerDestroy(file.id).then(res => {
    if (res.success) {
      ElMessage.success(res.message);
      emit("reloadServiceInfo", {});
    } else {
      ElMessage.error(res.message || "File deletion failure!");
    }
  });
}

function disabledDate(time, order_date) {
  return time.getTime() < new Date(order_date).getTime();
}

function supplierReceivedDateChange(value, supplier, type) {
  if (!(supplier && supplier.id)) {
    return;
  }
  let sendForm = {};
  if (type == "date") {
    sendForm = {
      order_supplier_id: supplier.id,
      supplier_received_date: value,
      note: supplier.note,
      supplier_amount: supplier.supplier_amount
    };
  } else if (type == "note") {
    sendForm = {
      order_supplier_id: supplier.id,
      note: value,
      supplier_received_date: supplier.supplier_received_date,
      supplier_amount: supplier.supplier_amount
    };
  } else {
    sendForm = {
      order_supplier_id: supplier.id,
      supplier_amount: value,
      note: supplier.note,
      supplier_received_date: supplier.supplier_received_date
    };
  }
  dialogLoading();
  orderControllerAddSupplier(serviceForm.order_id, sendForm)
    .then(res => {
      dialogPanel.value.close();
      if (res.success) {
        ElMessage.success(res.message);
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(_err => {
      dialogPanel.value.close();
    });
}

function handChangeStaffName(type) {
  const user_id = installId.value;
  if (!user_id) {
    return;
  }
  assignToLoading.value = true;
  if (!(serviceForm.appointment && serviceForm.appointment.length)) {
    ElMessage.error(
      "No appointment is in progress. Please create an appointment."
    );
    return;
  }
  const appointmentId = serviceForm.appointment[0].id;
  appointmentControllerAssign(appointmentId, { user_id: user_id }).then(res => {
    assignToLoading.value = false;
    if (res?.success) {
      ElMessage.success(res.message);
      const theOne = staffOptions.value.find(item => item.id === user_id);
      serviceForm[type] = theOne;
      emit("reloadServiceInfo", {});
    } else {
      ElMessage.error(res.message);
    }
  });
}

function getStaffOptions(query = "") {
  let filter = "";
  if (appointmentTypeText["install"]) {
    const roleNameListStr =
      appointmentTypeText["install"].filterRoleName.join("|");
    filter += "roles.name:in:" + roleNameListStr;
  }
  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  const size = 99;
  getStaffOptionsLoading.value = true;
  const search = query ? "%" + query + "%" : "";
  accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    serviceForm.company_region
  )
    .then(res => {
      const { data } = res;
      staffOptions.value = data || [];
      getStaffOptionsLoading.value = false;
    })
    .catch(_error => {
      getStaffOptionsLoading.value = false;
    });
}

function serviceInfoHandle(data) {
  canAssignTo.value = false;
  if (data && data.lead_id) {
    data = JSON.parse(JSON.stringify(data));
    Object.assign(serviceForm, data);
    if (data.appointment && data.appointment.length) {
      canAssignTo.value = hasAuth("assignAppointment") && props.editPage;
    }
    installId.value = serviceForm.install?.id;
  }
}

//update form data
function formChange() {
  const sendForm = {
    followup_date: serviceForm.followup_date,
    ready_date: serviceForm.ready_date,
    on_product_date: serviceForm.on_product_date,
    installation_date: serviceForm.installation_date,
    completion_date: serviceForm.completion_date
  };
  serviceControllerUpdate(serviceForm.id, sendForm)
    .then(res => {
      if (res.success) {
        ElMessage.success(res.message);
      } else {
        ElMessage.error(res.message);
      }
      emit("reloadServiceInfo", {});
    })
    .catch(() => {
      ElMessage.error("Update failed.");
    });
}

onMounted(() => {
  staffOptions.value = [];
  getStaffOptions();
});
</script>

<template>
  <div class="mb-[20px]">
    <div
      ref="loadingDialogRef"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="detail-content">
        <div class="general-info">
          <!-- Install -->
          <div class="item-title level-1">Service</div>
          <div class="border-b border-[#E9EBF0] mt-2.5 mb-2.5">
            <p class="text-[#2A2E34] text-sm font-medium mb-2.5">Install</p>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item label="Installer" prop="serviceForm.install.id">
                  <div class="item-value level-3" v-if="!canAssignTo">
                    <span v-if="serviceForm && serviceForm.install">
                      <CirCleText
                        :text="serviceForm.install.login_name"
                        :size="20"
                        :fontSize="'10px'"
                        :customBgColor="serviceForm.install.color"
                      />
                      {{ serviceForm.install.login_name }}
                    </span>
                    <span class="text-[#9DA7B8]" v-else>Not yet</span>
                  </div>
                  <div class="flex items-center" v-else>
                    <CirCleText
                      :text="serviceForm.install.login_name"
                      v-if="serviceForm.install"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="serviceForm.install.color"
                    />
                    <el-select
                      v-model="installId"
                      :placeholder="transformI18n('common.empty')"
                      filterable
                      clearable
                      class="w-[240px] mr-[10px]"
                      :loading="getStaffOptionsLoading"
                      :remote-method="getStaffOptions"
                      remote
                      @change="handChangeStaffName('install')"
                      :disabled="assignToLoading"
                    >
                      <el-option
                        v-for="item in staffOptions"
                        :key="item.id"
                        :label="item.login_name"
                        :value="item.id"
                      >
                        <div class="flex items-center">
                          <CirCleText
                            :text="item.login_name"
                            :size="20"
                            :fontSize="'10px'"
                            :customBgColor="item.color"
                          />
                          <span>{{ item.login_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Service Booked
                </div>
                <div class="item-value level-3" v-if="!canEdit">
                  <span v-if="serviceForm.installation_date">
                    {{
                      formatTime(serviceForm.installation_date, "DD/MM/YYYY")
                    }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="serviceForm.installation_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  OnProduction Date
                </div>
                <div class="item-value level-3" v-if="!canEdit">
                  <span v-if="serviceForm.on_product_date">
                    {{ formatTime(serviceForm.on_product_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="serviceForm.on_product_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Ready Date
                </div>
                <div class="item-value level-3" v-if="!canEdit">
                  <span v-if="serviceForm.ready_date">
                    {{ formatTime(serviceForm.ready_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="serviceForm.ready_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Followup Date
                </div>
                <div class="item-value level-3" v-if="!canEdit">
                  <span v-if="serviceForm.followup_date">
                    {{ formatTime(serviceForm.followup_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="serviceForm.followup_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Completion Date
                </div>
                <div class="item-value level-3" v-if="!canUpdateDate">
                  <span v-if="serviceForm.completion_date">
                    {{ formatTime(serviceForm.completion_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="serviceForm.completion_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
            </el-row>
          </div>
          <div class="border-b border-[#E9EBF0] mt-2.5 mb-2.5">
            <p
              class="w-full flex justify-between items-center text-[#2A2E34] text-sm font-medium mb-2.5"
            >
              <span>Supplier</span>
              <span
                v-if="hasAuth('addSupplierForService')"
                @click="addSupplier"
                class="cursor-pointer flex items-center pt-2 pb-2 pl-2.5 pr-2.5 rounded-md text-[#9B3CE5] bg-[#ffffff] border border-[#E9EBF0]"
              >
                <span class="iconfont just-icon-circle-add mr-[5px]" /> Add New
              </span>
            </p>
            <div class="pc-item">
              <el-row :gutter="10">
                <el-col :span="2" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">ID</div>
                </el-col>
                <el-col :span="5" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">
                    Supplier Name
                  </div>
                </el-col>
                <el-col :span="3" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">
                    Order Date
                  </div>
                </el-col>
                <el-col :span="4" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">
                    Supplier Received Date
                  </div>
                </el-col>
                <el-col :span="4" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">Amount</div>
                </el-col>
                <el-col :span="5" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">Note</div>
                </el-col>
              </el-row>
              <div
                v-if="
                  serviceForm &&
                  serviceForm.supplier_list &&
                  serviceForm.supplier_list.length
                "
              >
                <el-row
                  v-for="(supplier, index) in serviceForm?.supplier_list"
                  :key="index"
                  :gutter="10"
                >
                  <el-col :span="2" class="item-box">
                    <div class="item-value level-3">
                      {{ supplier.id }}
                    </div>
                  </el-col>
                  <el-col :span="5" class="item-box">
                    <div class="item-value level-3">
                      {{ supplier?.supplier?.name }}
                    </div>
                  </el-col>
                  <el-col :span="3" class="item-box">
                    <div class="item-value level-3" v-if="supplier.order_date">
                      {{ formatTime(supplier.order_date, "DD/MM/YYYY") }}
                    </div>
                  </el-col>
                  <el-col :span="4" class="item-box">
                    <el-date-picker
                      v-model="supplier.supplier_received_date"
                      type="date"
                      class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                      :placeholder="transformI18n('common.select')"
                      format="DD/MM/YYYY"
                      value-format="YYYY/MM/DD"
                      :disabled="!hasAuth('addSupplierForService')"
                      @change="
                        val => supplierReceivedDateChange(val, supplier, 'date')
                      "
                      :disabled-date="v => disabledDate(v, supplier.order_date)"
                    />
                  </el-col>
                  <el-col :span="4" class="item-box">
                    <el-input-number
                      v-model="supplier.supplier_amount"
                      :min="0"
                      :controls="false"
                      :disabled="!hasAuth('addSupplierForService')"
                      :placeholder="transformI18n('common.input')"
                      class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
                      @change="
                        val =>
                          supplierReceivedDateChange(
                            val,
                            supplier,
                            'supplier_amount'
                          )
                      "
                    />
                  </el-col>
                  <el-col :span="5" class="item-box">
                    <el-input
                      v-model="supplier.note"
                      :disabled="!hasAuth('addSupplierForService')"
                      :maxlength="30"
                      :controls="false"
                      :placeholder="transformI18n('common.input')"
                      class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
                      @change="
                        val => supplierReceivedDateChange(val, supplier, 'note')
                      "
                    />
                  </el-col>

                  <el-col :span="1">
                    <i
                      @click="removeSupplier(supplier)"
                      v-if="hasAuth('removeSupplierForService')"
                      class="cursor-pointer text-[#9B3CE5] iconfont just-icon-cancelled mr-[5px] ml-[10px]"
                    />
                  </el-col>
                </el-row>
              </div>
              <div v-else>
                <el-row>
                  <el-col :span="2" class="item-box">
                    <div class="item-value level-3">
                      <span class="text-[#9DA7B8]">Not yet</span>
                    </div>
                  </el-col>
                  <el-col :span="5" class="item-box">
                    <div class="item-value level-3">
                      <span class="text-[#9DA7B8]">Not yet</span>
                    </div>
                  </el-col>
                  <el-col :span="4" class="item-box">
                    <div class="item-value level-3">
                      <span class="text-[#9DA7B8]">Not yet</span>
                    </div>
                  </el-col>
                  <el-col :span="5" class="item-box">
                    <div class="item-value level-3">
                      <span class="text-[#9DA7B8]">Not yet</span>
                    </div>
                  </el-col>
                  <el-col :span="5" class="item-box">
                    <div class="item-value level-3">
                      <span class="text-[#9DA7B8]">Not yet</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div class="mobile-item">
              <div
                class="supplier-item-block"
                v-for="(supplier, index) in serviceForm.supplier_list"
                :key="index"
              >
                <div class="supplier-name flex justify-between">
                  <span>{{ supplier.id }}</span>
                  <span
                    class="iconfont just-icon-delete ml-2 primary-color"
                    v-if="hasAuth('removeSupplierForService')"
                    @click="removeSupplier(supplier)"
                  />
                </div>
                <el-row :gutter="15" class="px-[15px]">
                  <el-col :xs="12" :sm="6" class="supplier-item">
                    <div class="level-2">Supplier Name</div>
                    <div class="item-value level-3">
                      {{ supplier.supplier.name }}
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="6" class="supplier-item">
                    <div class="level-2">Order Date</div>

                    <div class="item-value level-3">
                      {{ formatTime(supplier.order_date, "DD/MM/YYYY") }}
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="6" class="supplier-item">
                    <div class="level-2">Supplier Received Date</div>

                    <el-date-picker
                      v-model="supplier.supplier_received_date"
                      :disabled="!hasAuth('addSupplierForService')"
                      type="date"
                      class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                      :placeholder="transformI18n('common.select')"
                      format="DD/MM/YYYY"
                      value-format="YYYY/MM/DD"
                      @change="
                        val => supplierReceivedDateChange(val, supplier, 'date')
                      "
                    />
                  </el-col>
                  <el-col :xs="12" :sm="6" class="supplier-item">
                    <div class="level-2">Amount</div>
                    <div class="item-value level-3">
                      <el-input-number
                        v-model="supplier.supplier_amount"
                        :min="0"
                        :disabled="!hasAuth('addSupplierForService')"
                        :controls="false"
                        :placeholder="transformI18n('common.input')"
                        class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
                        @change="
                          val =>
                            supplierReceivedDateChange(
                              val,
                              supplier,
                              'supplier_amount'
                            )
                        "
                      />
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="6" class="supplier-item">
                    <div class="level-2">Note</div>

                    <el-input
                      v-model="supplier.note"
                      :disabled="!hasAuth('addSupplierForService')"
                      :maxlength="30"
                      :controls="false"
                      clearable
                      :placeholder="transformI18n('common.input')"
                      class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
                      @change="
                        val => supplierReceivedDateChange(val, supplier, 'note')
                      "
                    />
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>

          <div
            class="item-title level-1 w-full mt-5 flex justify-between items-center"
          >
            <span>Service Attachments</span>
            <UploadFile
              v-if="hasAuth('uploadFileForService') && props.editPage"
              ref="uploadFileRef"
              :otherParam="uploadService"
              :btnTxt="'Upload'"
              :btnIcon="'just-icon-upload-line'"
              @fileChange="fileChange"
              :btnClass="'uploadBtn'"
            />
          </div>
          <div>
            <div
              class="empty mt-[20px]"
              v-if="
                !(serviceForm?.service_file && serviceForm.service_file.length)
              "
            >
              Not yet
            </div>
            <el-row
              class="flex justify-between items-center border text-base pl-2 pr-2 pb-1.5 pt-1.5 mt-5 border-[#E9EBF0] rounded-lg"
              v-for="(contract, index) in serviceForm?.service_file"
              :key="index"
            >
              <span
                ><i class="text-[#9B3CE5] iconfont just-icon-upload" />{{
                  contract.name
                }}</span
              >

              <div>
                <i
                  @click="previewFile(contract)"
                  class="cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
                />
                <span
                  v-if="hasAuth('delServiceUploadFile')"
                  class="iconfont just-icon-delete ml-2 primary-color"
                  @click="delAttachment(contract)"
                />
              </div>
            </el-row>
          </div>
          <div
            class="w-full mt-5 item-title level-1 flex justify-between items-center"
          >
            <span>Invoice Attachments</span>
            <UploadFile
              v-if="hasAuth('uploadFileForService') && props.editPage"
              ref="uploadFileRef"
              :otherParam="uploadInvoice"
              :btnTxt="'Upload'"
              :btnIcon="'just-icon-upload-line'"
              @fileChange="fileChange"
              :btnClass="'uploadBtn'"
            />
          </div>

          <div>
            <div
              class="empty mt-[20px]"
              v-if="!(serviceForm?.invoice && serviceForm.invoice.length)"
            >
              Not yet
            </div>
            <el-row
              class="flex justify-between items-center border text-base pl-2 pr-2 pb-1.5 pt-1.5 mt-5 border-[#E9EBF0] rounded-lg"
              v-for="(item, index) in serviceForm?.invoice"
              :key="index"
            >
              <span
                ><i class="text-[#9B3CE5] iconfont just-icon-upload" />{{
                  item.name
                }}</span
              >
              <div>
                <i
                  @click="previewFile(item)"
                  class="cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
                />
                <span
                  v-if="hasAuth('delServiceUploadFile')"
                  class="iconfont just-icon-delete ml-2 primary-color"
                  @click="delAttachment(item)"
                />
              </div>
            </el-row>
          </div>
        </div>
      </div>
    </div>
    <addSupplierDialog ref="addSupplierDialogRef" />
  </div>
</template>

<style lang="scss" scoped>
.detail-content {
  display: flex;
  justify-content: space-between;

  .general-info {
    flex-grow: 1;
    min-height: 350px;
    max-height: calc(100vh - 360px);
    padding: 0 10px;
    overflow: auto;
  }
}

.level-3 {
  line-height: 30px;
}

.pc-item {
  display: block;
}

.mobile-item {
  display: none;
}

@media screen and (width <= 768px) {
  .pc-item {
    display: none;
  }

  .mobile-item {
    display: block;

    .supplier-item-block {
      margin-top: 20px;
      border: 1px solid #e9ebf0;
      border-radius: 6px;
    }

    .supplier-item {
      padding: 5px 15px;
      margin: 0;
      border-radius: 6px;

      :deep(.el-date-editor.el-input) {
        width: 100%;
      }

      :deep(.el-input-number) {
        width: 100%;
      }
    }

    .supplier-name {
      padding: 5px 15px;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      background: #f7f8f9;
    }

    .level-2 {
      margin-bottom: 8px;
    }
  }
}

@media screen and (width <= 480px) {
  .base-item,
  .install-info,
  .supplier-list {
    .el-col {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  :deep(.el-date-editor.el-input) {
    width: 100%;
  }
}
</style>
