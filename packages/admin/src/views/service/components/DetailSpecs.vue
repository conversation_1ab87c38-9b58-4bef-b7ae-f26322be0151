<script setup lang="ts">
import { reactive, ref, onMounted, watch, nextTick } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { useCommonStoreHook } from "@/store/modules/common";
import AddIcon from "@/assets/svg/add.svg?component";

// order detail info
const form = reactive({
  id: null,
  order_id: null,
  lead_id: null,
  received_date: "",
  result: "new_order",
  under_sell: null,
  estimated_comm: null,
  start_date: null,
  product_staff_id: null,
  product_staff: null,
  cm_id: null,
  cm_booked_date: null,
  cm_received_date: null,
  on_product_date: null,
  ready_date: null,
  install_id: null,
  installation_date: null,
  completion_date: null,
  reason: null,
  finance_company: null,
  finance_type: null,
  finance_amount: null,
  finance_approved_date: null,
  finance_approved_no: null,
  payments: [],
  cm: null,
  install: null,
  lead: null,
  product_spec: null
});
const productCategoryList = ref([]); // all category info get by basic data
const subProductCategoryList = ref({});
const currentCategoryInfo = ref(null);
const currentProductMap = ref(null); //eg:{1:{id:1,name:"test"}}
const initSpecItem = ref(null); //{attrname:null,attrname2:null}
const specForm = ref([]);
const bColorList = ref([]);
const bmeshList = ref([]);
const selectProductList = ref([]);
const cascaderProps = {
  checkStrictly: true,
  emitPath: true
};
// const multipleLimitNum = ref(0);
const props = defineProps({
  serviceInfo: {
    type: Object,
    default() {
      return {};
    }
  },
  editPage: {
    type: Boolean,
    default: true
  }
});

watch(
  () => props.serviceInfo,
  _newVal => {
    init(_newVal);
  },
  {
    deep: true,
    immediate: true
  }
);
watch(
  () => productCategoryList.value,
  _newVal => {
    getCurrentCategoryInfo();
    specHandle();
  },
  {
    deep: true,
    immediate: true
  }
);

function init(data = null) {
  Object.assign(specForm, []);
  // multipleLimitNum.value = 0;
  if (data) {
    data = JSON.parse(JSON.stringify(data));
    Object.assign(form, data);
    if (form.lead) {
      const sub_category_ids_array = form.lead.sub_category_ids
        ? form.lead.sub_category_ids.split(",")
        : [];
      form.lead.sub_category_ids_array = sub_category_ids_array.map(Number);
    }
    getCurrentCategoryInfo();
    specHandle();
    nextTick(() => {
      if (currentCategoryInfo.value) {
        const selectList = [];
        currentCategoryInfo.value.products.map(item => {
          if (form.lead.sub_category_ids_array.includes(item.id)) {
            selectList.push(item);
          }
        });
        selectProductList.value = selectList;
      }
    });
  }
}

// get one specOption by categeory option and productid, and BColorListOption,BMeshListOption
function getSpecOption(option, product) {
  const productId = product.product_id;
  if (!option) {
    return [];
  }
  if (option.length == 0) {
    return [];
  }
  let res = [];
  if (option.length > 0) {
    res = option;
  } else {
    const product = currentCategoryInfo.value.products.find(
      item => item.id === productId
    );
    if (product && product.name && option[product.name]) {
      res = option[product.name];
    }
  }
  let handleOption = [];
  let returnEmpty = false;
  if (res.length) {
    for (const i in res) {
      if (res[i] === "BMeshColorList") {
        if (product.spec["Mesh type"]) {
          bmeshList.value.forEach(bt => {
            if (bt.value == product.spec["Mesh type"]) {
              handleOption = bt.children;
            }
          });
        } else {
          handleOption = [];
        }
        if (!handleOption.length) {
          returnEmpty = true;
        }
        break;
      } else if (res[i] === "BMeshList") {
        handleOption = bmeshList.value;
        break;
      } else if (res[i] === "BColorList") {
        handleOption = bColorList.value;
        break;
      }
    }
    if (handleOption.length) {
      return handleOption;
    }
  }
  if (returnEmpty) {
    return [];
  }
  return res;
}

// get current  categeory  default specItem form basic Data
function getOneInitSpecItem() {
  if (
    !(
      currentCategoryInfo.value &&
      currentCategoryInfo.value.spec &&
      currentCategoryInfo.value.spec.length
    )
  ) {
    return {};
  }
  const spec_list = {};
  currentCategoryInfo.value.spec.map(specItem => {
    spec_list[specItem.name] = null;
  });
  initSpecItem.value = spec_list;
}

// get a new product specItem
function getOneNewProductItem(productId) {
  // add product type
  let productName = "empty";
  // add attr productType to initSpecItem
  if (
    currentProductMap.value[productId] &&
    currentProductMap.value[productId].name
  ) {
    productName = currentProductMap.value[productId].name;
  }
  const spec = JSON.parse(JSON.stringify(initSpecItem.value));
  spec["productType"] = productName;
  const specFormItem = {
    product_id: productId,
    spec: spec
  };
  return specFormItem;
}

// del on product spec block
function delProduct(indexToRemove) {
  if (!specForm.value) {
    return;
  }
  specForm.value.splice(indexToRemove, 1);
}

// add one product spec block by productId
function addProduct(productId) {
  const specFormItem = getOneNewProductItem(productId);
  specForm.value.splice(specForm.value.length, 0, specFormItem);
}

// get current order Category basic data
function getCurrentCategoryInfo() {
  currentCategoryInfo.value = null;
  if (
    !productCategoryList.value.length ||
    !(form.lead && form.lead.product_category_id)
  ) {
    return;
  }
  currentCategoryInfo.value = productCategoryList.value.find(
    item => item.id === form.lead.product_category_id
  );
  if (currentCategoryInfo.value) {
    const productInofMap = {};
    currentCategoryInfo.value.products.map(item => {
      productInofMap[item.id] = item;
    });
    currentProductMap.value = productInofMap;
    getOneInitSpecItem();
  }
}

// Update and process the entire product_spec all form data
function specHandle() {
  if (!currentCategoryInfo.value) {
    return;
  }
  if (
    !(
      form.lead.sub_category_ids_array &&
      form.lead.sub_category_ids_array.length
    )
  ) {
    return;
  }
  const hasOldData = form.product_spec && form.product_spec.length;
  specForm.value = [];
  if (hasOldData) {
    specForm.value = getSpecFormByOldData();
  } else {
    specForm.value = defaultSpecForm();
  }
}

// when product_pec is [],set speclist by sub_category_ids_array
function defaultSpecForm() {
  const defaultSpecForm = [];
  form.lead.sub_category_ids_array.map(productId => {
    const specFormItem = getOneNewProductItem(productId);
    defaultSpecForm.push(specFormItem);
  });
  return defaultSpecForm;
}

function getSpecFormByOldData() {
  const specFormArray = [];
  form.product_spec.map(oldProductSpecItem => {
    const spec_list = {};
    for (const attrName in initSpecItem.value) {
      let oldValue = oldProductSpecItem.spec[attrName];
      if (Array.isArray(oldValue)) {
        oldValue = oldValue[0];
      }
      spec_list[attrName] = oldValue || null;
    }
    const productId = oldProductSpecItem.product_id;
    let productName = "empty";
    if (
      currentProductMap.value[productId] &&
      currentProductMap.value[productId].name
    ) {
      productName = currentProductMap.value[productId].name;
    }
    spec_list["productType"] = productName;
    specFormArray.push({
      product_id: productId,
      spec: spec_list
    });
  });
  return specFormArray;
}

// function subCategoryChange(newSubCategory) {
//   if (!newSubCategory.length) {
//     specForm.value = [];
//     return;
//   }
//   const oldSpecFormList = JSON.parse(JSON.stringify(specForm.value));
//   const newSpecFormList = [];
//   const leftProductIdList = [];
//   oldSpecFormList.map(item => {
//     if (newSubCategory.includes(item.product_id)) {
//       newSpecFormList.push(item);
//       leftProductIdList.push(item.product_id);
//     }
//   });
//   const needAddProductIds = newSubCategory.filter(
//     item => !leftProductIdList.includes(item)
//   );
//   if (needAddProductIds && needAddProductIds.length) {
//     needAddProductIds.map(pid => {
//       const specFormItem = getOneNewProductItem(pid);
//       newSpecFormList.push(specFormItem);
//     });
//   }
//   const selectList = [];
//   currentCategoryInfo.value.products.map(item => {
//     if (newSubCategory.includes(item.id)) {
//       selectList.push(item);
//     }
//   });
//   selectProductList.value = selectList;
//   specForm.value = newSpecFormList;
// }
function getNewInfo() {
  const list = [];
  specForm.value.map(item => {
    const handleSpec = [];
    for (const key in item.spec) {
      if (key == "productType") {
        continue;
      }
      handleSpec.push({
        name: key,
        value: item.spec[key]
      });
    }
    list.push({
      product_id: item.product_id,
      spec: handleSpec
    });
  });
  return {
    order_id: form.order_id,
    service_id: form.id,
    list: specForm.value
  };
}

function initData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  subProductCategoryList.value = basicData["subProductCategoryList"];
  bColorList.value = basicData["BColorList"];
  bmeshList.value = basicData["BMeshList"];
}
onMounted(() => {
  initData();
});
defineExpose({ getNewInfo });
</script>

<template>
  <div>
    <el-form
      :model="specForm"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="order-specs">
        <!-- header -->
        <el-row :gutter="10" v-if="form.lead">
          <el-col :xs="12" :sm="6" class="item-box">
            <el-form-item :label="transformI18n('productSpec.category')">
              <div class="item-value level-3">
                <span v-if="form.lead.category">
                  {{ form.lead.category.name }}</span
                >
                <span class="empty" v-else>Empty</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12" class="item-box product-select">
            <el-form-item
              :label="transformI18n('productSpec.product')"
              prop="sub_category_ids_array"
            >
              <el-select
                v-model="form.lead.sub_category_ids_array"
                :placeholder="transformI18n('common.empty')"
                multiple
                collapse-tags
                collapse-tags-tooltip
                :suffix-icon="AddIcon"
                :disabled="true"
              >
                <el-option
                  v-for="item in subProductCategoryList[
                    form.lead.product_category_id
                  ]"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- specs box -->
        <div
          v-if="
            specForm &&
            specForm.length &&
            currentCategoryInfo &&
            currentCategoryInfo.spec
          "
        >
          <div v-for="(product, index) in selectProductList" :key="index">
            <div
              class="text-[#2A2E34] text-lg mb-2.5 mt-2.5 flex items-center justify-between"
            >
              <span> {{ product.name }} </span>
              <span
                v-if="
                  props.editPage &&
                  serviceInfo.result != 'completion' &&
                  serviceInfo.result != 'cancelled'
                "
                @click="addProduct(product.id)"
                class="text-[#9B3CE5] cursor-pointer"
                ><i class="iconfont just-icon-circle-add mr-4" />Add Row</span
              >
            </div>

            <div
              class="product-specs-box"
              v-for="(productInfo, productIndex) in specForm"
              :key="productIndex"
            >
              <div
                v-if="product.id == productInfo.product_id"
                class="flex items-center overflow-scroll"
              >
                <!-- currentCategoryInfo.spec -->
                <span
                  class="item-box mr-2.5 min-w-[120px]"
                  v-for="(specItem, specIndex) in currentCategoryInfo.spec"
                  :key="specIndex"
                >
                  <el-form-item
                    :label="specItem.name"
                    v-if="specForm[productIndex].spec"
                  >
                    <el-select
                      v-if="specItem.type === 'select'"
                      class="specsBorder"
                      filterable
                      clearable
                      v-model="specForm[productIndex].spec[specItem.name]"
                    >
                      <el-option
                        v-for="item in getSpecOption(
                          specItem.options,
                          productInfo
                        )"
                        :label="item.label ? item.label : item"
                        :value="item.value ? item.value : item"
                        :key="item"
                      />
                    </el-select>
                    <el-radio-group
                      v-model="specForm[productIndex].spec[specItem.name]"
                      v-else-if="specItem.type == 'checkBox'"
                      class="specsBg"
                    >
                      <el-radio :label="'Yes'"> Yes</el-radio>
                      <div class="line" />
                      <el-radio :label="'No'">No</el-radio>
                    </el-radio-group>
                    <el-cascader
                      v-else-if="specItem.type == 'multiple'"
                      :options="getSpecOption(specItem.options, productInfo)"
                      :show-all-levels="false"
                      v-model="specForm[productIndex].spec[specItem.name]"
                      :props="cascaderProps"
                    />
                    <el-input
                      v-model="specForm[productIndex].spec[specItem.name]"
                      v-else
                      class="specsBorder"
                      placeholder="input"
                    />
                  </el-form-item>
                </span>
                <span
                  v-if="
                    serviceInfo.result != 'completion' &&
                    serviceInfo.result != 'cancelled' &&
                    props.editPage
                  "
                  class="cursor-pointer pt-5"
                  @click="delProduct(productIndex)"
                  ><i class="iconfont just-icon-delete-bin" />Delete</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.level-2 {
  margin-bottom: 8px;
}

.product-select {
  :deep(.el-select) {
    .el-select__tags-text {
      font-weight: 500;
      color: #2a2e34;
    }
  }
}

.order-specs {
  max-height: calc(100vh - 360px);
  padding-bottom: 20px;
  overflow: auto;
}

.product-specs-box {
  margin-bottom: 5px;
}

.specsBorder {
  width: 100%;
  padding: 0 10px;
  border: 1px solid #e9ebf0;
  border-radius: 6px;
}

.specsBg {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 0 10px;
  background-color: #f0f2f5;
  border-radius: 6px;

  .line {
    width: 1px;
    height: 20px;
    margin: 0 5px;
    border: 1px solid #e9ebf0;
  }

  .el-radio {
    margin: 0;
  }
}

@media screen and (width <= 480px) {
  .el-col {
    flex: 0 0 100%;
    max-width: 100%;
  }
}
</style>
