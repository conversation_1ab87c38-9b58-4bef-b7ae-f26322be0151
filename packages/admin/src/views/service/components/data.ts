import { transformI18n } from "@/plugins/i18n";

import blinds from "@/assets/image/blinds.png";
import garageDoor from "@/assets/image/garageDoor.png";
import landscaping from "@/assets/image/landscaping.png";
import roof from "@/assets/image/roof.png";
export { roof, blinds, garageDoor, landscaping };
export const indexDefaultQuery = {
  search: "",
  sort: ["-id"],
  filter: [],
  _with: [],
  withCount: []
};

export const columns: TableColumnList = [
  {
    label: "Service ID",
    width: "110",
    prop: "id",
    fixed: true,
    sortable: true
  },
  {
    label: "Order ID",
    width: "100",
    prop: "lead_id",
    sortable: true
  },
  {
    label: "Transno",
    prop: "transno",
    width: "120"
  },
  {
    label: "Client",
    prop: "lead.client.given_name",
    slot: "client",
    width: "140",
    sortable: true
  },
  {
    label: "Phone",
    prop: "lead.client.phone",
    width: "120",
    slot: "phone",
    sortable: true
  },
  // {
  //   label: "Email",
  //   prop: "lead.client.email",
  //   width: "140",
  //   slot: "email",
  //   sortable: true
  // },
  {
    label: "Address",
    prop: "lead.client.address",
    minWidth: "200",
    slot: "address",
    sortable: true
  },
  // {
  //   label: "Received Date",
  //   prop: "received_date",
  //   slot: "received_date",
  //   width: "130",
  //   sortable: true
  // },
  {
    label: "Create Date",
    prop: "created_at",
    slot: "created_at",
    width: "130",
    sortable: true
  },
  {
    label: "Category",
    prop: "lead.category.name",
    slot: "category",
    width: "130",
    sortable: true
  },
  {
    label: "Quantity",
    prop: "quantity",
    slot: "quantity",
    minWidth: "120"
  },
  {
    label: "Product",
    prop: "product.name",
    slot: "product",
    minWidth: "130",
    sortable: true
  },
  {
    label: "Service Amount",
    prop: "amount",
    slot: "amount",
    minWidth: "150",
    sortable: true
  },
  {
    label: "Production Staff",
    prop: "productStaff.login_name",
    slot: "production_staff",
    width: "150",
    sortable: true
  },
  {
    label: "Install Staff",
    prop: "install.login_name",
    slot: "install_staff",
    width: "150",
    sortable: true
  },
  {
    label: "On Production Date",
    prop: "on_product_date",
    slot: "production_date",
    width: "135"
  },
  {
    label: "Ready Date",
    prop: "ready_date",
    slot: "ready_date",
    width: "120"
  },
  {
    label: "Installation Date",
    prop: "installation_date",
    slot: "installation_date",
    width: "120"
  },
  {
    label: "Completion Date",
    prop: "completion_date",
    slot: "completion_date",
    width: "120"
  },
  {
    label: "Supplier days",
    slot: "supplier_days",
    width: "120"
  },
  {
    label: "Remaining Balance",
    prop: "remainingBalance",
    slot: "remainingBalance",
    width: "130"
  },
  {
    label: "Service Result",
    prop: "result",
    slot: "result",
    width: "160"
  },
  {
    label: "Followup Date",
    prop: "followup_date",
    slot: "followup_date",
    width: "140"
  },
  {
    label: "From old",
    prop: "from_old",
    width: "120"
  },
  {
    label: "Update At",
    prop: "updated_at",
    width: "120",
    slot: "updated_at",
    sortable: true
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    prop: "operation",
    fixed: "right",
    slot: "operation",
    align: "center"
  }
];

// the key require same with result value
export const showFields = {
  all: [
    "id",
    "lead_id",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "created_at",
    "lead.category.name",
    "quantity",
    "product",
    "amount",
    "productStaff.login_name",
    "result",
    "updated_at",
    "operation"
  ],
  received: [
    "id",
    "lead_id",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "created_at",
    "lead.category.name",
    "quantity",
    "product",
    "amount",
    "productStaff.login_name",
    "result",
    "updated_at",
    "operation"
  ],
  onProduction: [
    "id",
    "lead_id",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "product",
    "quantity",
    "amount",
    "productStaff.login_name",
    "on_product_date",
    "supplier_days",
    "result",
    "updated_at",
    "operation"
  ],
  ready: [
    "id",
    "lead_id",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "product",
    "quantity",
    "amount",
    "productStaff.login_name",
    "ready_date",
    "result",
    "updated_at",
    "operation"
  ],
  service: [
    "id",
    "lead_id",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "product",
    "quantity",
    "amount",
    "productStaff.login_name",
    "install.login_name",
    "installation_date",
    "result",
    "updated_at",
    "operation"
  ],
  outstanding: [
    "id",
    "lead_id",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "product",
    "quantity",
    "amount",
    "productStaff.login_name",
    "remainingBalance",
    "result",
    "updated_at",
    "operation"
  ],
  completion: [
    "id",
    "lead_id",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "product",
    "quantity",
    "amount",
    "productStaff.login_name",
    "created_at",
    "completion_date",
    "result",
    "updated_at"
  ],
  // hold: [
  //   "id",
  //   "lead_id",
  //   "lead.client.given_name",
  //   "lead.client.phone",
  //   "lead.client.address",
  //   "lead.category.name",
  //   "product",
  //   "quantity",
  //   "amount",
  //   "productStaff.login_name",
  //   "hold_time",
  //   "result",
  //   "operation"
  // ],
  cancelled: [
    "id",
    "lead_id",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "product",
    "quantity",
    "amount",
    "productStaff.login_name",
    "hold_time",
    "result",
    "updated_at"
  ],
  default: [
    "id",
    "lead_id",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "product",
    "quantity",
    "amount",
    "productStaff.login_name",
    "result",
    "updated_at",
    "operation"
  ]
};

export const tableActionList = {
  new: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" },
    { label: "Create Appointment", icon: "add", name: "createAppt" }
  ],
  ready: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" },
    { label: "Create Appointment", icon: "add", name: "createAppt" }
  ],
  outstanding: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" }
  ],
  completion: [],
  cancelled: [],
  default: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" }
  ]
};

export const detailActionList = {
  new: [{ label: "Cancel", icon: "cancel", name: "cancel" }],
  received: [{ label: "Cancel", icon: "cancel", name: "cancel" }],
  ready: [{ label: "Cancel", icon: "cancel", name: "cancel" }],
  outstanding: [{ label: "Cancel", icon: "cancel", name: "cancel" }],
  completion: [],
  cancelled: [],
  default: [{ label: "Cancel", icon: "cancel", name: "cancel" }]
};

export const allResults = [
  {
    value: "new",
    label: "New Service",
    icon: "new_order",
    disabled: true
  },
  {
    value: "received",
    label: "Plan Received",
    icon: "appointed",
    disabled: true
  },
  {
    value: "onProduction",
    label: "On Production",
    icon: "onProduction",
    disabled: true
  },
  {
    value: "ready",
    label: "Ready",
    icon: "ready",
    disabled: true
  },
  {
    value: "service",
    label: "Service",
    icon: "service",
    disabled: true
  },
  {
    value: "outstanding",
    label: "Outstanding",
    icon: "outstanding",
    disabled: true
  },
  {
    value: "completion",
    label: "Completion",
    icon: "completion",
    disabled: true
  }
  // {
  //   value: "hold",
  //   label: "On Hold",
  //   icon: "hold",
  //   disabled: true
  // }
  // {
  //   value: "cancelled",
  //   label: "Cancelled",
  //   icon: "cancelled",
  //   disabled: true
  // }
];

export const typeOption = [
  "Cash",
  "Cheque",
  "Credit",
  "CreditCard",
  "Direct Tsf",
  "Finn",
  "Finn Cost",
  "Refund"
];

export function calculatedAmount(item) {
  let paid_amount = 0;
  const payments = item.payments;
  paid_amount = payments.reduce((acc, cur) => {
    let curAmount = 0;
    if (cur.type == "Refund") {
      curAmount = -cur.amount;
    } else if (cur.type != "Unpaid") {
      curAmount = cur.amount;
    }
    return parseFloat(acc.toFixed(2)) + parseFloat(curAmount.toFixed(2));
  }, 0);
  const outstanding =
    parseFloat(item.amount ? item.amount.toFixed(2) : 0) -
    parseFloat(paid_amount.toFixed(2));
  return outstanding ? parseFloat(outstanding.toFixed(2)) : outstanding;
}
