<script setup lang="ts">
import { reactive, ref, onMounted, watch, computed } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { useCommonStoreHook } from "@/store/modules/common";
import CirCleText from "@/components/CircleText";
// import dayjs from "dayjs";
import { allResults } from "./data";
import { formatTime } from "@/utils/form";
import { hasAuth } from "@/router/utils";
import EmptySpan from "@/components/EmptySpan";

const canEditManagement = computed(() => {
  return hasAuth("editServiceManagement") && props.editPage;
});
const canEdit = computed(() => {
  if (
    serviceGeneralForm.result !== "cancelled" &&
    serviceGeneralForm.result !== "completion" &&
    props.editPage
  ) {
    return true;
  }
  return false;
});

const client = ref(null);
const serviceGeneralForm = reactive({
  id: null,
  lead_id: null,
  received_date: "",
  result: "new",
  under_sell: null,
  amount: null,
  start_date: null,
  product_staff_id: null,
  product_staff: null,
  on_product_date: null,
  ready_date: null,
  install_id: null,
  installation_date: null,
  completion_date: null,
  reason: null,
  payments: [],
  install: null,
  lead: null
});
const formRef = ref(null);
const productCategoryList = ref([]);
const subProductCategoryList = ref({});
const sourceList = ref([]);
const subSourceList = ref({});
const promotionList = ref([]);
const salesList = ref([]);
const spokenToUserList = ref([]);
const apptSetterList = ref([]);
const productStaffList = ref([]);
const allStaffList = ref();
const staffName = reactive({
  saleName: { login_name: null, color: null },
  apptSetterName: { login_name: null, color: null },
  spokenTo: { login_name: null, color: null },
  productStaff: { login_name: null, color: null }
});
// const emit = defineEmits<{
//   (e: "createWithSameClient", val: Object): void;
// }>();

const props = defineProps({
  allStaff: {
    type: Array,
    default() {
      return [];
    }
  },
  serviceInfo: {
    type: Object,
    default() {
      return {};
    }
  },
  editPage: {
    type: Boolean,
    default: true
  }
});

watch(
  () => props.allStaff,
  _newVal => {
    allStaffList.value = _newVal;
    updateStaff();
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.serviceInfo,
  _newVal => {
    init(_newVal);
  },
  {
    deep: true,
    immediate: true
  }
);

function updateStaff() {
  if (allStaffList.value) {
    salesList.value = [];
    spokenToUserList.value = [];
    apptSetterList.value = [];
    productStaffList.value = [];
    allStaffList.value.map(item => {
      if (item.roles?.length) {
        item.roles.map(role => {
          if (role.name === "sale_manager") {
            salesList.value.push(item);
          } else if (role.name === "lead_taker_user") {
            spokenToUserList.value.push(item);
          } else if (role.name === "appointment_setter") {
            apptSetterList.value.push(item);
          } else if (role.name === "production_assistant") {
            productStaffList.value.push(item);
          }
        });
      }
    });
  }
}

function getResultIcon(result) {
  if (!result) {
    return;
  }
  return allResults.find(item => item.value === result)?.icon;
}

// function handChangeStaffName(id, nameType) {
//   const item = allStaffList.value.find(item => item.id === id);
//   if (item) {
//     staffName[nameType] = { login_name: item.login_name, color: item.color };
//   }
// }

// Use return to detail page update inof
function getNewOrderGeneralInfo() {
  if (serviceGeneralForm.lead) {
    serviceGeneralForm.lead.sub_category_ids =
      serviceGeneralForm.lead.sub_category_ids_array.join(",");
  }
  console.log("serviceGeneralForm", serviceGeneralForm);
  return serviceGeneralForm;
}

function init(data = null) {
  if (data && data.lead_id) {
    data = JSON.parse(JSON.stringify(data));
    Object.assign(serviceGeneralForm, data);
    if (data.lead) {
      client.value = data.lead.client;
    }

    if (serviceGeneralForm.lead) {
      staffName.saleName = {
        login_name: serviceGeneralForm.lead.sales?.login_name,
        color: serviceGeneralForm.lead.sales?.color
      };
      staffName.apptSetterName = {
        login_name: serviceGeneralForm.lead.appt_setter?.login_name,
        color: serviceGeneralForm.lead.appt_setter?.color
      };
      staffName.spokenTo = {
        login_name: serviceGeneralForm.lead.spoken_to?.login_name,
        color: serviceGeneralForm.lead.spoken_to?.color
      };
      serviceGeneralForm.lead.sub_category_ids_array = serviceGeneralForm.lead
        .sub_category_ids
        ? serviceGeneralForm.lead.sub_category_ids.split(",")
        : [];
      serviceGeneralForm.lead.sub_category_ids_array =
        serviceGeneralForm.lead.sub_category_ids_array.map(Number);
    }
    staffName.productStaff = {
      login_name: serviceGeneralForm.product_staff?.login_name,
      color: serviceGeneralForm.product_staff?.color
    };
  }
}

function initData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  subProductCategoryList.value = basicData["subProductCategoryList"];
  sourceList.value = basicData["sourceList"];
  subSourceList.value = basicData["subSourceList"];
  promotionList.value = basicData["promotionList"];
}
onMounted(() => {
  initData();
});
defineExpose({ getNewOrderGeneralInfo });
</script>

<template>
  <div>
    <el-form
      ref="formRef"
      :model="serviceGeneralForm"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="detail-content">
        <div class="general-info">
          <div class="item-block">
            <!-- client info -->
            <div class="item-title level-1">
              {{ transformI18n("client.client") }}
            </div>
            <el-row :gutter="10" v-if="client">
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="level-2">{{ transformI18n("client.client") }}</div>
                <div class="item-value level-3 edit-client">
                  <span>
                    {{ client.title }} {{ client.surname }}
                    {{ client.given_name }}
                  </span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="transformI18n('leads.clientPhone')">
                  <div class="item-value level-3 edit-client">
                    {{ client.phone }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="level-2">
                  {{ transformI18n("leads.backupContact") }}
                </div>
                <div class="item-value level-3 edit-client">
                  <span
                    v-if="
                      client.sec_title ||
                      client.sec_surname ||
                      client.sec_given_name
                    "
                  >
                    {{ client.sec_title }} {{ client.sec_surname }}
                    {{ client.sec_given_name }}
                  </span>
                  <span v-else class="empty">{{
                    transformI18n("common.empty")
                  }}</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.backupContactPhone')"
                  prop="sec_phone"
                >
                  <div class="item-value level-3 edit-client">
                    {{ client.sec_phone }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="item-box">
                <el-form-item
                  :label="transformI18n('client.address')"
                  prop="address"
                >
                  <div class="item-value level-3 edit-client">
                    <span
                      v-if="
                        serviceGeneralForm.lead &&
                        serviceGeneralForm.lead.address
                      "
                    >
                      {{ serviceGeneralForm.lead.address }}</span
                    >
                    <span v-else-if="client && client.address">
                      {{ client.address }}</span
                    >
                    <span v-else class="empty"> Empty</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="transformI18n('client.email')">
                  <div class="item-value level-3 edit-client">
                    <span v-if="client && client.email">
                      {{ client.email }}</span
                    >
                    <span v-else class="empty"> Empty</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- management -->
          <div class="item-block">
            <div class="item-title level-1">
              {{ transformI18n("leads.management") }}
            </div>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="8" class="item-box">
                <el-form-item
                  :label="'Production Staff'"
                  prop="product_staff_id"
                >
                  <div class="flex items-center">
                    <CirCleText
                      :text="staffName.productStaff.login_name"
                      v-if="staffName.productStaff"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="staffName.productStaff.color"
                    />
                    <EmptySpan
                      :text="
                        staffName.productStaff &&
                        staffName.productStaff.login_name
                          ? staffName.productStaff.login_name
                          : ''
                      "
                    />
                  </div>

                  <!-- <el-select
                    v-else
                    v-model="serviceGeneralForm.product_staff_id"
                    :placeholder="transformI18n('common.empty')"
                    filterable
                    @change="val => handChangeStaffName(val, 'productStaff')"
                  >
                    <el-option
                      v-for="item in productStaffList"
                      :key="item.id"
                      :label="item.login_name"
                      :value="item.id"
                    >
                      <div class="flex items-center">
                        <CirCleText
                          :text="item.login_name"
                          :size="20"
                          :fontSize="'10px'"
                          :customBgColor="item.color"
                        />
                        <span>{{ item.login_name }}</span>
                      </div>
                    </el-option>
                  </el-select> -->
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="8" class="item-box">
                <el-form-item :label="transformI18n('common.result')">
                  <div class="flex items-center">
                    <span
                      :class="
                        'mr-1  input-result-icon iconfont just-icon-' +
                        getResultIcon(serviceGeneralForm.result)
                      "
                    />
                    <span class="capitalize">{{
                      transformI18n(
                        `orders.result_${serviceGeneralForm.result}`
                      )
                    }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="8" class="item-box">
                <el-form-item :label="'Service Received Date'">
                  <div
                    class="item-value level-3"
                    v-if="!canEditManagement || !canEdit"
                  >
                    <span v-if="serviceGeneralForm.received_date">{{
                      formatTime(serviceGeneralForm.received_date, "DD/MM/YYYY")
                    }}</span>
                    <span v-else class="empty">Empty</span>
                  </div>
                  <el-date-picker
                    v-model="serviceGeneralForm.received_date"
                    type="date"
                    :placeholder="'Select'"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    v-else
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="8" class="item-box">
                <el-form-item :label="'Service Amount'" prop="amount">
                  <EmptySpan
                    :text="serviceGeneralForm.amount + ''"
                    v-if="!canEditManagement || !canEdit"
                  />
                  <el-input
                    v-else
                    v-model="serviceGeneralForm.amount"
                    :placeholder="transformI18n('common.input')"
                  >
                    <template #prefix v-if="serviceGeneralForm.amount"
                      ><span class="mr-2">$</span>
                    </template></el-input
                  >
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="8" class="item-box">
                <el-form-item :label="'Start Date'" prop="start_date">
                  <div
                    class="item-value level-3"
                    v-if="!canEditManagement || !canEdit"
                  >
                    <span v-if="serviceGeneralForm.start_date">{{
                      formatTime(serviceGeneralForm.start_date, "DD/MM/YYYY")
                    }}</span>
                    <span v-else class="empty">Empty</span>
                  </div>
                  <el-date-picker
                    v-model="serviceGeneralForm.start_date"
                    type="date"
                    :placeholder="'ASAP'"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    v-else
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="8" class="item-box">
                <div class="level-2">Related Leads</div>
                <div class="item-value level-3">
                  <span v-if="serviceGeneralForm.lead">{{
                    serviceGeneralForm.lead.id
                  }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
          <!-- Product Requirements -->
          <div class="item-block">
            <div class="item-title level-1">
              {{ transformI18n("leads.productRequirements") }}
            </div>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="transformI18n('productSpec.category')">
                  <div class="item-value level-3">
                    <span
                      v-if="
                        serviceGeneralForm.lead &&
                        serviceGeneralForm.lead.category
                      "
                    >
                      {{ serviceGeneralForm.lead.category.name }}</span
                    >
                    <span class="empty" v-else>Empty</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="item-box product-select">
                <el-form-item
                  :label="transformI18n('productSpec.product')"
                  prop="sub_category_ids_array"
                >
                  <div class="item-value level-3">
                    <span> {{ serviceGeneralForm.lead.product }}</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" class="item-box">
                <el-form-item :label="'Product Detail'" prop="lead.comment">
                  <el-input
                    v-model="serviceGeneralForm.lead.comment"
                    v-if="serviceGeneralForm.lead"
                    :placeholder="transformI18n('leads.commentsHoldDetail')"
                    autocomplete="off"
                    :rows="3"
                    maxlength="50"
                    type="textarea"
                    readonly
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.level-2 {
  margin-bottom: 8px;
}

.detail-content {
  display: flex;
  justify-content: space-between;

  .general-info {
    flex-grow: 1;
    min-height: 350px;
    max-height: calc(100vh - 360px);
    padding: 0 10px;
    overflow: auto;
  }

  .notes-box {
    flex-shrink: 0;
    width: 450px;
    background: #fff;
    border: 1px solid #e9ebf0;
  }

  .item-block {
    padding: 0 0 20px;
    margin-top: 20px;
    border: 0;
    border-bottom: 1px solid #e9ebf0;

    &:last-child {
      border-bottom: none;
    }
  }

  .item-box {
    min-height: 50px;
    margin: 20px 0 0;

    .item-value {
      line-height: 1.5em;
    }
  }
}

.input-result-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.edit-client {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.noEdit {
  cursor: not-allowed;
}

@media screen and (width <= 480px) {
  .el-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .detail-content .item-box {
    margin-top: 10px;
  }
}
</style>
