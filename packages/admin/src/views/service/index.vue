<script setup lang="ts">
import { ref, onMounted, nextTick, reactive } from "vue";
import { transformI18n } from "@/plugins/i18n";
import ListTable from "./components/listTable.vue";
import ServiceDetail from "./detail.vue";
import ResultDialog from "@/components/ResultDialog";
import { accountControllerIndex } from "@/api/admin/admin-account";
import { serviceControllerGetSummaryInfo } from "@/api/admin/service";
import MapView from "@/views/leads/components/LeadsMapView.vue";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const companyRegion = ref([]);
const mapCompanyRegion = ref([]);
const activeName = ref("tab_all");
// current tab List total num and show beside the tab label
const listCount = ref({
  review: 0,
  all: 0,
  new: 0,
  onProduction: 0,
  ready: 0,
  service: 0,
  outstanding: 0,
  completion: 0,
  cancelled: 0
});
const MapMarksNum = ref(0);
const mapViewRef = ref(null);
const changeTab = (tab, event) => {
  nextTick(() => {
    if (activeName.value === "mapView" && mapViewRef.value) {
      mapViewRef.value.initViewMap();
    } else {
      tabsList.forEach(item => {
        if ("tab_" + item.name === tab.props.name) {
          loadDataFlag[item.name] = event._vts + "";
        }
      });
    }
  });
};
const resultDialogRef = ref(null);
const allStaffList = ref();
const serviceDetailRef = ref(null);
// The value of tabName must be consistent with the order result except for 'all' and 'review'
const tabsList = [
  {
    label: transformI18n("orders.all"),
    name: "all",
    component: ListTable,
    props: { tabName: "all", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("common.new"),
    name: "new",
    component: ListTable,
    props: { tabName: "new", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("common.received"),
    name: "received",
    component: ListTable,
    props: { tabName: "received", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_onProduction"),
    name: "onProduction",
    component: ListTable,
    props: { tabName: "onProduction", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_ready"),
    name: "ready",
    component: ListTable,
    props: { tabName: "ready", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_service"),
    name: "service",
    component: ListTable,
    props: { tabName: "service", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_outstanding"),
    name: "outstanding",
    component: ListTable,
    props: { tabName: "outstanding", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("common.completion"),
    name: "completion",
    component: ListTable,
    props: { tabName: "completion", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("common.cancelled"),
    name: "cancelled",
    component: ListTable,
    props: { tabName: "cancelled", companyRegion: companyRegion.value }
  }
];

// Dynamic components refresh the data
const loadDataFlag = reactive({
  all: "",
  new: "",
  onProduction: "",
  ready: "",
  service: "",
  outstanding: "",
  completion: "",
  cancelled: ""
});

// view details page
function toDetail(row) {
  serviceDetailRef.value.show(row);
}

function beforeResultClose(done) {
  activeName.value = "tab_review";
  loadDataFlag["review"] =
    "" + (Math.floor(Math.random() * (1000 - 10 + 1)) + 10);
  done();
}

function reLoadTableData() {
  tabsList.forEach(item => {
    if ("tab_" + item.name === activeName.value) {
      loadDataFlag[item.name] =
        "" + (Math.floor(Math.random() * (1000 - 10 + 1)) + 10);
    }
    item.props.companyRegion = companyRegion.value;
  });
  getSummaryInfo();
}

// get system all account
function getAccount() {
  // staff info
  const search = "";
  const filter = "";
  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  const size = 666;
  let companyRegionStr = null;
  if (companyRegion.value.length) {
    companyRegionStr = companyRegion.value.join(",");
  }
  accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    companyRegionStr
  ).then(res => {
    const { data } = res;
    allStaffList.value = data || [];
  });
}

// get every type service list total number
function getSummaryInfo() {
  let companyRegionStr = "";
  if (companyRegion.value.length) {
    companyRegionStr = companyRegion.value.join(",");
  }
  serviceControllerGetSummaryInfo(companyRegionStr).then(res => {
    const { data } = res;
    if (data) {
      for (const key in data) {
        listCount.value[key] = data[key];
      }
    }
  });
}
function changeCompany() {
  getAccount();
  reLoadTableData();
  mapCompanyRegion.value = companyRegion.value;
}
function initData() {
  getAccount();
  getSummaryInfo();
}

function changeMapMarksNum(total) {
  MapMarksNum.value = total;
}

onMounted(() => {
  initData();
});
</script>

<template>
  <el-card shadow="never">
    <div class="font-medium page-header">
      {{ transformI18n("menus.hsService") }}
    </div>
    <div class="page-action-box">
      <el-select
        v-if="userBaseInfo.isPlatformUser"
        class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
        v-model="companyRegion"
        @change="changeCompany"
        multiple
        collapse-tags
        collapse-tags-tooltip
        :placeholder="'Company'"
      >
        <el-option
          v-for="item in userBaseInfo.companyRegion"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <el-tabs
      v-model="activeName"
      :class="
        userBaseInfo.isPlatformUser ? 'demo-tabs platform-tabs' : 'demo-tabs'
      "
      @tab-click="changeTab"
    >
      <el-tab-pane
        label="tabItem.label"
        :name="'tab_' + tabItem.name"
        v-for="(tabItem, index) in tabsList"
        :key="index"
      >
        <template #label>
          <span class="custom-tabs-label">
            <span>{{ tabItem.label }}</span>
            <el-tag
              type="info"
              effect="light"
              round
              v-if="listCount[tabItem.name] > 0"
            >
              {{ listCount[tabItem.name] }}
            </el-tag>
          </span>
        </template>
        <component
          :is="tabItem.component"
          v-if="activeName == 'tab_' + tabItem.name"
          :ref="`${tabItem.name}Ref`"
          @updateListSummary="getSummaryInfo"
          @toDetail="row => toDetail(row, tabItem.name)"
          :args="tabItem.props"
          :loadDataFlag="loadDataFlag[tabItem.name]"
          :allStaffList="allStaffList"
        />
      </el-tab-pane>
      <el-tab-pane label="Map View" name="mapView">
        <template #label>
          <span class="custom-tabs-label">
            <span>Map View</span>
            <el-tag type="info" effect="light" round v-if="MapMarksNum > 0">
              {{ MapMarksNum }}
            </el-tag>
          </span>
        </template>
        <mapView
          ref="mapViewRef"
          :dataType="'service'"
          :companyRegion="mapCompanyRegion"
          @updateMapMarksNum="changeMapMarksNum"
        />
      </el-tab-pane>
    </el-tabs>
    <ServiceDetail
      ref="serviceDetailRef"
      @closed="reLoadTableData"
      :allStaff="allStaffList"
    />
    <ResultDialog ref="resultDialogRef" :before-close="beforeResultClose" />
  </el-card>
</template>
<style lang="scss" scoped>
.demo-tabs {
  :deep(.el-tabs__header) {
    padding: 0 0 0 200px;
    -ms-overflow-style: none;
    scrollbar-width: none;

    ::-webkit-scrollbar {
      display: none;
    }
  }
}

.platform-tabs {
  // height: calc(100% - 50px);
  margin-top: 50px;
}

::v-deep(.platform-tabs.el-tabs .el-tabs__header) {
  padding: 0 10px;
}
</style>
