<script setup lang="ts">
import {
  roleControllerIndex,
  roleC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  permissionControllerIndex
} from "@/api/admin/admin-role";
import {
  columns,
  platformColumns,
  indexDefaultQuery,
  defaultRoles
} from "./role_data";
import RoleForm from "./form.vue";
import { onMounted, reactive, ref } from "vue";
import DataTable from "@/components/DataTable";
import { Plus } from "@element-plus/icons-vue";
import { transformI18n } from "@/plugins/i18n";
import RiEdit2Line from "@iconify-icons/ri/edit-2-line";
import searchLine from "@iconify-icons/ri/search-line";
import { ElMessageBox, ElLoading } from "element-plus";
import { useNav } from "@/layout/hooks/useNav";

defineOptions({
  name: "Role"
});

const { userBaseInfo } = useNav();
const form = reactive({});
const dataTableRef = ref(null);
const dataFormRef = ref(null);
const permissions = ref([] as any);
const mv = ref();
const dialogPanel = ref();

const companyRegion = ref([]);

function onCreate() {
  dataFormRef.value.show().then(() => {
    loadData();
  });
}

function onEdit(row) {
  dataFormRef.value.show(row).then(() => {
    loadData();
  });
}

function onDelete(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>delete</strong> the <strong style='color:var(--el-color-primary)'>${row.name}</strong> role?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    roleControllerDestroy(row.id)
      .then(() => {
        dialogPanel.value.close();
        loadData();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

function loadData() {
  dataTableRef.value.loadData();
}

function tableQueryParams() {
  const filterInfo = [];
  let search = "";
  if (form.search) {
    search = `%${form.search}%`;
  }
  if (companyRegion.value.length) {
    filterInfo.push("company_region:in:" + companyRegion.value.join("|"));
  }
  filterInfo.push("is_pad:eq:0");
  return Object.assign(
    { ...indexDefaultQuery },
    {
      filter:
        filterInfo.length === 0 ? indexDefaultQuery.filter : filterInfo.join()
    },
    { search: search }
  );
}
function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}
onMounted(() => {
  permissionControllerIndex(null, null, null, null, null, 1, 99999).then(
    result => {
      permissions.value = result.data;
    }
  );
});
</script>

<template>
  <el-card ref="mv" shadow="never">
    <template #header>
      <div class="card-header">
        <span class="font-medium">
          {{ transformI18n("menus.hsRole") }}
        </span>
        <div class="page-action-box">
          <el-select
            v-if="userBaseInfo.isPlatformUser"
            class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
            v-model="companyRegion"
            @change="loadData"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :placeholder="'Company'"
          >
            <el-option
              v-for="item in userBaseInfo.companyRegion"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            v-auth="'addRole'"
            :icon="Plus"
            type="primary"
            @click="onCreate"
            >{{ transformI18n("role.newRole") }}</el-button
          >
        </div>
      </div>
    </template>
    <div class="mt-2 px-2 pb-2 bg-bg_color">
      <div class="flex justify-end w-full h-[50px] list-form">
        <div class="search-box">
          <el-input
            ref="inputRef"
            v-model="form.search"
            size="large"
            clearable
            :placeholder="transformI18n('leads.keywordPlaceHolder')"
            @input="loadData()"
            class="table-search-input"
          >
            <template #prefix>
              <el-tooltip
                class="item"
                effect="dark"
                content="You can search by Name"
                placement="top"
              >
                <IconifyIconOffline
                  class="mr-[5px]"
                  width="16px"
                  height="16px"
                  color="#656F7D"
                  :icon="searchLine"
                />
              </el-tooltip>
            </template>
          </el-input>
        </div>
      </div>
      <data-table
        ref="dataTableRef"
        :columns="userBaseInfo.isPlatformUser ? platformColumns : columns"
        :source="roleControllerIndex"
        :form="form"
        :slotNames="['isPlatform', 'operation']"
        :query-params="tableQueryParams"
        :header-cell-style="{
          background: 'var(--el-table-row-hover-bg-color)',
          color: 'var(--el-text-color-primary)'
        }"
      >
        <template #isPlatform="{ row }">
          <span v-if="row.is_platform"> Yes </span>
          <span v-else>No</span>
        </template>
        <template #operation="{ row }">
          <div>
            <el-dropdown trigger="click">
              <span class="iconfont just-icon-gengduo" />
              <template #dropdown>
                <el-dropdown-menu>
                  <Auth value="editRole">
                    <el-dropdown-item
                      class="text-[#2A2E34] text-base"
                      @click.stop="onEdit(row)"
                    >
                      <IconifyIconOffline
                        class="mr-[5px]"
                        width="20px"
                        height="20px"
                        color="#9B3CE5"
                        :icon="RiEdit2Line"
                      />
                      {{ transformI18n("buttons.hseditor") }}
                    </el-dropdown-item>
                  </Auth>
                  <Auth value="delRole">
                    <el-dropdown-item
                      v-if="!defaultRoles.includes(row.name)"
                      class="text-[#2A2E34] text-base"
                      @click.stop="onDelete(row)"
                    >
                      <i
                        class="!text-xl text-[#9B3CE5] iconfont just-icon-cancelled"
                      />
                      {{ transformI18n("buttons.hsdelete") }}
                    </el-dropdown-item>
                  </Auth>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </data-table>
      <role-form ref="dataFormRef" :permissions="permissions" />
    </div>
  </el-card>
</template>
