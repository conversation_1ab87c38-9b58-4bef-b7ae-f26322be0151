import { requiredField } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
import {
  DashboardPermission,
  AppointmentPermission,
  LeadsPermission,
  OrdersPermission,
  ProductPermission,
  ClientsPermission,
  BasicDataPermission,
  AccountPermission,
  RolePermission,
  ReportPermission,
  ServicePermission
} from "@/enum/permission";

export const indexDefaultQuery = {
  search: "",
  sort: ["id"],
  filter: [],
  _with: ["permissions"],
  withCount: []
};

export const platformColumns: TableColumnList = [
  {
    label: transformI18n("role.name"),
    prop: "name",
    formatter(row) {
      return row.translation?.en;
    }
  },
  {
    label: "Company",
    prop: "company_region"
  },
  {
    label: "Is Platform Role",
    prop: "is_platform",
    slot: "isPlatform"
  },
  {
    label: transformI18n("common.createDate"),
    prop: "created_at"
  },
  {
    label: transformI18n("common.updateDate"),
    prop: "updated_at"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];

export const columns: TableColumnList = [
  {
    label: transformI18n("role.name"),
    prop: "name",
    formatter(row) {
      return row.translation?.en;
    }
  },
  {
    label: transformI18n("common.createDate"),
    prop: "created_at"
  },
  {
    label: transformI18n("common.updateDate"),
    prop: "updated_at"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];

export const rules = {
  name: [requiredField(transformI18n("role.name"))]
};

export const defaultRoles = [
  "super_admin",
  "general_manager",
  "admin_office",
  "sale_manager",
  "appointment_setter",
  "production_assistant"
  // "default_platform_admin"
];

export const permissionMap = [
  {
    name: transformI18n("menus.hshome"),
    items: [
      {
        name: transformI18n("menus.hshome"),
        permissions: Object.values(DashboardPermission)
      }
    ]
  },
  {
    name: transformI18n("menus.hsAppointment"),
    items: [
      {
        name: transformI18n("menus.hsAppointment"),
        permissions: Object.values(AppointmentPermission)
      }
    ]
  },
  {
    name: transformI18n("menus.hsLeads"),
    items: [
      {
        name: transformI18n("menus.hsLeads"),
        permissions: Object.values(LeadsPermission)
      }
    ]
  },
  {
    name: transformI18n("menus.hsOrders"),
    items: [
      {
        name: transformI18n("menus.hsOrders"),
        permissions: Object.values(OrdersPermission)
      }
    ]
  },
  {
    name: transformI18n("menus.hsService"),
    items: [
      {
        name: transformI18n("menus.hsService"),
        permissions: Object.values(ServicePermission)
      }
    ]
  },
  {
    name: transformI18n("menus.hsClients"),
    items: [
      {
        name: transformI18n("menus.hsClients"),
        permissions: Object.values(ClientsPermission)
      }
    ]
  },
  {
    name: transformI18n("menus.hsProduct"),
    items: [
      {
        name: transformI18n("menus.hsProduct"),
        permissions: Object.values(ProductPermission)
      }
    ]
  },
  {
    name: transformI18n("menus.hsbasic"),
    items: [
      {
        name: transformI18n("menus.hsbasic"),
        permissions: Object.values(BasicDataPermission)
      }
    ]
  },
  {
    name: transformI18n("menus.hsRole"),
    items: [
      {
        name: transformI18n("menus.hsRole"),
        permissions: Object.values(RolePermission)
      }
    ]
  },
  {
    name: transformI18n("menus.hsUser"),
    items: [
      {
        name: transformI18n("menus.hsUser"),
        permissions: Object.values(AccountPermission)
      }
    ]
  },
  {
    name: transformI18n("menus.hasReportManage"),
    items: [
      {
        name: transformI18n("menus.hasReportManage"),
        permissions: Object.values(ReportPermission)
      }
    ]
  }
];
