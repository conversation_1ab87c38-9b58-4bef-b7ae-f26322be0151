<script setup lang="ts">
import { reactive, ref } from "vue";
import { defaultRoles, rules, permissionMap } from "./role_data";
import {
  roleControllerStore,
  roleControllerUpdate
} from "@/api/admin/admin-role";
import { sendFormFilter } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const form = reactive({
  id: null,
  name: null,
  code: null,
  company_region: null,
  permissions: [],
  is_platform: 0
});
const props = defineProps({
  permissions: Array
});
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const isEdit = ref(false);
const permissionData = ref([]);
const errorFlag = ref(false);

function show(data = null) {
  isEdit.value = !!data;
  errorFlag.value = false;
  if (isEdit.value) {
    Object.assign(form, data);
    form.code = data.name;
    form.name = data.translation?.en;
  } else {
    Object.keys(form).forEach(key => {
      form[key] = null;
      if (key == "is_platform") {
        form[key] = 0;
      }
      if (key == "company_region") {
        console.log(userBaseInfo);
        form.company_region = userBaseInfo.value.isPlatformUser
          ? null
          : userBaseInfo.value.accountCompanyRegion;
      }
    });
  }
  buildPermissionData();
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function hide() {
  dialogVisible.value = false;
}

async function onSave() {
  await formRef.value.validate(valid => {
    if (!form.is_platform && !form.company_region) {
      errorFlag.value = true;
      return;
    }
    if (valid) {
      loading.value = true;
      const sendForm = {
        ...sendFormFilter(form),
        guard_name: "api",
        translation: { en: form.name, "zh-CN": form.name },
        permissions: formPermissions()
      };

      if (isEdit.value) {
        sendForm.name = form.code;
      }
      (isEdit.value
        ? roleControllerUpdate(form.id, sendForm)
        : roleControllerStore(sendForm)
      )
        .then(() => {
          loading.value = false;
          hide();
          promise.resolve();
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
}

function buildPermissionData() {
  permissionData.value = [];

  JSON.parse(JSON.stringify(permissionMap)).forEach(module => {
    module.items.forEach((item, index) => {
      item.permissions = item.permissions.map(permission => {
        const permissionTmp = props.permissions.find(
          p => p.name === permission
        );

        return {
          ...permissionTmp,
          name: transformI18n(`permission.${permission}`),
          checked:
            form.permissions &&
            !!form.permissions.find(p => p.name === permission && p.active)
        };
      });
      permissionData.value.push({
        module: module.name,
        item: item.name,
        itemChecked: item.permissions.every(p => p.checked),
        permissions: item.permissions,
        rowspan: index === 0 ? module.items.length : 0
      });
    });
  });
}

function handleItemCheckChange(row) {
  row.permissions.forEach(p => {
    p.checked = row.itemChecked;
  });
}

function handlePermissionCheckChange(row) {
  row.itemChecked = row.permissions.every(p => p.checked);
}

function formPermissions() {
  const result = [];
  permissionData.value.forEach(item => {
    result.push(...item.permissions.filter(p => p.checked).map(p => p.id));
  });
  return result;
}

function permissionSpanMethod({ row, columnIndex }) {
  if (columnIndex === 0) {
    return {
      rowspan: row.rowspan,
      colspan: 1
    };
  }
}

defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="
      isEdit ? transformI18n('role.editRole') : transformI18n('role.newRole')
    "
    width="80%"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      class="w-[99/100] ml-7 mr-7"
    >
      <el-col class="!mb-0 mt-5 text-sm text-[#2A2E34]" :span="24">{{
        transformI18n("role.name")
      }}</el-col>
      <el-col :span="24">
        <el-form-item prop="name">
          <el-input
            v-model="form.name"
            :placeholder="transformI18n('role.name')"
            autocomplete="off"
            :disabled="defaultRoles.includes(form.name)"
          />
        </el-form-item>
      </el-col>
      <el-col
        v-if="userBaseInfo.isPlatformUser"
        class="!mb-0 mt-5 text-sm text-[#2A2E34]"
        :span="24"
      >
        Platform Role
      </el-col>
      <el-col v-if="userBaseInfo.isPlatformUser">
        <el-radio-group
          v-model="form.is_platform"
          class="ml-4"
          :disabled="isEdit"
        >
          <el-radio :label="1" size="large">Yes</el-radio>
          <el-radio :label="0" size="large">No</el-radio>
        </el-radio-group>
      </el-col>
      <el-col
        v-if="userBaseInfo.isPlatformUser && !form.is_platform"
        class="!mb-0 mt-5 text-sm text-[#2A2E34]"
        :span="24"
      >
        Company
      </el-col>
      <el-col
        v-if="userBaseInfo.isPlatformUser && !form.is_platform"
        :span="24"
      >
        <el-form-item prop="Company">
          <el-select
            v-model="form.company_region"
            clearable
            :placeholder="'Company'"
            :disabled="isEdit"
          >
            <el-option
              v-for="item in userBaseInfo.companyRegion"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <p
        class="-mt-4 text-[#f56c6c] text-[12px]"
        v-if="userBaseInfo.isPlatformUser && !form.is_platform && errorFlag"
      >
        Please select the company
      </p>
      <el-col class="!mb-2.5 text-sm text-[#2A2E34]" :span="24">{{
        transformI18n("role.menuPermission")
      }}</el-col>
    </el-form>

    <div class="ml-7 mr-7">
      <el-table
        border
        :data="permissionData"
        :span-method="permissionSpanMethod"
      >
        <el-table-column
          prop="module"
          :label="transformI18n('permission.module')"
          width="180"
        />
        <el-table-column
          prop="item"
          :label="transformI18n('permission.item')"
          width="180"
        >
          <template #default="{ row }">
            <el-checkbox
              v-model="row.itemChecked"
              :indeterminate="
                !!row.permissions.find(p => p.checked) &&
                !!row.permissions.find(p => !p.checked)
              "
              @change="handleItemCheckChange(row)"
              >{{ row.item }}</el-checkbox
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="setting"
          :label="transformI18n('permission.setting')"
        >
          <template #default="{ row }">
            <el-checkbox
              v-for="permission in row.permissions"
              :key="permission.id"
              v-model="permission.checked"
              @change="handlePermissionCheckChange(row)"
              >{{ permission.name }}</el-checkbox
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide">{{
          transformI18n("buttons.hsCancel")
        }}</el-button>
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :disabled="loading"
          :loading="loading"
          @click="onSave"
          >{{ transformI18n("buttons.hssave") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.help-block {
  font-size: 12px;
  color: #999;
}

.form-item-with {
  width: 80%;
}
</style>
