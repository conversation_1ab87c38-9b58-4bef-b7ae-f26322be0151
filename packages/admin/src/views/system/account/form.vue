<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { rules, typeOption } from "./account_data";
import RoleSelect from "@/components/Select/RoleSelect.vue";
import { reportControllerIndex } from "@/api/admin/report";

import {
  accountControllerStore,
  accountControllerUpdate
} from "@/api/admin/admin-account";
import { sendFormFilter } from "@/utils/form";

import { transformI18n } from "@/plugins/i18n";
import { useNav } from "@/layout/hooks/useNav";
import { message } from "@/utils/message";

const { userBaseInfo } = useNav();

const form = reactive({
  id: null,
  type: "pc",
  role: [],
  report_look: [],
  is_active: 0,
  is_platform: 0,
  company_region: null,
  calendar_id: null,
  company_ids: [],
  all_flag: 0
});
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const isEdit = ref(false);
const dashboardList = ref([]);
const biReportsList = ref([]);

function show(data = null) {
  isEdit.value = !!data;
  biReportsList.value = [];
  if (isEdit.value) {
    Object.assign(form, data);
    form.role = [];
    if (data.roles.length) {
      if (form.type == "pc") {
        data.roles.forEach(item => {
          form.role.push(item.id);
        });
      } else {
        form.role = data.roles[0].id;
      }
    }
    if (form.report_look) {
      form.report_look = form.report_look.split(",").map(Number);
    }
    if (form.company_ids) {
      form.company_ids = form.company_ids.split(",").map(String);
    }
    if (dashboardList.value.length) {
      dashboardList.value.forEach(item => {
        if (
          item.company_region == form.company_region ||
          (form.is_platform &&
            (form.all_flag ||
              (!form.all_flag &&
                form.company_ids &&
                form.company_ids.indexOf(item.company_region) !== -1)))
        ) {
          biReportsList.value.push(item);
        }
      });
    }
  } else {
    Object.keys(form).forEach(key => {
      form[key] = null;
      if (key == "type") {
        form[key] = "pc";
      }
      if (key == "is_active") {
        form[key] = 1;
      }
      if (key == "report_look") {
        form.report_look = [];
      }
      if (key == "is_platform") {
        form[key] = 0;
      }
      if (key == "company_region") {
        form[key] = userBaseInfo.value.isPlatformUser
          ? null
          : userBaseInfo.value.accountCompanyRegion;
      }
    });
    console.log(form);
  }
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function hide() {
  dialogVisible.value = false;
}

function changeType(value) {
  if (value == "pad") {
    form.role = null;
    form.is_platform = 0;
    form.all_flag = 0;
    form.company_ids = [];
  } else {
    form.role = [];
  }
}
function changeAllFlag(value) {
  form.company_ids = [];
  biReportsList.value = [];
  form.report_look = [];
  if (value) {
    if (dashboardList.value.length && form.is_platform) {
      dashboardList.value.forEach(item => {
        if (item.company_region == "All") {
          biReportsList.value.push(item);
        }
      });
    }
  }
}

function changeAccessCompany(value) {
  console.log(value);
  form.report_look = [];
  biReportsList.value = [];
  dashboardList.value.forEach(item => {
    if (value.length && value.indexOf(item.company_region) !== -1) {
      biReportsList.value.push(item);
    }
  });
}
function changeCompany(value) {
  biReportsList.value = [];
  form.report_look = [];
  form.company_ids = [];
  form.all_flag = 0;
  if (form.type == "pc") {
    form.role = [];
  } else {
    form.role = null;
  }
  if (dashboardList.value.length) {
    dashboardList.value.forEach(item => {
      if (item.company_region == value) {
        biReportsList.value.push(item);
      }
    });
  }
}
function changePlatform() {
  biReportsList.value = [];
  // if (dashboardList.value.length && form.is_platform) {
  //   dashboardList.value.forEach(item => {
  //     biReportsList.value.push(item);
  //   });
  // }
  form.report_look = [];
  form.role = [];
  form.company_region = null;
  form.type = "pc";
}
async function onSave() {
  await formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      let report_look = "";
      if (form.report_look && form.report_look.length) {
        report_look = form.report_look.join();
      }
      let company_ids = "";
      if (form.company_ids && form.company_ids.length) {
        company_ids = form.company_ids.join();
      }
      const sendForm = {
        ...sendFormFilter(form),
        roles: form.type == "pc" ? form.role : [form.role],
        report_look,
        company_ids
      };
      if (form.is_platform && !sendForm.role.length) {
        message("Please select a role for the user!", { type: "error" });
        loading.value = false;
        return;
      }
      delete sendForm.role;
      // if (form.type == "pad") {
      //   delete sendForm.roles;
      // } else if (!form.role) {
      //   return;
      // }
      console.log(sendForm);
      (isEdit.value
        ? accountControllerUpdate(form.id, sendForm)
        : accountControllerStore(sendForm)
      )
        .then(() => {
          loading.value = false;
          hide();
          promise.resolve();
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
}

function getBIreportList() {
  reportControllerIndex().then(res => {
    dashboardList.value = res.data;
  });
}

onMounted(() => {
  getBIreportList();
});
defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="
      isEdit
        ? transformI18n('account.editAccount')
        : transformI18n('account.newAccount')
    "
    width="38.75rem"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      class="w-[99/100] ml-7 mr-7"
    >
      <el-row>
        <el-col
          v-if="userBaseInfo.isPlatformUser"
          class="!mb-0 mt-5 text-sm text-[#2A2E34]"
          :span="24"
        >
          Platform User
        </el-col>
        <el-col v-if="userBaseInfo.isPlatformUser">
          <el-radio-group
            v-model="form.is_platform"
            class="ml-4"
            @change="changePlatform"
          >
            <el-radio :label="1" size="large">Yes</el-radio>
            <el-radio :label="0" size="large">No</el-radio>
          </el-radio-group>
        </el-col>
        <el-col
          v-if="userBaseInfo.isPlatformUser && !form.is_platform"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
        >
          Company
        </el-col>
        <el-col
          v-if="userBaseInfo.isPlatformUser && !form.is_platform"
          :span="24"
        >
          <el-form-item prop="Company">
            <el-select
              v-model="form.company_region"
              clearable
              @change="changeCompany"
              :placeholder="'Company'"
            >
              <el-option
                v-for="item in userBaseInfo.companyRegion"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("account.username")
        }}</el-col>
        <el-col :span="24">
          <el-form-item prop="login_name">
            <el-input
              v-model="form.login_name"
              :placeholder="transformI18n('account.username')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("account.name")
        }}</el-col>
        <el-col :span="24">
          <el-form-item prop="name">
            <el-input
              v-model="form.name"
              :placeholder="transformI18n('account.name')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("account.email")
        }}</el-col>
        <el-col :span="24">
          <el-form-item prop="email">
            <el-input
              v-model="form.email"
              :placeholder="transformI18n('account.email')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("account.mobile")
        }}</el-col>
        <el-col :span="24">
          <el-form-item prop="mobile">
            <el-input
              v-model="form.mobile"
              :placeholder="transformI18n('account.mobile')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("account.position")
        }}</el-col>
        <el-col :span="24">
          <el-form-item prop="position">
            <el-input
              v-model="form.position"
              :placeholder="transformI18n('account.position')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="!form.is_platform"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >{{ transformI18n("account.userType") }}</el-col
        >
        <el-col v-if="!form.is_platform" :span="24">
          <el-form-item prop="type">
            <el-select
              v-model="form.type"
              @change="changeType"
              :placeholder="transformI18n('common.search')"
            >
              <el-option
                v-for="item in typeOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">Status</el-col>
        <el-col :span="24">
          <el-switch
            v-model="form.is_active"
            active-text="Active"
            :active-value="1"
            :inactive-value="0"
            inactive-text="Inactive"
          />
        </el-col>
        <el-col
          v-if="
            dialogVisible &&
            (((form.company_region || form.is_platform) &&
              userBaseInfo.isPlatformUser) ||
              !userBaseInfo.isPlatformUser)
          "
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >{{ transformI18n("account.role") }}</el-col
        >
        <el-col
          v-if="
            dialogVisible &&
            (((form.company_region || form.is_platform) &&
              userBaseInfo.isPlatformUser) ||
              !userBaseInfo.isPlatformUser)
          "
          :span="24"
        >
          <el-form-item prop="role">
            <role-select
              v-model="form.role"
              :type="form.type"
              :companyRegion="
                form.is_platform ? 'platform' : form.company_region
              "
            />
          </el-form-item>
        </el-col>
        <el-col
          v-if="form.is_platform"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >Access All Company</el-col
        >
        <el-col v-if="form.is_platform" :span="24">
          <el-switch
            v-model="form.all_flag"
            @change="changeAllFlag"
            active-text="Yes"
            :active-value="1"
            :inactive-value="0"
            inactive-text="No"
          />
        </el-col>
        <el-col
          v-if="form.is_platform && !form.all_flag"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
          >Access Company</el-col
        >
        <el-col v-if="form.is_platform && !form.all_flag" :span="24">
          <el-form-item prop="company_ids">
            <el-select
              v-model="form.company_ids"
              clearable
              multiple
              @change="changeAccessCompany"
              :placeholder="'Access Company'"
            >
              <el-option
                v-for="item in userBaseInfo.companyRegion"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24"
          >Google Calendar ID</el-col
        >
        <el-col :span="24">
          <el-form-item prop="calendar_id">
            <el-input
              v-model="form.calendar_id"
              :placeholder="transformI18n('common.input')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">Report</el-col>
        <el-col :span="24">
          <el-form-item prop="report_look">
            <el-select
              v-model="form.report_look"
              multiple
              filterable
              :placeholder="transformI18n('common.search')"
            >
              <el-option
                v-for="item in biReportsList"
                :key="item.id"
                :value="item.id"
                :label="
                  form.is_platform
                    ? item.name + '(' + item.company_region + ')'
                    : item.name
                "
              >
                {{
                  form.is_platform
                    ? item.name + "(" + item.company_region + ")"
                    : item.name
                }}
                <span class="iconfont just-icon-eye ml-2" v-if="item.is_show" />
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide">{{
          transformI18n("buttons.hsCancel")
        }}</el-button>
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :loading="loading"
          :disabled="loading"
          @click="onSave"
          >{{ transformI18n("buttons.hssave") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.help-block {
  font-size: 12px;
  color: #999;
}

.form-item-with {
  width: 80%;
}
</style>
