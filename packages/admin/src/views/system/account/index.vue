<script setup lang="ts">
import {
  accountControllerIndex,
  accountC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  accountControllerResetPassword
} from "@/api/admin/admin-account";
import { columns, platformColumns } from "./account_data";
import AccountForm from "./form.vue";
import { reactive, ref, onMounted } from "vue";
import DataTable from "@/components/DataTable";
import { Plus } from "@element-plus/icons-vue";
import { transformI18n } from "@/plugins/i18n";
import RiEdit2Line from "@iconify-icons/ri/edit-2-line";
import Lock2Line from "@iconify-icons/ri/lock-2-line";
import searchLine from "@iconify-icons/ri/search-line";
import RoleSelect from "@/components/Select/RoleSelect.vue";
import { ElMessageBox, ElLoading } from "element-plus";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const companyRegion = ref([]);

defineOptions({
  name: "Account"
});

const form = reactive({});
const formRef = ref(null);
const refInput = ref();
const dataTableRef = ref(null);
const dataFormRef = ref(null);
const mv = ref();
const dialogPanel = ref();
const indexQuery = reactive({
  search: "",
  sort: "-id",
  filter: "",
  _with: "roles",
  withCount: "",
  isList: null
});

function onSearch() {
  loadData();
}

function onReset() {
  refInput.value.focus();
  indexQuery.search = "";
  Object.keys(form).forEach(key => {
    switch (key) {
      default:
        form[key] = null;
        break;
    }
  });
  loadData();
}

function onCreate() {
  dataFormRef.value.show().then(() => {
    loadData();
  });
}

function onEdit(row) {
  dataFormRef.value.show(row).then(() => {
    loadData();
  });
  // uploadFormRef.value
}

function onDelete(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>delete</strong> the <strong style='color:var(--el-color-primary)'>${row.name}</strong> account?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    accountControllerDestroy(row.id)
      .then(() => {
        dialogPanel.value.close();
        loadData();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

function resetPwd(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>reset the password </strong> for <strong style='color:var(--el-color-primary)'>${row.name}</strong> account?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    accountControllerResetPassword(row.id)
      .then(() => {
        dialogPanel.value.close();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

function loadData() {
  dataTableRef.value.loadData();
}

function tableQueryParams(form) {
  const filterInfo = [];
  let search = "";
  if (form.search) {
    search = `%${form.search}%`;
  }
  if (companyRegion.value.length) {
    filterInfo.push("company_region:in:" + companyRegion.value.join("|"));
  }
  if (form.role) {
    filterInfo.push(`roles.id:eq:${form.role}`);
  }
  if (form.position) {
    filterInfo.push(`position:like:%${form.position}%`);
  }
  console.log("*********");
  return Object.assign(
    { ...indexQuery },
    { filter: filterInfo.length === 0 ? indexQuery.filter : filterInfo.join() },
    { search: search },
    { isList: true }
  );
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function getRoleName(role) {
  console.log(role);
  let roleName = "";
  if (role) {
    roleName = role.translation ? JSON.parse(role.translation).en : role.name;
  }
  return roleName + " ";
}
onMounted(() => {});
</script>

<template>
  <el-card ref="mv" shadow="never">
    <template #header>
      <div class="card-header">
        <span class="font-medium">
          {{ transformI18n("menus.hsUser") }}
        </span>
        <div class="page-action-box">
          <el-select
            v-if="userBaseInfo.isPlatformUser"
            class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
            v-model="companyRegion"
            @change="loadData"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :placeholder="'Company'"
          >
            <el-option
              v-for="item in userBaseInfo.companyRegion"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>

          <el-button
            v-auth="'addAccount'"
            :icon="Plus"
            type="primary"
            @click="onCreate"
            >{{ transformI18n("account.newAccount") }}</el-button
          >
        </div>
      </div>
    </template>
    <div class="mt-2 px-2 pb-2 bg-bg_color">
      <div class="flex justify-between w-full h-[50px] list-form">
        <el-form ref="formRef" :inline="true" :model="form">
          <el-form-item
            :label="transformI18n('account.position') + ':'"
            prop="position"
          >
            <el-input
              v-model="form.position"
              :placeholder="transformI18n('account.position')"
              clearable
              class="!w-[200px]"
              ref="refInput"
              @input="onSearch()"
            />
          </el-form-item>
          <el-form-item
            :label="transformI18n('account.role') + ':'"
            prop="role"
          >
            <role-select
              v-model="form.role"
              :companyRegion="
                userBaseInfo.isPlatformUser
                  ? ''
                  : userBaseInfo.accountCompanyRegion
              "
            />
          </el-form-item>
          <el-form-item class="action-box">
            <el-button type="primary" @click="onSearch()">{{
              transformI18n("buttons.hssearch")
            }}</el-button>
            <el-button @click="onReset()">{{
              transformI18n("buttons.hsReset")
            }}</el-button>
          </el-form-item>
        </el-form>
        <div class="search-box">
          <el-input
            ref="inputRef"
            v-model="form.search"
            size="large"
            clearable
            :placeholder="transformI18n('leads.keywordPlaceHolder')"
            @input="loadData"
            class="table-search-input"
          >
            <template #prefix>
              <el-tooltip
                class="item"
                effect="dark"
                content="You can search by Name,Position,Products,Mobile,Email"
                placement="top"
              >
                <IconifyIconOffline
                  class="mr-[5px]"
                  width="16px"
                  height="16px"
                  color="#656F7D"
                  :icon="searchLine"
                />
              </el-tooltip>
            </template>
          </el-input>
        </div>
      </div>
      <data-table
        ref="dataTableRef"
        :columns="userBaseInfo.isPlatformUser ? platformColumns : columns"
        :source="accountControllerIndex"
        :form="form"
        :slotNames="['operation', 'roleName', 'company', 'status']"
        :query-params="tableQueryParams"
        :header-cell-style="{
          background: 'var(--el-table-row-hover-bg-color)',
          color: 'var(--el-text-color-primary)'
        }"
      >
        <template #status="{ row }">
          <el-tag type="success" v-if="row.is_active">Active</el-tag>
          <el-tag type="danger" v-else>Inactive</el-tag>
        </template>
        <template #roleName="{ row }">
          <span v-for="(role, index) in row.roles" :key="index">
            {{ getRoleName(role) }}
          </span>
        </template>
        <template #operation="{ row }">
          <div>
            <el-dropdown trigger="click">
              <span class="iconfont just-icon-gengduo" />
              <template #dropdown>
                <el-dropdown-menu>
                  <Auth value="editAccount">
                    <el-dropdown-item
                      class="text-[#2A2E34] text-base"
                      @click.stop="onEdit(row)"
                    >
                      <IconifyIconOffline
                        class="mr-[5px]"
                        width="20px"
                        height="20px"
                        color="#9B3CE5"
                        :icon="RiEdit2Line"
                      />
                      {{ transformI18n("buttons.hseditor") }}
                    </el-dropdown-item>
                  </Auth>
                  <Auth value="resetPassword">
                    <el-dropdown-item
                      class="text-[#2A2E34] text-base"
                      @click.stop="resetPwd(row)"
                    >
                      <IconifyIconOffline
                        class="mr-[5px]"
                        width="20px"
                        height="20px"
                        color="#9B3CE5"
                        :icon="Lock2Line"
                      />
                      {{ transformI18n("common.changePwd") }}
                    </el-dropdown-item>
                  </Auth>
                  <Auth value="delAccount">
                    <el-dropdown-item
                      class="text-[#2A2E34] text-base"
                      @click.stop="onDelete(row)"
                    >
                      <i
                        class="!text-xl text-[#9B3CE5] iconfont just-icon-cancelled"
                      />
                      {{ transformI18n("buttons.hsdelete") }}
                    </el-dropdown-item>
                  </Auth>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </data-table>
      <account-form ref="dataFormRef" />
    </div>
  </el-card>
</template>
