import { requiredField } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
export const indexDefaultQuery = {
  search: "",
  sort: ["-id"],
  filter: [],
  _with: [],
  withCount: []
};

export const typeOption = [
  {
    value: "pc",
    label: "PC User"
  },
  {
    value: "pad",
    label: "Pad User"
  }
];

export const columns: TableColumnList = [
  {
    label: transformI18n("account.username"),
    prop: "login_name"
  },
  {
    label: transformI18n("account.name"),
    prop: "name"
  },
  {
    label: transformI18n("account.position"),
    prop: "position"
  },
  {
    label: transformI18n("account.productCategory"),
    prop: "products"
  },
  {
    label: transformI18n("account.role"),
    slot: "roleName",
    width: "200"
  },
  {
    label: transformI18n("account.email"),
    prop: "email"
  },
  {
    label: transformI18n("account.mobile"),
    prop: "mobile"
  },
  {
    label: "Status",
    prop: "status",
    slot: "status"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];

export const platformColumns: TableColumnList = [
  {
    label: transformI18n("account.username"),
    prop: "login_name"
  },
  {
    label: transformI18n("account.name"),
    prop: "name"
  },
  {
    label: transformI18n("account.position"),
    prop: "position"
  },
  {
    label: transformI18n("account.productCategory"),
    prop: "products"
  },
  {
    label: transformI18n("account.role"),
    slot: "roleName"
  },
  {
    label: transformI18n("account.email"),
    prop: "email"
  },
  {
    label: transformI18n("account.mobile"),
    prop: "mobile"
  },
  {
    label: "Company",
    prop: "company_region"
  },
  {
    label: "Status",
    prop: "status",
    slot: "status"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];
export const REGEXP_EMAIL = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
export const REGEXP_PHONE = /^((04|02|03|07|08)\d{8}|(000|111)\d{7})$/;

export const rules = {
  login_name: [requiredField(transformI18n("account.username"))],
  name: [requiredField(transformI18n("account.name"))],
  mobile: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          return true;
        } else if (!REGEXP_PHONE.test(value)) {
          callback(new Error(transformI18n("client.phoneRuleReg")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  email: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          return true;
        } else if (!REGEXP_EMAIL.test(value)) {
          callback(new Error(transformI18n("client.emailRuleReg")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
};
