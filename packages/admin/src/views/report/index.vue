<script setup lang="ts">
// import { transformI18n } from "@/plugins/i18n";
import { ElLoading } from "element-plus";
import { ref, onMounted, watch } from "vue";
import { reportControllerIndex } from "@/api/admin/report";
import { useUserStoreHook } from "@/store/modules/user";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const companyRegion = ref("");
const dialogPanel = ref();
const mv = ref();
const activeName = ref("pageHeader");
const dashboardList = ref([]);
const dashboardListShow = ref([]);
const currentUserReportLook = useUserStoreHook()?.reportLook;
const accountInfo = useUserStoreHook()?.userBaseInfo;
const isSuperadmin = useUserStoreHook()?.roles.indexOf("super_admin") !== -1;
const loading = ref([]);
const companyRegionList = ref([]);

watch(
  () => [dashboardList.value, currentUserReportLook],
  _val => {
    getCurrentReportDashboardList();
  },
  {
    immediate: true,
    deep: true
  }
);

function onIframeLoad(index) {
  loading.value[index] = false;
}

function getCurrentReportDashboardList() {
  loading.value = [];
  if (isSuperadmin && dashboardList.value.length) {
    dashboardListShow.value = dashboardList.value;
  } else if (
    currentUserReportLook &&
    currentUserReportLook.length &&
    dashboardList.value.length
  ) {
    const dashboardShowList = [];

    const reportLooksIds = accountInfo.reportLookIds
      ? accountInfo.reportLookIds.split(",")
      : [];

    dashboardList.value.map(item => {
      if (reportLooksIds.includes(item.id + "")) {
        dashboardShowList.push(item);
      }
    });
    dashboardListShow.value = dashboardShowList;
  }
  if (dashboardListShow.value?.length) {
    activeName.value = "tab" + dashboardListShow.value[0].id;
    loading.value = Array(dashboardListShow.value.length).fill(true);
  }
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function changeTab() {}

function getBIreportList() {
  companyRegionList.value = [];
  if (userBaseInfo.value.isPlatformUser) {
    companyRegion.value = userBaseInfo.value.all_flag ? "All" : null;
    // companyRegionList.value.push({
    //   label: "All",
    //   value: "All"
    // });
  } else {
    companyRegion.value = userBaseInfo.value.accountCompanyRegion;
  }
  userBaseInfo.value.companyRegion.forEach(item => {
    companyRegionList.value.push(item);
  });

  dialogLoading();
  const search = "";
  const filter = companyRegion.value
    ? "is_show:eq:1,company_region:eq:" + companyRegion.value
    : "is_show:eq:1";
  reportControllerIndex(search, filter)
    .then(res => {
      dashboardList.value = res.data;
      dialogPanel.value.close();
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}
defineOptions({
  name: "ReportDashboard"
});
onMounted(() => {
  getBIreportList();
});
</script>

<template>
  <div ref="mv" class="p-5 bg-white">
    <!-- <div class="card-header">
      <span class="font-medium">
        {{ transformI18n("report.reportDesc") }}
      </span>
      <div class="page-action-box">
        <el-select
          class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
          v-model="companyRegion"
          @change="getBIreportList"
          :placeholder="'Company'"
        >
          <el-option
            v-for="item in companyRegionList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
    </div> -->
    <el-tabs v-model="activeName" @tab-click="changeTab" class="demo-tabs">
      <el-tab-pane
        :label="item.name + '(' + item.company_region + ')'"
        :name="'tab' + item.id"
        v-for="(item, index) in dashboardListShow"
        :key="item.id"
      >
        <div v-loading="loading[index]">
          <iframe
            :src="item.url"
            frameborder="0"
            width="100%"
            @load="onIframeLoad(index)"
            class="external-webcard"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<style lang="scss" scoped>
.external-webcard {
  height: calc(100vh - 130px);
}

.demo-tabs {
  :deep(.el-tabs__nav) {
    align-items: center;
  }

  :deep(.el-tabs__header) {
    padding: 0 0 0 15px;

    .el-tabs__item.is-disabled {
      height: 27px;
      padding: 0 20px 0 0;
      font-size: 18px;
      font-weight: 600;
      color: #2a2e34;
      cursor: none;
      border-right: 1px solid #e9ebf0;
    }
  }
}
</style>
