<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { appointmentControllerIndex } from "@/api/admin/appointment";
import { statusColor, appointmentTypeText } from "@/views/appointment/data.ts";
import CirCleText from "@/components/CircleText";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
import weekday from "dayjs/plugin/weekday"; // 引入相关插件
dayjs.extend(weekday);

const router = useRouter();
const currentWeek = ref([]);
const selectedDay = ref(null);
const loading = ref(false);
const appointmentsList = ref([]);
const eventsList = ref([]);
const appointmentType = ref("all");
const appointmentTypeOption = [
  { label: "All Type", value: "all" },
  { label: "Sales", value: "sale" },
  { label: "CM", value: "cm" },
  { label: "Install", value: "install" },
  { label: "Service", value: "service" }
];
const props = defineProps({
  companyRegion: {
    type: String,
    default: ""
  }
});
watch(
  () => props.companyRegion,
  _value => {
    weekTimeHandle();
  },
  {
    immediate: true
  }
);
watch(
  () => selectedDay,
  _val => {
    getAppointmentsList();
  },
  {
    immediate: true,
    deep: true
  }
);
function changeDay(item) {
  selectedDay.value = item;
}

function getAppointmentsList() {
  const search = "";
  const filterInfo = [];
  if (selectedDay.value) {
    const startDate = selectedDay.value.time.format("YYYY-MM-DD 00:00:00");
    const endDate = selectedDay.value.time.format("YYYY-MM-DD 23:59:59");
    filterInfo.push(`date:gte:${startDate}`);
    filterInfo.push(`date:lte:${endDate}`);
  }
  if (appointmentType.value != "all") {
    filterInfo.push(`type:eq:${appointmentType.value}`);
  }
  if (props.companyRegion) {
    filterInfo.push(
      "company_region:in:" + props.companyRegion.replace(",", "|")
    );
  }
  const filter = filterInfo.join(",");
  const sort = "";
  const _with = "assignTo,client,leads, leads.category";
  loading.value = true;
  appointmentControllerIndex(search, filter, sort, _with)
    .then(res => {
      loading.value = false;
      const { data } = res;
      appointmentsList.value = data || [];
      const events = [];
      appointmentsList.value.map(item => {
        const statusColorCurrent =
          statusColor[item.status] || statusColor["all"];
        const staff = item.assign_to ? item.assign_to.login_name : "";
        const category =
          item.leads && item.leads.category ? item.leads.category.name : "";
        events.push({
          id: item.id,
          type: item.type,
          date: item.date,
          start: dayjs(item.date).format("hh:mm A"),
          bgColor: statusColorCurrent.bgColor,
          color: statusColorCurrent.color,
          staff: staff,
          status: item.status,
          comment: item.comment,
          object_id: item.object_id,
          client: item.client,
          address: item.address,
          leads: item.leads,
          assign_to: item.assign_to,
          category: category
        });
      });
      eventsList.value = events;
    })
    .catch(_err => {
      loading.value = false;
    });
}

function weekTimeHandle() {
  const startOfWeek = dayjs().weekday(1);
  const weekDays = [];
  const today = dayjs().startOf("day"); // 获取当前日期的开始时间（时、分、秒都为0）
  selectedDay.value = null;
  for (let i = 0; i < 7; i++) {
    const currentDay = startOfWeek.add(i, "day");
    const isSelectedDay = currentDay.isSame(today, "day"); // 判断是否是今天
    const dayItem = {
      time: currentDay,
      dayOfWeek: currentDay.format("ddd"),
      day: currentDay.format("D"),
      month: currentDay.format("MMMM, YYYY")
    };
    if (isSelectedDay) {
      selectedDay.value = dayItem;
    }
    weekDays.push(dayItem);
  }
  if (!selectedDay.value) {
    selectedDay.value = weekDays[0];
  }
  currentWeek.value = weekDays;
}
onMounted(() => {
  // weekTimeHandle();
});
</script>
<template>
  <div class="appointment-box">
    <div class="flex justify-between mb-[10px]">
      <span v-if="selectedDay" class="selectedDay">{{
        selectedDay.month
      }}</span>
      <div class="flex justify-end items-center">
        <el-select
          v-model="appointmentType"
          placeholder="Type"
          size="small"
          class="w-[100px] mr-[10px] px-2.5 border border-[#dcdfe6] rounded"
          @change="getAppointmentsList"
        >
          <el-option
            v-for="item in appointmentTypeOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button
          class="text-[12px] mr-[10px]"
          size="small"
          @click="router.push('/appointment/index')"
          ><span
            class="iconfont mr-2 just-icon-link-details primary-color"
          />See All</el-button
        >
      </div>
    </div>
    <div class="flex justify-evenly weekbox">
      <div
        v-for="(item, index) of currentWeek"
        :key="index"
        @click="changeDay(item)"
        :class="
          'flex flex-col items-center p-[5px] dayItem' +
          (item.day === selectedDay.day ? ' isActive' : '')
        "
      >
        <span>{{ item.dayOfWeek }}</span>
        <span>{{ item.day }}</span>
      </div>
    </div>
    <!-- appointemnt list -->
    <div v-if="!(eventsList && eventsList.length)">
      <el-empty description="No appointment for the day" :image-size="60" />
    </div>
    <div class="event-box mt-[15px]">
      <div
        v-for="(eventItem, eventIndex) of eventsList"
        :key="eventIndex"
        class="event-item flex flex-col my-[10px] p-[6px] rounded-lg text-[12px]"
        :style="`background-color: ${eventItem.bgColor}`"
      >
        <div class="flex justify-between">
          <span class="time uppercase" :style="`color: ${eventItem.color};`">{{
            eventItem.start
          }}</span>
          <div class="flex justify-start flex-nowrap">
            <span class="title mr-[6px]">#{{ eventItem.object_id }}</span>
            <span class="type" v-if="appointmentTypeText[eventItem.type]">{{
              appointmentTypeText[eventItem.type].label
            }}</span>
          </div>
        </div>
        <div class="flex items-center">
          <CirCleText
            class="circle-text"
            :text="eventItem.staff"
            :size="15"
            :fontSize="'10px'"
            :customBgColor="eventItem.color"
          />
          <span class="mx-[5px]" v-if="eventItem.staff">{{
            eventItem.staff
          }}</span>
          <span class="type" v-if="eventItem.category">{{
            eventItem.category
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
// .appointment-box {
//   max-height: 650px;
//   overflow: auto;
// }
.event-box {
  max-height: 710px;
  overflow: auto;
}

.weekbox {
  .dayItem {
    font-size: 12px;
  }

  .isActive {
    color: #fff;
    background: var(--el-color-primary);
    border-radius: 6px;
  }
}

.selectedDay {
  font-size: 20px;
  font-weight: 500px;
}

.event-item {
  .time {
    font-weight: 500;
  }

  .type,
  .title {
    padding: 2px 5px;
    font-size: 10px;
    color: #656f7d;
    background: #fff;
    border-radius: 20px;
  }
}
</style>
