<script setup lang="ts">
import { ref, onMounted, reactive, watch } from "vue";
import type { PaginationProps } from "@pureadmin/table";
import { leadsControllerIndex } from "@/api/admin/leads";
import { allResults } from "@/views/leads/components/leads_data.ts";
// import CirCleText from "@/components/CircleText";
import EmptySpan from "@/components/EmptySpan";
import dayjs from "dayjs";
const tableData = ref([]);
const loading = ref(true);
const columns: TableColumnList = [
  {
    label: "Leads ID",
    width: "80",
    prop: "id",
    fixed: true
  },
  {
    label: "Client",
    slot: "client",
    minWidth: "110"
  },
  {
    label: "Phone",
    minWidth: "100",
    slot: "phone"
  },
  {
    label: "Followup Time",
    prop: "followup_time",
    minWidth: "120"
  },
  {
    label: "Product",
    slot: "product",
    minWidth: "100"
  },
  {
    label: "Leads Result",
    slot: "result",
    minWidth: "120"
  },
  {
    label: "Appt Setter",
    slot: "apptSetter",
    minWidth: "150"
  }
];

/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  layout: "prev,next,pager,total",
  total: 0,
  align: "left",
  prevText: "Previous",
  nextText: "Next"
});
const props = defineProps({
  companyRegion: {
    type: String,
    default: ""
  }
});
watch(
  () => props.companyRegion,
  _value => {
    console.log("------");
    loadData();
  },
  {
    immediate: true
  }
);
function onCurrentChange(_page) {
  pagination.currentPage = _page;
  loadData();
}
function loadData() {
  loading.value = true;
  const params = {
    search: "",
    filter: "",
    sort: "-id",
    _with: "sales,client,apptSetter,saleAppointment",
    withCount: "",
    page: pagination.currentPage,
    size: pagination.pageSize
  };
  const filterInfo = [];
  const startDate = dayjs().format("YYYY-MM-DD 00:00:00");
  const endDate = dayjs().format("YYYY-MM-DD 23:59:59");
  filterInfo.push(`followup_time:gte:${startDate}`);
  filterInfo.push(`followup_time:lte:${endDate}`);
  if (props.companyRegion) {
    filterInfo.push(
      "company_region:in:" + props.companyRegion.replace(",", "|")
    );
  }
  params.filter = filterInfo.join(",");
  const { search, filter, sort, _with, withCount, page, size } = params;
  leadsControllerIndex(search, filter, sort, _with, withCount, page, size)
    .then(res => {
      loading.value = false;
      tableData.value = res.data;
      pagination["page-count"] = res.meta.last_page;
      pagination.currentPage = res.meta.current_page;
      pagination.total = res.meta.total;
    })
    .catch(_err => {
      loading.value = false;
    });
}

// get current row result icon
function getCurrentResultIcon(result) {
  if (!result) {
    return;
  }
  const resultAllList = [
    ...allResults,
    {
      value: "appointed",
      label: "Appointed",
      icon: "appointed"
    }
  ];
  if (typeof result === "string") {
    return resultAllList.find(item => item.value === result)?.icon;
  } else if (result instanceof Array) {
    return resultAllList.find(item => item.value === result[0])?.icon;
  }
}

onMounted(() => {
  // loadData();
});
</script>
<template>
  <div class="mt-[15px]">
    <pure-table
      row-key="id"
      showOverflowTooltip
      size="small"
      :border="true"
      :loading="loading"
      :loading-config="{ background: 'transparent' }"
      :data="tableData"
      :columns="columns"
      class="leads-list-page !text-[13px]"
    >
      <template #client="{ row }">
        <span class="iconfont just-icon-client mr-2" />
        <span v-if="row.client && row.client.given_name"
          >{{ row.client.given_name }} {{ row.client.surname }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #phone="{ row }">
        <span v-if="row.client && row.client.phone">{{
          row.client.phone
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #product="{ row }">
        <EmptySpan :text="row.product ? row.product : ''" />
      </template>
      <template #apptSetter="{ row }">
        <!-- <CirCleText
          :text="row.appt_setter.name"
          v-if="row.appt_setter && row.appt_setter.name"
        /> -->
        <EmptySpan :text="row.appt_setter ? row.appt_setter.name : ''" />
      </template>
      <template #result="{ row }">
        <div v-if="row.result" class="flex justify-start">
          <span
            :class="
              'mr-1 iconfont just-icon-' +
              getCurrentResultIcon(row.result) +
              ' input-result-icon'
            "
          />
          <span class="capitalize">{{ row.result }}</span>
        </div>
        <span class="empty" v-else>Empty</span>
      </template>
    </pure-table>
    <el-pagination
      :current-page="pagination.currentPage"
      :pageSize="pagination.pageSize"
      :small="true"
      layout="prev,next,slot"
      :total="pagination.total"
      class="custom-page"
      :prevText="pagination['prevText']"
      :nextText="pagination['nextText']"
      @current-change="onCurrentChange"
    >
      <template #default>
        <div class="page-right">
          Page {{ pagination.currentPage }} of {{ pagination["page-count"] }}
        </div>
      </template>
    </el-pagination>
  </div>
</template>
