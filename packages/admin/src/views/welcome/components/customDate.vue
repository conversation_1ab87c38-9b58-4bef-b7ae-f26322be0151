<script setup lang="ts">
import { ref, onMounted } from "vue";
import { dayjs } from "element-plus";
dayjs.en.weekStart = 1;
const emit = defineEmits<{
  (e: "confirm", val: Object): void;
  (e: "cancel"): void;
}>();

const dialogVisible = ref(false);
const selectedCustomDate = ref(null);
function show(data = null) {
  dialogVisible.value = true;
  console.log(data);
  if (data) {
    selectedCustomDate.value = [data.start, data.end];
  } else {
    selectedCustomDate.value = null;
  }
}

function hide() {
  dialogVisible.value = false;
  emit("cancel");
}

function confirm() {
  dialogVisible.value = false;
  emit("confirm", selectedCustomDate);
}

onMounted(() => {});
defineExpose({ show });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'Custom Date'"
    width="50%"
    class="map-box"
  >
    <div class="text-center pt-2">
      <el-date-picker
        v-model="selectedCustomDate"
        type="daterange"
        class="border border-[#E9EBF0] rounded-[6px] p-[5px] grow"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        placeholder="Select Date"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="hide" class="w-100 h-[40px]">Cancel</el-button>
        <el-button
          type="primary"
          @click="confirm"
          :disabled="!selectedCustomDate"
          class="w-100 h-[40px]"
          >Confirm</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
