<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { dashboardControllerGetTarget } from "@/api/admin/dashboard";
import dayjs from "dayjs";
const tableData = ref([]);
const loading = ref(true);
const productColumns: TableColumnList = [
  {
    label: "Target",
    prop: "name",
    fixed: true
  },
  {
    label: "Completion",
    prop: "completion",
    fixed: true
  },
  {
    label: "Turnover target",
    prop: "turnover"
  },
  {
    label: "Turnover achieve %",
    prop: "turnover_ach",
    slot: "turnoverAch"
  }
];

const saleColumns: TableColumnList = [
  {
    label: "Target",
    width: "80",
    prop: "name",
    fixed: true
  },
  {
    label: "Enquiries",
    prop: "enquiries",
    minWidth: "70"
  },
  {
    label: "Allocated",
    minWidth: "80",
    prop: "allocated"
  },
  {
    label: "Sales",
    prop: "sales",
    minWidth: "50"
  },
  {
    label: "Booking%",
    prop: "booking",
    minWidth: "80",
    slot: "booking"
  },
  {
    label: "Conversions",
    prop: "conversions",
    minWidth: "100"
  },
  {
    label: "Average sales",
    prop: "avg_sale",
    minWidth: "100"
  },
  {
    label: "Under sell %",
    prop: "under_sell",
    minWidth: "100"
  },
  {
    label: "Gross sales",
    prop: "gross_sale",
    minWidth: "100"
  },
  {
    label: "Enquiries achieve %",
    prop: "eq_ach",
    minWidth: "120",
    slot: "eqAch"
  },
  {
    label: "Sales achieve %",
    prop: "sale_ach",
    minWidth: "110",
    slot: "saleAch"
  }
];

// /** 分页配置 */
// const pagination = reactive<PaginationProps>({
//   pageSize: 999,
//   currentPage: 1,
//   layout: "prev,next,pager,total",
//   total: 0,
//   align: "left",
//   prevText: "Previous",
//   nextText: "Next"
// });
const props = defineProps({
  companyRegion: {
    type: String,
    default: ""
  },
  type: {
    type: String,
    default: "sale"
  }
});
watch(
  () => props.companyRegion,
  _value => {
    console.log("------");
    loadData();
  },
  {
    immediate: true
  }
);
watch(
  () => props.type,
  _value => {
    loadData();
  },
  {
    immediate: true
  }
);

function loadData() {
  loading.value = true;
  dashboardControllerGetTarget(
    props.companyRegion,
    dayjs(new Date()).format("YYYY-MM")
  )
    .then(res => {
      loading.value = false;
      tableData.value = res.data;
    })
    .catch(_err => {
      loading.value = false;
    });
}

function getClass(value, type = null) {
  if (type == "booking") {
    if (value < 35) {
      return "text-red";
    } else if (value > 35 && value < 40) {
      return "text-orange";
    } else {
      return "text-green";
    }
  } else {
    if (value <= 90) {
      return "text-red";
    } else if (value > 90 && value < 100) {
      return "text-orange";
    } else {
      return "text-green";
    }
  }
}

onMounted(() => {
  // loadData();
});
</script>
<template>
  <div class="mt-[15px]">
    <pure-table
      row-key="id"
      showOverflowTooltip
      size="small"
      :border="true"
      :loading="loading"
      :loading-config="{ background: 'transparent' }"
      :data="tableData"
      :columns="props.type == 'sale' ? saleColumns : productColumns"
      class="target-list-page !text-[13px]"
    >
      <template #booking="{ row }">
        <span :class="getClass(row.booking, 'booking')">
          {{ row.booking }}</span
        >
      </template>
      <template #eqAch="{ row }">
        <span :class="getClass(row.eq_ach)"> {{ row.eq_ach }}</span>
      </template>
      <template #saleAch="{ row }">
        <span :class="getClass(row.sale_ach)"> {{ row.sale_ach }}</span>
      </template>
      <template #turnoverAch="{ row }">
        <span :class="getClass(row.turnover_ach)"> {{ row.turnover_ach }}</span>
      </template>
    </pure-table>
    <!-- <el-pagination
      :current-page="pagination.currentPage"
      :pageSize="pagination.pageSize"
      :small="true"
      layout="prev,next,slot"
      :total="pagination.total"
      class="custom-page"
      :prevText="pagination['prevText']"
      :nextText="pagination['nextText']"
      @current-change="onCurrentChange"
    >
      <template #default>
        <div class="page-right">
          Page {{ pagination.currentPage }} of {{ pagination["page-count"] }}
        </div>
      </template>
    </el-pagination> -->
  </div>
</template>

<style scoped>
.text-red {
  color: red;
}

.text-orange {
  color: orange;
}

.text-green {
  color: green;
}
</style>
