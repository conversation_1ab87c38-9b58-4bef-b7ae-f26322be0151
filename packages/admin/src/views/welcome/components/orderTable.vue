<script setup lang="ts">
import { ref, onMounted, reactive, watch } from "vue";
import type { PaginationProps } from "@pureadmin/table";
import { orderControllerIndex } from "@/api/admin/order";
import { allResults } from "@/views/orders/components/data.ts";
import CirCleText from "@/components/CircleText";
import EmptySpan from "@/components/EmptySpan";
import dayjs from "dayjs";
import { formatTime } from "@/utils/form";

const tableData = ref([]);
const loading = ref(true);

const columns: TableColumnList = [
  {
    label: "Order ID",
    minWidth: "80",
    prop: "lead.id",
    fixed: true
  },
  {
    label: "Client",
    slot: "client",
    minWidth: "110"
  },
  {
    label: "Phone",
    minWidth: "100",
    slot: "phone"
  },
  {
    label: "Followup Date",
    prop: "followup_date",
    slot: "followup_date",
    minWidth: "120"
  },
  {
    label: "Product",
    slot: "product",
    minWidth: "100"
  },

  {
    label: "Order Result",
    slot: "result",
    minWidth: "130"
  },
  {
    label: "Appt Setter",
    slot: "apptSetter",
    minWidth: "150"
  },
  {
    label: "Production Staff",
    slot: "production_staff",
    minWidth: "110"
  }
];

/** 分页配置 */
const pagination = reactive<PaginationProps>({
  pageSize: 10,
  currentPage: 1,
  layout: "prev,next,pager,total",
  total: 0,
  align: "left",
  prevText: "Previous",
  nextText: "Next"
});
const props = defineProps({
  companyRegion: {
    type: String,
    default: ""
  }
});
watch(
  () => props.companyRegion,
  _value => {
    console.log("=====");
    loadData();
  },
  {
    immediate: true
  }
);
function onCurrentChange(_page) {
  pagination.currentPage = _page;
  loadData();
}
function loadData() {
  loading.value = true;
  const params = {
    search: "",
    filter: ``,
    sort: "-id",
    _with: "payments,productStaff,lead.client,lead.apptSetter",
    withCount: "",
    page: pagination.currentPage,
    size: pagination.pageSize
  };
  const filterInfo = [];
  const startDate = dayjs().format("YYYY-MM-DD 00:00:00");
  const endDate = dayjs().format("YYYY-MM-DD 23:59:59");
  filterInfo.push(`followup_date:gte:${startDate}`);
  filterInfo.push(`followup_date:lte:${endDate}`);
  if (props.companyRegion) {
    filterInfo.push(
      "company_region:in:" + props.companyRegion.replace(",", "|")
    );
  }
  params.filter = filterInfo.join(",");

  const { search, filter, sort, _with, withCount, page, size } = params;
  orderControllerIndex(search, filter, sort, _with, withCount, page, size)
    .then(res => {
      loading.value = false;
      tableData.value = res.data;
      pagination["page-count"] = res.meta.last_page;
      pagination.currentPage = res.meta.current_page;
      pagination.total = res.meta.total;
    })
    .catch(_err => {
      loading.value = false;
    });
}

// get current row result icon
function getCurrentResultIcon(result) {
  if (!result) {
    return;
  }
  if (typeof result === "string") {
    return allResults.find(item => item.value === result)?.icon;
  }
}

onMounted(() => {
  loadData();
});
</script>
<template>
  <div class="mt-[15px]">
    <pure-table
      row-key="id"
      alignWhole="center"
      showOverflowTooltip
      size="small"
      :border="true"
      :loading="loading"
      :loading-config="{ background: 'transparent' }"
      :data="tableData"
      :columns="columns"
      class="leads-list-page"
    >
      <template #client="{ row }">
        <span class="iconfont just-icon-client mr-2" />
        <span v-if="row.lead && row.lead.client && row.lead.client.given_name"
          >{{ row.lead.client.given_name }} {{ row.lead.client.surname }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #phone="{ row }">
        <span v-if="row.lead && row.lead.client && row.lead.client.phone">{{
          row.lead.client.phone
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>

      <template #apptSetter="{ row }">
        <EmptySpan
          :text="
            row.lead && row.lead.appt_setter ? row.lead.appt_setter.name : ''
          "
        />
      </template>
      <template #production_staff="{ row }">
        <div v-if="row.product_staff && row.product_staff.login_name">
          <CirCleText
            :text="row.product_staff.login_name"
            :size="20"
            fontSize="10px"
            :customBgColor="row.product_staff.color"
          />
          {{ row.product_staff.login_name }}
        </div>
        <span v-else>Empty</span>
      </template>
      <template #product="{ row }">
        <EmptySpan :text="row.lead ? row.lead.product : ''" />
      </template>
      <template #followup_date="{ row }">
        <span v-if="row.followup_date">{{
          formatTime(row.followup_date, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #result="{ row }">
        <div class="result-container">
          <span
            :class="
              'mr-1 iconfont just-icon-' + getCurrentResultIcon(row.result)
            "
          />
          <span class="capitalize">{{ row.result }}</span>
        </div>
      </template>
    </pure-table>
    <el-pagination
      :current-page="pagination.currentPage"
      :pageSize="pagination.pageSize"
      :small="true"
      layout="prev,next,slot"
      :total="pagination.total"
      class="custom-page"
      :prevText="pagination['prevText']"
      :nextText="pagination['nextText']"
      @current-change="onCurrentChange"
    >
      <template #default>
        <div class="page-right">
          Page {{ pagination.currentPage }} of {{ pagination["page-count"] }}
        </div>
      </template>
    </el-pagination>
  </div>
</template>
