<script setup lang="ts">
import { transformI18n } from "@/plugins/i18n";
import { saleSummaryDefaultData, productionSummaryDefaultData } from "./data";
import { ElLoading } from "element-plus";
import { ref, onMounted, nextTick, reactive } from "vue";
import { dashboardControllerSummary } from "@/api/admin/dashboard";
import leadGraph from "./leadsGraph.vue";
import leadsTable from "./components/leadsTable.vue";
import orderTable from "./components/orderTable.vue";
import serviceTable from "./components/serviceTable.vue";
import appointmentList from "./components/appointmentList.vue";
import { useCommonStoreHook } from "@/store/modules/common";
import customDate from "./components/customDate.vue";
import { storageLocal } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";
import targetTable from "./components/targetTable.vue";

const { userBaseInfo, isProductAssistant } = useNav();

const companyRegion = ref([]);
const currentCompanyRegion = ref("");
const activeName = ref(isProductAssistant.value ? "production" : "sale");
const dialogPanel = ref();
const mv = ref();
const biLoading = ref(true);
const biUrl = ref(
  "https://bi.jqcrm.com.au/public/dashboard/12acbcad-4ee7-469f-9de4-f49f503875b1"
);
const leadGraphRef = ref(null);
const productCategoryList = ref([]);
const salesSummaryData = ref([saleSummaryDefaultData]);
const productionSummaryData = ref([productionSummaryDefaultData]);
const form = reactive({
  type: "leads",
  product_category_id: null,
  timeRangeType: "week",
  start: null,
  end: null
});
const timeRangeTypeOption = [
  {
    label: "Year",
    value: "year"
  },
  {
    label: "Month",
    value: "month"
  },
  {
    label: "Week",
    value: "week"
  },
  {
    label: "Day",
    value: "day"
  },
  {
    label: "Custom",
    value: "custom"
  }
];

const CustomFormRef = ref(null);
const preTimeRangeType = ref("week");
function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}
function changeCompany() {
  getDashboardSummay();
  if (companyRegion.value.length) {
    currentCompanyRegion.value = companyRegion.value.join(",");
  } else {
    currentCompanyRegion.value = "";
  }
}
function getDashboardSummay() {
  if (form.timeRangeType == "custom") {
    if (form.start && form.end) {
      handleCustomConfirm([form.start, form.end]);
    } else {
      CustomFormRef.value.show();
    }
  } else {
    storageLocal().setItem(
      "dashboardFilterProductCagetorayId",
      form.product_category_id
    );
    storageLocal().setItem("dashboardFilterTimeRangeType", form.timeRangeType);
    dialogLoading();
    const companyRegionStr = companyRegion.value.length
      ? companyRegion.value.join(",")
      : null;

    dashboardControllerSummary(
      form.timeRangeType,
      form.product_category_id,
      null,
      null,
      companyRegionStr
    )
      .then(res => {
        dialogPanel.value.close();
        if (res.success && res.data) {
          if (res.data.saleData) {
            const saleSummary = [];
            saleSummaryDefaultData.forEach((item, index) => {
              saleSummaryDefaultData[index].value =
                res.data.saleData[item.index];
              saleSummary.push(saleSummaryDefaultData[index]);
            });
            salesSummaryData.value = saleSummary;
          }

          if (res.data.productionData) {
            const productionData = [];
            productionSummaryDefaultData.forEach((item, index) => {
              productionSummaryDefaultData[index].value =
                res.data.productionData[item.index];
              productionData.push(productionSummaryDefaultData[index]);
            });
            productionSummaryData.value = productionData;
          }
        }
      })
      .catch(() => {
        dialogPanel.value.close();
      });
    preTimeRangeType.value = form.timeRangeType;
  }
}

function handleCustomConfirm(e) {
  dialogLoading();
  form.start = e.value ? e.value[0] : e[0];
  form.end = e.value ? e.value[1] : e[1];
  storageLocal().setItem("dashboardFilterTimeRangeStart", form.start);
  storageLocal().setItem("dashboardFilterTimeRangeEnd", form.end);
  storageLocal().setItem("dashboardFilterTimeRangeType", form.timeRangeType);
  const companyRegionStr = companyRegion.value.length
    ? companyRegion.value.join(",")
    : null;
  dashboardControllerSummary(
    form.timeRangeType,
    form.product_category_id,
    form.start,
    form.end,
    companyRegionStr
  )
    .then(res => {
      dialogPanel.value.close();
      if (res.success && res.data) {
        if (res.data.saleData) {
          const saleSummary = [];
          saleSummaryDefaultData.forEach((item, index) => {
            saleSummaryDefaultData[index].value = res.data.saleData[item.index];
            saleSummary.push(saleSummaryDefaultData[index]);
          });
          salesSummaryData.value = saleSummary;
        }

        if (res.data.productionData) {
          const productionData = [];
          productionSummaryDefaultData.forEach((item, index) => {
            productionSummaryDefaultData[index].value =
              res.data.productionData[item.index];
            productionData.push(productionSummaryDefaultData[index]);
          });
          productionSummaryData.value = productionData;
        }
      }
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}
function showCustom() {
  if (form.timeRangeType == "custom") {
    CustomFormRef.value.show(form);
  }
}
function handleCustomConcel() {
  console.log("=======");
  form.timeRangeType = preTimeRangeType.value;
}

function initData() {
  form.product_category_id = storageLocal().getItem(
    "dashboardFilterProductCagetorayId"
  );
  form.timeRangeType = storageLocal().getItem("dashboardFilterTimeRangeType");
  form.start = storageLocal().getItem("dashboardFilterTimeRangeStart");
  form.end = storageLocal().getItem("dashboardFilterTimeRangeEnd");
}

function onIframeLoad() {
  biLoading.value = false;
}

defineOptions({
  name: "Dashboard"
});

onMounted(() => {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  initData();
  nextTick(() => {
    if (form.timeRangeType == "custom" && form.start && form.end) {
      handleCustomConfirm([form.start, form.end]);
    } else {
      getDashboardSummay();
    }
  });
});
</script>

<template>
  <div ref="mv" class="p-5 bg-white">
    <h1 class="text-3xl font-bold">
      {{ transformI18n("common.welcomeBack") }}
    </h1>
    <h2 class="text-base font-normal text-[#656F7D]">
      {{ transformI18n("common.dashboardDesc") }}
    </h2>
    <!-- header summary -->
    <div class="mt-[10px] relative">
      <el-tabs v-model="activeName">
        <el-tab-pane label="Sales" name="sale">
          <el-row :gutter="24">
            <div
              class="w-1/3 xl:w-1/5 p-2.5"
              v-for="(item, index) in salesSummaryData"
              :key="index"
            >
              <el-card
                class="line-card p-5 !rounded-lg border-[1px]-[#EAECF0] h-full"
                shadow="never"
              >
                <span class="text-base font-medium text-[#101828]">
                  {{ item.name }}
                </span>
                <div class="flex justify-between items-center mt-2.5">
                  <span
                    v-if="item.index == 'amount'"
                    class="text-[40px] font-medium leading-[44px] text-[#101828]"
                  >
                    ${{ item.value }}
                  </span>
                  <span
                    v-else
                    class="text-[40px] font-medium leading-[44px] text-[#101828]"
                  >
                    {{ item.value }}
                  </span>
                  <span
                    class="rounded-[10px] flex justify-center items-center w-11 h-11 border-[1px] border-[#EAECF0]"
                    :style="{
                      backgroundColor: item.bgColor
                    }"
                  >
                    <i
                      :class="'!text-2xl iconfont just-icon-' + item.icon"
                      :style="{
                        color: item.color
                      }"
                    />
                  </span>
                </div>
              </el-card>
            </div>
          </el-row>
          <el-row :gutter="24">
            <div v-loading="biLoading" class="w-full mx-2.5">
              <iframe
                :src="biUrl"
                frameborder="0"
                width="100%"
                @load="onIframeLoad()"
                class="external-webcard"
              />
            </div>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="Production" name="production">
          <el-row :gutter="24">
            <div
              class="w-1/3 xl:w-1/5 p-2.5"
              v-for="(item, index) in productionSummaryData"
              :key="index"
            >
              <el-card
                class="line-card p-5 !rounded-lg border-[1px]-[#EAECF0] h-full"
                shadow="never"
              >
                <span class="text-base font-medium text-[#101828]">
                  {{ item.name }}
                </span>
                <div class="flex justify-between items-center mt-2.5">
                  <span
                    v-if="item.index == 'amount'"
                    class="text-[40px] font-medium leading-[44px] text-[#101828]"
                  >
                    ${{ item.value }}
                  </span>
                  <span
                    v-else
                    class="text-[40px] font-medium leading-[44px] text-[#101828]"
                  >
                    {{ item.value }}
                  </span>
                  <span
                    class="rounded-[10px] flex justify-center items-center w-11 h-11 border-[1px] border-[#EAECF0]"
                    :style="{
                      backgroundColor: item.bgColor
                    }"
                  >
                    <i
                      :class="'!text-2xl iconfont just-icon-' + item.icon"
                      :style="{
                        color: item.color
                      }"
                    />
                  </span>
                </div>
              </el-card>
            </div>
          </el-row>
        </el-tab-pane>
      </el-tabs>

      <div class="flex justify-between absolute top-[-5px] right-0">
        <el-form ref="formRef" :model="form">
          <el-select
            v-if="userBaseInfo.isPlatformUser"
            class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
            v-model="companyRegion"
            @change="changeCompany()"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :placeholder="'Company'"
          >
            <el-option
              v-for="item in userBaseInfo.companyRegion"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-select
            v-model="form.product_category_id"
            @change="getDashboardSummay()"
            clearable
            placeholder="Select Category"
            class="border-ele mx-[10px]"
          >
            <el-option
              v-for="item in productCategoryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <el-select
            v-model="form.timeRangeType"
            class="border-ele w-[120px]"
            @change="getDashboardSummay()"
          >
            <el-option
              v-for="(item, index) in timeRangeTypeOption"
              :key="index"
              :label="item.label"
              :value="item.value"
              @click="showCustom"
            >
              <div class="flex items-center">
                <span class="capitalize">{{ item.label }}</span>
              </div>
            </el-option>
            <template #prefix>
              <span class="iconfont just-icon-calendar-todo" />
            </template>
          </el-select>
        </el-form>
      </div>
    </div>
    <el-row :gutter="24">
      <div class="w-full">
        <div class="mt-2 mb-2" v-if="activeName == 'sale'">
          <div
            class="px-[10px] pb-[10px] mx-2.5 rounded-lg border rounded-lg border-[#EAECF0] grow"
          >
            <p class="mt-2">Sales Target</p>
            <targetTable
              ref="saleTargetTableRef"
              :companyRegion="currentCompanyRegion"
              :type="activeName"
            />
          </div>
        </div>
        <div class="mt-2 mb-2" v-if="activeName == 'production'">
          <div
            class="px-[10px] pb-[10px] mx-2.5 rounded-lg border rounded-lg border-[#EAECF0] grow"
          >
            <p class="mt-2">Production Target</p>
            <targetTable
              ref="productTargetTableRef"
              :companyRegion="currentCompanyRegion"
              :type="activeName"
            />
          </div>
        </div>
      </div>
    </el-row>
    <el-row :gutter="24">
      <div class="w-full xl:w-3/3 flex">
        <div class="w-1/3">
          <leadGraph :companyRegion="currentCompanyRegion" ref="leadGraphRef" />
        </div>
        <div class="w-1/3">
          <leadGraph
            ref="salesAmountGraphRef"
            :title="'Sales Amount'"
            :pieType="'salesAmount'"
            :companyRegion="currentCompanyRegion"
            :pieCenterText="'Total'"
          />
        </div>
        <div class="w-1/3">
          <leadGraph
            ref="completionAcountRef"
            :title="'Completion Acount'"
            :pieType="'completionAcount'"
            :companyRegion="currentCompanyRegion"
            :pieCenterText="'Total'"
          />
        </div>
      </div>
      <div class="w-full xl:w-2/3 flex flex-col">
        <div
          class="px-[10px] mx-2.5 rounded-lg border rounded-lg border-[#EAECF0] grow"
        >
          <div class="action mt-2.5">
            <el-radio-group v-model="form.type" size="default">
              <el-radio-button label="leads">Leads</el-radio-button>
              <el-radio-button label="order">Production Order</el-radio-button>
              <el-radio-button label="service">Service Order</el-radio-button>
            </el-radio-group>
          </div>
          <orderTable
            ref="orderTableRef"
            :companyRegion="currentCompanyRegion"
            v-if="form.type == 'order'"
          />
          <serviceTable
            ref="serviceTableRef"
            :companyRegion="currentCompanyRegion"
            v-else-if="form.type == 'service'"
          />
          <leadsTable
            ref="leadsTableRef"
            :companyRegion="currentCompanyRegion"
            v-else
          />
        </div>
      </div>
      <div
        class="p-2.5 rounded-lg border rounded-lg border-[#EAECF0] w-full xl:w-1/3"
      >
        <appointmentList
          :companyRegion="currentCompanyRegion"
          ref="appointmentListRef"
        />
      </div>
    </el-row>
    <customDate
      ref="CustomFormRef"
      @cancel="handleCustomConcel"
      @confirm="handleCustomConfirm"
    />
  </div>
</template>

<style scoped>
.border-ele {
  padding: 0 5px;
  border: 1px solid #d0d5dd;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
}
</style>
