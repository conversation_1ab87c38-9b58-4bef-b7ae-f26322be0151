<script setup lang="ts">
import { ref, onMounted, reactive, watch, nextTick } from "vue";
import { useECharts } from "@pureadmin/utils";
import { colorList } from "./data";
import {
  dashboardControllerGetLeadStatisticByTimeRange,
  dashboardControllerGetSalesAmountStatisticBySoldDateRange
} from "@/api/admin/dashboard";
import customDate from "./components/customDate.vue";

const form = reactive({
  echartType: "line", //line pie
  timeRangeType: "week",
  start: null,
  end: null
});
const timeRangeTypeOption = [
  {
    label: "Year",
    value: "year"
  },
  {
    label: "Month",
    value: "month"
  },
  {
    label: "Week",
    value: "week"
  },
  {
    label: "Day",
    value: "day"
  },
  {
    label: "Custom",
    value: "custom"
  }
];
const chartRef = ref();
const CustomFormRef = ref(null);
const preTimeRangeType = ref(null);
const preChartType = ref(null);
const { setOptions } = useECharts(chartRef, {});
const xData = ref([]);
const lineData = ref(null);
const pieData = ref(null);
const lineChartOption = {
  dataZoom: [
    {
      start: 0,
      end: 100,
      height: 10
    }
  ],
  xAxis: {
    type: "category",
    show: true,
    data: xData.value,
    axisLine: {
      show: false
    },
    axisTick: {
      show: true
    }
    // axisLabel: {
    //   // interval: 1
    // }
  },
  legend: {
    icon: "circle",
    left: "right",
    top: 2,
    align: "left",
    width: "82%",
    itemGap: 10,
    itemWidth: 8
  },
  grid: {
    top: 50,
    right: 10
  },
  tooltip: {
    trigger: "axis"
  },
  yAxis: {
    type: "value"
  },
  series: []
};
const pieChartOption = {
  tooltip: {
    trigger: "item"
  },
  legend: {
    icon: "circle",
    top: "center",
    right: "10px",
    orient: "vertical",
    itemGap: 20,
    itemWidth: 8,
    formatter: function (name) {
      return name;
    }
  },
  series: [
    {
      name: "",
      type: "pie",
      radius: ["30%", "45%"],
      center: ["25%", "50%"],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: "center",
        rich: {
          a: {
            color: "#101828",
            fontSize: 28,
            fontWidth: 500
          },
          b: {
            color: "#656F7D",
            fontSize: 14
          }
        },
        formatter: function (_params) {
          let str = `{a|0}\n`;
          str += "{b|Total Leads}";
          return str;
        }
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 40,
          fontWeight: "bold"
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ]
};
const props = defineProps({
  title: {
    type: String,
    default: "Total Leads"
  },
  pieCenterText: {
    type: String,
    default: "Total Leads"
  },
  pieType: {
    type: String,
    default: "leads" //salesAmount completionAcount
  },
  companyRegion: {
    type: String,
    default: ""
  }
});
watch(
  () => props.companyRegion,
  _value => {
    getData();
  },
  {
    immediate: true
  }
);

watch(
  () => form,
  _val => {
    // enderEcharts();
    if (
      form.timeRangeType != "custom" &&
      form.timeRangeType != preTimeRangeType.value
    ) {
      getData();
    }
    if (form.echartType != preChartType.value) {
      getData();
    }
  },
  {
    immediate: true,
    deep: true
  }
);

function getData() {
  switch (props.pieType) {
    case "salesAmount":
      getSalesAmount();
      break;
    case "completionAcount":
      getSalesAmount();
      break;
    default:
      getTotalLeadsData();
  }
  preTimeRangeType.value = form.timeRangeType;
  preChartType.value = form.echartType;
}

function handleCustomConfirm(e) {
  form.start = e.value[0];
  form.end = e.value[1];
  getData();
}
function showCustom() {
  if (form.timeRangeType == "custom") {
    console.log(preTimeRangeType);
    CustomFormRef.value.show(preTimeRangeType.value == "custom" ? form : null);
  }
}
function handleCustomConcel() {
  form.timeRangeType = preTimeRangeType.value;
}

function getSalesAmount() {
  xData.value = [];
  lineData.value = null;
  pieData.value = null;
  dashboardControllerGetSalesAmountStatisticBySoldDateRange(
    form.timeRangeType,
    form.start,
    form.end,
    props.pieType,
    props.companyRegion
  ).then(res => {
    if (res?.data?.xData && res.data.lineData) {
      xData.value = res.data.xData;
      lineData.value = res.data.lineData;
      pieData.value = res.data.pieData;
    }
    renderEcharts();
  });
}

function getTotalLeadsData() {
  xData.value = [];
  lineData.value = null;
  pieData.value = null;
  dashboardControllerGetLeadStatisticByTimeRange(
    form.timeRangeType,
    form.start,
    form.end,
    props.companyRegion
  ).then(res => {
    if (res?.data?.xData && res.data.lineData) {
      xData.value = res.data.xData;
      lineData.value = res.data.lineData;
      pieData.value = res.data.pieData;
    }
    renderEcharts();
  });
}
async function renderEcharts() {
  await nextTick();
  if (form.echartType === "pie") {
    pieDataHandle();
  } else {
    lineDatahandle();
  }
}

function lineDatahandle() {
  const option = JSON.parse(JSON.stringify(lineChartOption));
  option.xAxis.data = xData.value;
  if (lineData.value) {
    const handleData = [];
    for (const i in lineData.value) {
      let color1 = null;
      let color2 = null;
      if (colorList[i]) {
        color1 = colorList[i][0];
        color2 = colorList[i][1];
      } else {
        color1 = "rgba(105, 105, 205, 1)";
        color2 = "rgba(105, 105, 205, 0)";
      }
      const item = {
        name: i,
        data: lineData.value[i],
        type: "line",
        smooth: true,
        itemStyle: {
          color: color1
        },
        lineStyle: {
          color: color1,
          width: 2
        },
        showSymbol: true,
        areaStyle: {
          opacity: 0.2,
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 0.3,
            colorStops: [
              {
                offset: 0,
                color: color1 // 0% 处的颜色
              },
              {
                offset: 1,
                color: color2 // 100% 处的颜色
              }
            ],
            global: false // default is false
          }
        }
      };
      handleData.push(item);
    }
    option.series = handleData;
  }
  setOptions(option);
}

function pieDataHandle() {
  let total = 0;
  const handleData = [];
  const option = JSON.parse(JSON.stringify(pieChartOption));
  if (pieData.value) {
    for (const i in pieData.value) {
      const value = isNaN(parseInt(pieData.value[i]))
        ? 0
        : parseInt(pieData.value[i]);
      total += value;
      let color = null;
      if (colorList[i]) {
        color = colorList[i][0];
      }
      handleData.push({
        value,
        name: i,
        itemStyle: { color: color }
      });
    }
    option.legend.formatter = name => {
      const target = pieData.value[name];
      let percentNum = (target / total) * 100;
      percentNum = isNaN(percentNum) ? 0 : percentNum;
      const arr = [name + " " + percentNum.toFixed(2) + "%"];
      return arr.join("\n");
    };
    option.series[0].label.formatter = _name => {
      let str = `{a|${total}}\n`;
      str += `{b|${props.pieCenterText}}`;
      return str;
    };
    option.series[0].data = handleData;
  }

  setOptions(option);
}
onMounted(() => {});
</script>
<template>
  <div class="m-2.5 border rounded-lg border-[#EAECF0] p-[20px] pb-0 h-[95%]">
    <div class="chart-header flex justify-between items-center mb-[20px]">
      <div class="echartTitle">{{ props.title }}</div>
      <div class="action">
        <el-radio-group
          v-model="form.echartType"
          size="small"
          class="!flex-nowrap"
        >
          <el-radio-button label="line">Trend</el-radio-button>
          <el-radio-button label="pie">Amount Percentage</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div class="absolute z-10 mr-[5px]">
      <el-select
        v-model="form.timeRangeType"
        size="small"
        class="border rounded-lg border-[#EAECF0] pl-[3px] w-[92px]"
      >
        <el-option
          v-for="(item, index) in timeRangeTypeOption"
          :key="index"
          :label="item.label"
          @click="showCustom"
          :value="item.value"
        >
          <div class="flex items-center">
            <span class="capitalize">{{ item.label }}</span>
          </div>
        </el-option>
        <template #prefix>
          <span class="iconfont just-icon-calendar-todo" />
        </template>
      </el-select>
    </div>
    <div ref="chartRef" style="width: 100%; height: 400px" />
    <customDate
      ref="CustomFormRef"
      @cancel="handleCustomConcel"
      @confirm="handleCustomConfirm"
    />
  </div>
</template>
