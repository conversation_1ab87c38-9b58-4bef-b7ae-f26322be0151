<script setup lang="ts">
import { ref, onMounted, reactive, watch, nextTick } from "vue";
import { useECharts } from "@pureadmin/utils";
import { dashboardControllerGetAppointmentStatistic } from "@/api/admin/dashboard";
import { colorList } from "./data";

const form = reactive({
  timeRangeType: "month"
});
const timeRangeTypeOption = [
  {
    label: "Year",
    value: "year"
  },
  {
    label: "Month",
    value: "month"
  },
  {
    label: "Week",
    value: "week"
  }
];
const chartRef = ref();
const { setOptions } = useECharts(chartRef, {});
const yearData = ref([]);
const monthData = ref(null);
const weekData = ref(null);
const pieChartOption = {
  tooltip: {
    trigger: "item"
  },
  legend: {
    icon: "circle",
    top: "center",
    right: "10px",
    orient: "vertical",
    itemGap: 20,
    itemWidth: 8,
    formatter: function (name) {
      return name;
    }
  },
  series: [
    {
      name: "",
      type: "pie",
      radius: ["30%", "45%"],
      center: ["25%", "50%"],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: "center",
        rich: {
          a: {
            color: "#101828",
            fontSize: 28,
            fontWidth: 500
          },
          b: {
            color: "#656F7D",
            fontSize: 14
          }
        },
        formatter: function (_params) {
          let str = `{a|0}\n`;
          str += "{b|Total Appt}";
          return str;
        }
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 40,
          fontWeight: "bold"
        }
      },
      labelLine: {
        show: false
      },
      data: []
    }
  ]
};
watch(
  () => form,
  _val => {
    renderEcharts();
  },
  {
    immediate: false,
    deep: true
  }
);

function getData() {
  yearData.value = [];
  monthData.value = [];
  weekData.value = null;
  dashboardControllerGetAppointmentStatistic().then(res => {
    if (res?.data) {
      yearData.value = res.data.year || [];
      monthData.value = res.data.month || [];
      weekData.value = res.data.week || [];
    }
    renderEcharts();
  });
}
async function renderEcharts() {
  await nextTick();
  let pieData = null;
  switch (form.timeRangeType) {
    case "year":
      pieData = yearData.value;
      break;
    case "month":
      pieData = monthData.value;
      break;
    case "week":
      pieData = weekData.value;
      break;
  }
  pieDataHandle(pieData);
}

function pieDataHandle(pieData) {
  let total = 0;
  const handleData = [];
  const option = JSON.parse(JSON.stringify(pieChartOption));
  if (pieData) {
    for (const i in pieData) {
      const value = isNaN(parseInt(pieData[i])) ? 0 : parseInt(pieData[i]);
      total += value;
      let color = null;
      if (colorList[i]) {
        color = colorList[i][0];
      }
      handleData.push({
        value,
        name: i,
        itemStyle: { color: color }
      });
    }
    option.legend.formatter = name => {
      const target = pieData[name];
      let percentNum = (target / total) * 100;
      percentNum = isNaN(percentNum) ? 0 : percentNum;
      const arr = [name + " " + percentNum.toFixed(2) + "%"];
      return arr.join("\n");
    };
    option.series[0].label.formatter = _name => {
      let str = `{a|${total}}\n`;
      str += "{b|Total Appt}";
      return str;
    };
    option.series[0].data = handleData;
  }
  setOptions(option);
}
onMounted(() => {
  getData();
});
</script>
<template>
  <div
    class="m-2.5 ml-0 border rounded-lg border-[#EAECF0] p-[20px] pb-0 h-[95%]"
  >
    <div class="chart-header flex justify-between items-center mb-[20px]">
      <div class="echartTitle">New Appointment</div>
      <div class="action">
        <el-select
          v-model="form.timeRangeType"
          class="border rounded-lg border-[#EAECF0] pl-[3px] w-[100px]"
        >
          <el-option
            v-for="(item, index) in timeRangeTypeOption"
            :key="index"
            :label="item.label"
            :value="item.value"
          >
            <div class="flex items-center">
              <span class="capitalize">{{ item.label }}</span>
            </div>
          </el-option>
          <template #prefix>
            <span class="iconfont just-icon-calendar-todo" />
          </template>
        </el-select>
      </div>
    </div>
    <div ref="chartRef" style="width: 100%; height: 400px" />
  </div>
</template>
