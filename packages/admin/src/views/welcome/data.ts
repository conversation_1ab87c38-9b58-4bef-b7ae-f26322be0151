import { transformI18n } from "@/plugins/i18n";

// sale summary data
export const saleSummaryDefaultData = [
  {
    name: transformI18n("summary.totalLeads"),
    value: 0,
    icon: "lead",
    color: "#1A6FE6",
    bgColor: "rgba(26, 111, 230, 0.03)",
    index: "totalLeads"
  },
  {
    name: transformI18n("summary.salesAppointment"),
    value: 0,
    icon: "appointed",
    color: "#6969CD",
    bgColor: "rgba(105, 105, 205, 0.03)",
    index: "salesAppointment"
  },
  {
    name: transformI18n("summary.soldLeads"),
    value: 0,
    icon: "lead",
    color: "#E2206D",
    bgColor: "rgba(226, 32, 109, 0.03)",
    index: "soldLeads"
  },
  {
    name: transformI18n("summary.depositAmount"),
    value: 0,
    icon: "quoted",
    color: "#7F34B9",
    bgColor: "rgba(127, 52, 185, 0.03)",
    index: "depositAmount"
  },
  {
    name: transformI18n("summary.saleAmount"),
    value: 0,
    icon: "quoted",
    color: "#EC590F",
    bgColor: "rgba(236, 89, 15, 0.03)",
    index: "saleAmount"
  }
];

// production summary data

export const productionSummaryDefaultData = [
  {
    name: transformI18n("summary.cmAppointment"),
    value: 0,
    icon: "cm",
    color: "#1A6FE6",
    bgColor: "rgba(26, 111, 230, 0.03)",
    index: "cmAppointment"
  },
  {
    name: transformI18n("summary.installAppointment"),
    value: 0,
    icon: "installation",
    color: "#6969CD",
    bgColor: "rgba(105, 105, 205, 0.03)",
    index: "installAppointment"
  },
  {
    name: transformI18n("summary.serviceAppointment"),
    value: 0,
    icon: "service",
    color: "#E2206D",
    bgColor: "rgba(226, 32, 109, 0.03)",
    index: "serviceAppointment"
  },
  {
    name: transformI18n("summary.installAmount"),
    value: 0,
    icon: "quoted",
    color: "#7F34B9",
    bgColor: "rgba(127, 52, 185, 0.03)",
    index: "installAmount"
  },
  {
    name: transformI18n("summary.completionAmount"),
    value: 0,
    icon: "quoted",
    color: "#EC590F",
    bgColor: "rgba(236, 89, 15, 0.03)",
    index: "completionAmount"
  }
];
export const colorList = {
  Landscaping: ["rgba(105, 105, 205, 1)", "rgba(105, 105, 205, 0)"],
  Blinds: ["rgba(29, 175, 238, 1)", "rgba(29, 175, 238, 0)"],
  Roof: ["rgba(226, 32, 109, 1)", "rgba(226, 32, 109, 0)"],
  "Garage Door": ["rgba(144, 180, 34, 1)", "rgba(144, 180, 34, 0)"],
  Patio: ["rgba(247, 138, 77, 1)", "rgba(247, 138, 77,0)"]
};
