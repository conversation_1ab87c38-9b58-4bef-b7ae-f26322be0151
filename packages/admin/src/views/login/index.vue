<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { loginRules } from "./utils/rule";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { useCommonStoreHook } from "@/store/modules/common";
import { initRouter, getTopMenu } from "@/router/utils";
import { bg, avatar } from "./utils/static";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ref, reactive, watch, onMounted, onBeforeUnmount } from "vue";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { commonControllerBasicData } from "@/api/admin/basic-data";

import Lock from "@iconify-icons/ri/lock-line";
import User from "@iconify-icons/ri/user-line";
import { transformI18n } from "@/plugins/i18n";
import { useI18n } from "vue-i18n";

defineOptions({
  name: "Login"
});
const router = useRouter();
const loading = ref(false);
const checked = ref(false);
const policyChecked = ref(true);
const ruleFormRef = ref<FormInstance>();

const { initStorage } = useLayout();

initStorage();
const { locale } = useI18n();
console.log(`当前系统采用的语言是：${locale.value}`);
const { dataThemeChange } = useDataThemeChange();
dataThemeChange();

const ruleForm = reactive({
  username: "",
  password: ""
});

const onLogin = async (formEl: FormInstance | undefined) => {
  loading.value = true;
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      useUserStoreHook()
        .loginByUsername({
          login_name: ruleForm.username,
          password: ruleForm.password
        })
        .then(res => {
          if (res.success) {
            let firstLoginReminder = "";
            if (res.isFirstLoginAfterReset) {
              firstLoginReminder =
                " !<p class='leading-6'>" +
                transformI18n("login.firstLoginReminder") +
                "<p>";
            }
            commonControllerBasicData().then(res => {
              if (res) {
                useCommonStoreHook().SET_SETBASICDATA(res);
              }
            });
            // 获取后端路由
            initRouter().then(() => {
              if (useUserStoreHook()?.roles.indexOf("lead_taker_user") !== -1) {
                router.push("leadHistory");
              } else if (
                useUserStoreHook()?.roles.indexOf("install_user") !== -1 ||
                useUserStoreHook()?.roles.indexOf("sales_consultant_user") !==
                  -1
              ) {
                router.push("appointment");
              } else {
                router.push(getTopMenu(true).path);
              }
              message(
                transformI18n("login.loginSuccess") + firstLoginReminder,
                {
                  type: "success",
                  duration: firstLoginReminder ? 0 : 3000,
                  showClose: firstLoginReminder ? true : false,
                  dangerouslyUseHTMLString: true
                }
              );
            });
          } else {
            loading.value = false;
            message(res.message, { type: "error" });
          }
        });
    } else {
      loading.value = false;
      return fields;
    }
  });
};

/** 使用公共函数，避免`removeEventListener`失效 */
function onkeypress({ code }: KeyboardEvent) {
  if (code === "Enter") {
    onLogin(ruleFormRef.value);
  }
}

onMounted(() => {
  window.document.addEventListener("keypress", onkeypress);
});

onBeforeUnmount(() => {
  window.document.removeEventListener("keypress", onkeypress);
});

watch(checked, bool => {
  useUserStoreHook().SET_ISREMEMBERED(bool);
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" />
    <div class="login-container">
      <div>
        <avatar class="avatar" />
        <div class="login-box">
          <div class="login-form">
            <h2 class="login-title">{{ transformI18n("login.title") }}</h2>
            <h3 class="login-desc">{{ transformI18n("login.desc") }}</h3>

            <el-form
              ref="ruleFormRef"
              :model="ruleForm"
              :rules="loginRules"
              size="large"
            >
              <Motion :delay="100">
                <el-form-item
                  :rules="[
                    {
                      required: true,
                      message: transformI18n('login.usernameReg'),
                      trigger: 'blur'
                    }
                  ]"
                  prop="username"
                >
                  <el-input
                    clearable
                    v-model="ruleForm.username"
                    :placeholder="transformI18n('login.username')"
                    :prefix-icon="useRenderIcon(User)"
                  />
                </el-form-item>
              </Motion>

              <Motion :delay="150">
                <el-form-item prop="password">
                  <el-input
                    clearable
                    show-password
                    v-model="ruleForm.password"
                    :placeholder="transformI18n('login.password')"
                    :prefix-icon="useRenderIcon(Lock)"
                  />
                </el-form-item>
              </Motion>
              <Motion>
                <el-form-item class="remembber-me">
                  <div
                    class="w-full h-[20px] flex justify-between items-center"
                  >
                    <el-checkbox v-model="checked">
                      <span class="flex">
                        {{ transformI18n("login.remember") }}
                        <!-- <el-tooltip effect="dark"
                                    placement="top"
                                    :content="transformI18n('login.rememberInfo')">
                          <IconifyIconOffline :icon="Info"
                                              class="ml-1" />
                        </el-tooltip> -->
                      </span>
                    </el-checkbox>
                    <el-button
                      link
                      type="primary"
                      @click="router.push('/reset-password')"
                    >
                      {{ transformI18n("login.forget") }}
                    </el-button>
                  </div>
                </el-form-item>
              </Motion>
              <Motion :delay="250">
                <el-button
                  class="w-full mt-4"
                  size="default"
                  type="primary"
                  :loading="loading"
                  :disabled="!policyChecked"
                  @click="onLogin(ruleFormRef)"
                >
                  {{ transformI18n("menus.hslogin") }}
                </el-button>
              </Motion>
              <div class="">
                <el-checkbox v-model="policyChecked">
                  <span class="flex items-center !text-[#606266]">
                    I have read and agree
                    <el-button
                      link
                      type="primary"
                      @click="router.push('/privacy-policy')"
                    >
                      《Just Quality Privacy Policy》
                    </el-button>
                  </span>
                </el-checkbox>
              </div>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
