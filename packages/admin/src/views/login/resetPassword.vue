<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { resetPassordRules } from "./utils/rule";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { bg, avatar } from "./utils/static";
import { ref, reactive, onMounted, onBeforeUnmount } from "vue";
import { useDataThemeChange } from "@/layout/hooks/useDataThemeChange";
import { accountControllerResetPasswordByEmail } from "@/api/admin/admin-account";
import { transformI18n } from "@/plugins/i18n";

const router = useRouter();
const loading = ref(false);
const ruleFormRef = ref<FormInstance>();

const { initStorage } = useLayout();

initStorage();
const { dataThemeChange } = useDataThemeChange();
dataThemeChange();

const ruleForm = reactive({
  email: ""
});

const toResetPassword = async (formEl: FormInstance | undefined) => {
  loading.value = true;
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      accountControllerResetPasswordByEmail(ruleForm.email).then(res => {
        loading.value = false;
        if (res.success) {
          message(res.message, { type: "success" });
          router.push("/login");
        } else {
          message(res.message, { type: "error" });
        }
      });
    } else {
      loading.value = false;
      return fields;
    }
  });
};

/** 使用公共函数，避免`removeEventListener`失效 */
function onkeypress({ code }: KeyboardEvent) {
  if (code === "Enter") {
    toResetPassword(ruleFormRef.value);
  }
}

onMounted(() => {
  window.document.addEventListener("keypress", onkeypress);
});

onBeforeUnmount(() => {
  window.document.removeEventListener("keypress", onkeypress);
});
</script>

<template>
  <div class="select-none">
    <img :src="bg" class="wave" />
    <div class="login-container">
      <div>
        <avatar class="avatar" />
        <div class="login-box !h-[350px]">
          <div class="login-form">
            <h2 class="login-title">
              {{ transformI18n("login.resetPassword") }}
            </h2>
            <el-form
              ref="ruleFormRef"
              :model="ruleForm"
              :rules="resetPassordRules"
              size="large"
              label-position="top"
              class="mt-[40px]"
            >
              <Motion :delay="100">
                <el-form-item
                  :label="transformI18n('client.email')"
                  prop="email"
                >
                  <el-input
                    clearable
                    v-model="ruleForm.email"
                    class="border border-[#E9EBF0] rounded-[6px]"
                    :placeholder="transformI18n('client.email')"
                  />
                </el-form-item>
              </Motion>
              <Motion :delay="250" class="flex justify-end">
                <el-button
                  class="mt-4"
                  size="default"
                  type="primary"
                  :loading="loading"
                  @click="toResetPassword(ruleFormRef)"
                >
                  {{ transformI18n("common.reset") }}
                </el-button>
                <el-button
                  @click="router.push('/login')"
                  class="mt-4 !h-[44px]"
                  size="default"
                  type="info"
                >
                  {{ transformI18n("common.cancle") }}
                </el-button>
              </Motion>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import url("@/style/login.css");
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}
</style>
