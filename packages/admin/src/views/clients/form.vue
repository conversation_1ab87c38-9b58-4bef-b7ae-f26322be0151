<script setup lang="ts">
import { reactive, ref } from "vue";
import { rules } from "./clients_data";
import {
  clientControllerUpdate,
  clientControllerStore
} from "@/api/admin/clients";
import { sendFormFilter } from "@/utils/form";
import LocationIcon from "@/assets/svg/location.svg?component";
import { transformI18n } from "@/plugins/i18n";
import GoogelMap from "@/components/GoogleAmap/mapDialog.vue";
import { clientTitleOption } from "./../leads/components/leads_data";
import { handleAddress } from "@/utils/common";
import { useNav } from "@/layout/hooks/useNav";
const { userBaseInfo } = useNav();
const form = reactive({
  id: null,
  address: "",
  longitude: null,
  latitude: null,
  suburb: null,
  company_region: null
});
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const isEdit = ref(false);
const googelMapRef = ref(null);
function show(data = null) {
  isEdit.value = !!data;
  if (isEdit.value) {
    Object.assign(form, data);
  } else {
    Object.keys(form).forEach(key => {
      form[key] = null;
      if (key == "company_region") {
        form[key] = userBaseInfo.value.isPlatformUser
          ? null
          : userBaseInfo.value.accountCompanyRegion;
      }
    });
  }
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function hide() {
  dialogVisible.value = false;
}

// when click address input open amap
function addressFocus() {
  googelMapRef.value.show(form).then(res => {
    console.log(res, "ditu");
  });
}

function toUpdateAddress(addressInfo) {
  if (addressInfo) {
    form.address = addressInfo.address;
    form.longitude = addressInfo.longitude;
    form.latitude = addressInfo.latitude;
    form.suburb = handleAddress(addressInfo.address);
  }
}

async function onSave() {
  await formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      const sendForm = sendFormFilter(form);
      if (isEdit.value) {
        clientControllerUpdate(form.id, sendForm)
          .then(() => {
            loading.value = false;
            hide();
            promise.resolve();
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        clientControllerStore(sendForm)
          .then(() => {
            loading.value = false;
            hide();
            promise.resolve(sendForm);
          })
          .catch(() => {
            loading.value = false;
          });
      }
    }
  });
}

defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="isEdit ? transformI18n('client.edit') : transformI18n('client.new')"
    width="38.75rem"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      class="w-[99/100] ml-7 mr-7"
    >
      <el-row>
        <el-col
          v-if="userBaseInfo.isPlatformUser"
          class="!mb-0 mt-5 text-sm text-[#2A2E34]"
          :span="24"
        >
          Company
        </el-col>
        <el-col
          class="text-sm text-[#2A2E34]"
          v-if="userBaseInfo.isPlatformUser"
          :span="24"
        >
          <el-form-item prop="company_region">
            <el-select
              v-model="form.company_region"
              clearable
              :placeholder="'Company'"
            >
              <el-option
                v-for="item in userBaseInfo.companyRegion"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("client.client")
        }}</el-col>
        <el-col :span="12">
          <el-form-item
            :label="transformI18n('client.title')"
            prop="title"
            class="!mr-2"
          >
            <el-select
              v-model="form.title"
              :placeholder="transformI18n('common.search')"
            >
              <el-option
                v-for="item in clientTitleOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="transformI18n('client.givenName') + ':'"
            prop="given_name"
            class="!mr-2"
          >
            <el-input
              v-model="form.given_name"
              :placeholder="transformI18n('client.givenName')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="transformI18n('client.surname') + ':'"
            prop="surname"
            class="!mr-2"
          >
            <el-input
              v-model="form.surname"
              :placeholder="transformI18n('client.surname')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="transformI18n('client.phone') + ':'"
            prop="phone"
            class="!mr-2"
          >
            <el-input
              v-model="form.phone"
              :placeholder="transformI18n('client.phone')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("leads.backupContact")
        }}</el-col>
        <el-col :span="12">
          <el-form-item
            :label="transformI18n('client.secTitle')"
            prop="sec_title"
          >
            <el-select
              v-model="form.sec_title"
              :placeholder="transformI18n('common.search')"
            >
              <el-option
                v-for="item in clientTitleOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="transformI18n('client.givenName') + ':'"
            prop="sec_given_name"
            class="!mr-2"
          >
            <el-input
              v-model="form.sec_given_name"
              :placeholder="transformI18n('client.givenName')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="transformI18n('client.surname') + ':'"
            prop="sec_surname"
            class="!mr-2"
          >
            <el-input
              v-model="form.sec_surname"
              :placeholder="transformI18n('client.surname')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="transformI18n('client.phone') + ':'"
            prop="sec_phone"
            class="!mr-2"
          >
            <el-input
              v-model="form.sec_phone"
              :placeholder="transformI18n('client.phone')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" class="!mb-0 text-sm text-[#2A2E34]">{{
          transformI18n("client.address")
        }}</el-col>
        <el-col :span="24">
          <el-form-item :label="''" prop="address" @click="addressFocus">
            <el-input
              v-model="form.address"
              :placeholder="transformI18n('leads.addressOfTheClient')"
              autocomplete="off"
              :readonly="true"
            >
              <template #append>
                <LocationIcon class="mr-1" />
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            :label="transformI18n('client.suburb') + ':'"
            prop="suburb"
            class="!mr-2"
          >
            <el-input
              v-model="form.suburb"
              :placeholder="transformI18n('client.suburb')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24" class="!mb-0 text-sm text-[#2A2E34]">{{
          transformI18n("client.email")
        }}</el-col>
        <el-col :span="24">
          <el-form-item
            :label="transformI18n('client.email') + ':'"
            prop="email"
            class="!mr-2"
          >
            <el-input
              v-model="form.email"
              :placeholder="transformI18n('client.email')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <GoogelMap ref="googelMapRef" @toUpdateAddress="toUpdateAddress" />
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide">{{
          transformI18n("buttons.hsCancel")
        }}</el-button>
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :disabled="loading"
          :loading="loading"
          @click="onSave"
          >{{ transformI18n("buttons.hssave") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
