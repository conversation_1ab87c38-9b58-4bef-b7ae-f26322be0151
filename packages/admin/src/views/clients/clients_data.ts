import { requiredField } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
export const indexDefaultQuery = {
  search: "",
  sort: ["-id"],
  filter: [],
  _with: [],
  withCount: []
};

export const columns: TableColumnList = [
  {
    label: "ID",
    width: "50",
    prop: "id",
    fixed: true
  },
  {
    label: transformI18n("client.title"),
    prop: "title"
  },
  {
    label: transformI18n("client.givenName"),
    prop: "given_name"
  },
  {
    label: transformI18n("client.surname"),
    prop: "surname"
  },
  {
    label: transformI18n("client.phone"),
    prop: "phone"
  },
  {
    label: transformI18n("client.address"),
    prop: "address"
  },
  {
    label: transformI18n("client.suburb"),
    prop: "suburb"
  },
  {
    label: transformI18n("client.email"),
    prop: "email"
  },
  {
    label: transformI18n("client.secGivenName"),
    prop: "sec_given_name"
  },
  {
    label: transformI18n("client.secSurname"),
    prop: "sec_surname"
  },
  {
    label: "In Blacklist",
    width: "150",
    fixed: "right",
    slot: "inBlacklist"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];

export const platformColumns: TableColumnList = [
  {
    label: "ID",
    width: "50",
    prop: "id",
    fixed: true
  },
  {
    label: transformI18n("client.title"),
    prop: "title"
  },
  {
    label: transformI18n("client.givenName"),
    prop: "given_name"
  },
  {
    label: transformI18n("client.surname"),
    prop: "surname"
  },
  {
    label: transformI18n("client.phone"),
    prop: "phone"
  },
  {
    label: transformI18n("client.address"),
    prop: "address"
  },
  {
    label: transformI18n("client.suburb"),
    prop: "suburb"
  },
  {
    label: transformI18n("client.email"),
    prop: "email"
  },
  {
    label: transformI18n("client.secGivenName"),
    prop: "sec_given_name"
  },
  {
    label: transformI18n("client.secSurname"),
    prop: "sec_surname"
  },
  {
    label: "In Blacklist",
    width: "150",
    fixed: "right",
    slot: "inBlacklist"
  },
  {
    label: "Company",
    prop: "company_region"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];

export const REGEXP_EMAIL = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
export const REGEXP_PHONE = /^((04|02|03|07|08)\d{8}|(000|111)\d{7})$/;

export const rules = {
  title: [requiredField(transformI18n("client.title"))],
  given_name: [requiredField(transformI18n("client.givenName"))],
  surname: [requiredField(transformI18n("client.givenName"))],
  phone: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          return true;
        } else if (!REGEXP_PHONE.test(value)) {
          callback(new Error(transformI18n("client.phoneRuleReg")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  sec_phone: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          return true;
        } else if (!REGEXP_PHONE.test(value)) {
          callback(new Error(transformI18n("client.phoneRuleReg")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  address: [requiredField(transformI18n("client.address"))],
  email: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          return true;
        } else if (!REGEXP_EMAIL.test(value)) {
          callback(new Error(transformI18n("client.emailRuleReg")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  company_region: [requiredField("Company")]
};
