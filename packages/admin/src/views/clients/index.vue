<script setup lang="ts">
import {
  clientsController<PERSON>ndex,
  clientController<PERSON><PERSON>roy
} from "@/api/admin/clients";
import { columns, platformColumns } from "./clients_data";
import ClientForm from "./form.vue";
import { reactive, ref, onMounted, nextTick } from "vue";
import DataTable from "@/components/DataTable";
import { transformI18n } from "@/plugins/i18n";
import { Plus } from "@element-plus/icons-vue";
import RiEdit2Line from "@iconify-icons/ri/edit-2-line";
import { ElMessageBox, ElLoading } from "element-plus";
import searchLine from "@iconify-icons/ri/search-line";
import ResultDialog from "@/components/ResultDialog";
import CreateLeadForm from "./../leads/create_form.vue";
import RemoveFromBlacklistDialog from "@/components/RemoveFromBlacklistDialog";
import BlacklistHistoryDialog from "@/components/BlacklistHistoryDialog";
import { useNav } from "@/layout/hooks/useNav";
const { userBaseInfo } = useNav();
const companyRegion = ref([]);
defineOptions({
  name: "Clients"
});
const resultDialogRef = ref(null);
const form = reactive({});
const dataTableRef = ref(null);
const dataFormRef = ref(null);
const mv = ref();
const dialogPanel = ref();
const indexQuery = reactive({
  search: "",
  sort: "-id",
  filter: "",
  _with: "blacklistRecord.createdBy",
  withCount: ""
});
const lastCreateInfo = ref(null);
const CreateLeadRef = ref(null);
const removeFromBlacklistDialogRef = ref(null);
const blacklistHistoryDialog = ref(null);
function onCreate() {
  dataFormRef.value.show().then(result => {
    lastCreateInfo.value = result;
    loadData();
    showResultDialog();
  });
}

function onEdit(row) {
  dataFormRef.value.show(row).then(() => {
    loadData();
  });
}

function showResultDialog() {
  let data = {};
  data = {
    title: "Create Successful",
    content:
      "The account is created successfully. You can continue to perform operations",
    type: "success",
    buttons: [
      {
        text: "Create New Lead",
        onClick: createWithSameClient
      }
    ]
  };
  nextTick(() => {
    resultDialogRef.value.show(data).then();
  });
}

function createWithSameClient() {
  nextTick(() => {
    resultDialogRef.value.hide();
    onCreateLead(lastCreateInfo.value);
  });
}

function onCreateLead(data = null) {
  lastCreateInfo.value = null;
  CreateLeadRef.value.show(data).then();
}

function onDelete(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>delete</strong> the <strong style='color:var(--el-color-primary)'>${row.given_name}</strong> client?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    clientControllerDestroy(row.id)
      .then(() => {
        dialogPanel.value.close();
        loadData();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

function loadData() {
  dataTableRef.value.loadData();
}

function tableQueryParams(form) {
  const filterInfo = [];
  let search = "";
  if (form.search) {
    search = `%${form.search}%`;
  }
  if (companyRegion.value.length) {
    filterInfo.push("company_region:in:" + companyRegion.value.join("|"));
  }
  return Object.assign(
    { ...indexQuery },
    { filter: filterInfo.length === 0 ? indexQuery.filter : filterInfo.join() },
    { search: search }
  );
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function removeBlacklist(row) {
  const data = {
    client_id: row.id
  };
  removeFromBlacklistDialogRef.value.show(data).then(() => {
    loadData();
  });
}
function viewHistory(row) {
  console.log(row);
  blacklistHistoryDialog.value.show(row);
}
onMounted(() => {});
</script>

<template>
  <el-card ref="mv" shadow="never">
    <template #header>
      <div class="card-header">
        <span class="font-medium">
          {{ transformI18n("menus.hsClients") }}
        </span>
        <div class="page-action-box">
          <el-select
            v-if="userBaseInfo.isPlatformUser"
            class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
            v-model="companyRegion"
            @change="loadData"
            multiple
            :placeholder="'Company'"
          >
            <el-option
              v-for="item in userBaseInfo.companyRegion"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            v-auth="'addClient'"
            :icon="Plus"
            type="primary"
            @click="onCreate"
            >{{ transformI18n("client.newClient") }}</el-button
          >
        </div>
      </div>
    </template>
    <div class="mt-2 px-2 pb-2 bg-bg_color">
      <div class="flex justify-end w-full h-[50px] list-form">
        <div class="search-box">
          <el-input
            ref="inputRef"
            v-model="form.search"
            size="large"
            clearable
            :placeholder="transformI18n('leads.keywordPlaceHolder')"
            @input="loadData()"
            class="table-search-input"
          >
            <template #prefix>
              <el-tooltip
                class="item"
                effect="dark"
                content="You can search by Given Name,Surname,Phone,Email or Address"
                placement="top"
              >
                <IconifyIconOffline
                  class="mr-[5px]"
                  width="16px"
                  height="16px"
                  color="#656F7D"
                  :icon="searchLine"
                />
              </el-tooltip>
            </template>
          </el-input>
        </div>
      </div>
      <data-table
        ref="dataTableRef"
        :columns="userBaseInfo.isPlatformUser ? platformColumns : columns"
        :source="clientsControllerIndex"
        :form="form"
        :slotNames="['operation', 'user_status', 'inBlacklist']"
        :query-params="tableQueryParams"
        :header-cell-style="{
          background: 'var(--el-table-row-hover-bg-color)',
          color: 'var(--el-text-color-primary)'
        }"
      >
        <template #inBlacklist="{ row }">
          <span v-if="row.is_in_blacklist">
            Yes
            <i
              @click="viewHistory(row)"
              class="view-history-item text-[#9B3CE5] iconfont just-icon-eye ml-2"
          /></span>
          <span v-else>No</span>
        </template>
        <template #operation="{ row }">
          <div>
            <el-dropdown trigger="click">
              <span class="iconfont just-icon-gengduo" />
              <template #dropdown>
                <el-dropdown-menu>
                  <Auth value="editClient">
                    <el-dropdown-item
                      class="text-[#2A2E34] text-base"
                      @click.stop="onEdit(row)"
                    >
                      <IconifyIconOffline
                        class="mr-[5px]"
                        width="20px"
                        height="20px"
                        color="#9B3CE5"
                        :icon="RiEdit2Line"
                      />
                      {{ transformI18n("buttons.hseditor") }}
                    </el-dropdown-item>
                  </Auth>
                  <Auth value="delClient">
                    <el-dropdown-item
                      class="text-[#2A2E34] text-base"
                      @click.stop="onDelete(row)"
                    >
                      <i
                        class="!text-xl text-[#9B3CE5] iconfont just-icon-cancelled"
                      />
                      {{ transformI18n("buttons.hsdelete") }}
                    </el-dropdown-item>
                  </Auth>
                  <Auth value="removeFromBlacklist">
                    <el-dropdown-item
                      v-if="row.is_in_blacklist"
                      class="text-[#2A2E34] text-base"
                      @click.stop="removeBlacklist(row)"
                    >
                      <i
                        class="!text-xl text-[#9B3CE5] iconfont just-icon-delete"
                      />
                      Remove From Blacklist
                    </el-dropdown-item>
                  </Auth>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </data-table>
      <client-form ref="dataFormRef" />
      <ResultDialog ref="resultDialogRef" />
      <CreateLeadForm ref="CreateLeadRef" />
      <RemoveFromBlacklistDialog ref="removeFromBlacklistDialogRef" />
      <BlacklistHistoryDialog ref="blacklistHistoryDialog" />
    </div>
  </el-card>
</template>
<style lang="sass" scoped>
:deep(.el-form--inline .el-form-item)
  margin-right: 0

.view-history-item
  cursor: pointer
</style>
