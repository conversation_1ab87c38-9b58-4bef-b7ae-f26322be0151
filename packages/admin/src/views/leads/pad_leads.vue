<script setup lang="ts">
import { ref, onMounted } from "vue";
import { formatTime } from "@/utils/form";
import changePassword from "@/components/ChangePassword/ChangePassword.vue";
import Profile from "@/components/Profile/Profile.vue";
import FilterDialog from "@/components/Filter/Filter.vue";
import { ElLoading } from "element-plus";
import { useRouter } from "vue-router";
import { draftLeadsControllerIndex } from "@/api/admin/leads";
import { useNav } from "@/layout/hooks/useNav";
import MoreFilled from "@iconify-icons/ri/more-fill";
import { transformI18n } from "@/plugins/i18n";
import EmptySpan from "@/components/EmptySpan";
import Filter from "@iconify-icons/ri/filter-line";
import dayjs from "dayjs";

const router = useRouter();
const mv = ref();
const leadList = ref([]);
const dialogPanel = ref();
const page = ref(0);
const stopLoad = ref(false);
const ProfileDialogRef = ref(null);
const FilterDialogRef = ref(null);
const changePasswordDialog = ref();
const { logout, userAvatar, username } = useNav();
const filter = ref(null);
function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

async function getData() {
  dialogLoading();
  draftLeadsControllerIndex(
    null,
    filter.value,
    "-id",
    "lead.client,lead.source,lead.subsource,lead.category,source,subsource",
    null,
    page.value,
    20
  )
    .then(res => {
      dialogPanel.value.close();
      const { data } = res;
      stopLoad.value = true;
      if (leadList.value.length) {
        leadList.value = leadList.value.concat(data || []);
      } else {
        leadList.value = data || [];
      }
      if (data.length) {
        stopLoad.value = false;
      }
    })
    .catch(_err => {
      dialogPanel.value.close();
    });
}

function load() {
  if (stopLoad.value) {
    return;
  }
  page.value = page.value + 1;
  getData();
}

function changePwd() {
  changePasswordDialog.value.show();
}

function viewProfile() {
  ProfileDialogRef.value.show();
}

function showFilter() {
  FilterDialogRef.value.show().then(res => {
    const filterInfo = [];
    if (res.value.enquiryDateStart) {
      const enquiryDateStart = dayjs(res.value.enquiryDateStart).format(
        "YYYY-MM-DD 00:00:00"
      );
      filterInfo.push(`enquiry_date:gte:${enquiryDateStart}`);
    }
    if (res.value.enquiryDateEnd) {
      const enquiryDateEnd = dayjs(res.value.enquiryDateEnd).format(
        "YYYY-MM-DD 23:59:00"
      );
      filterInfo.push(`enquiry_date:lte:${enquiryDateEnd}`);
    }
    if (res.value.apptDateStart) {
      const apptDateStart = dayjs(res.value.apptDateStart).format(
        "YYYY-MM-DD 00:00:00"
      );
      filterInfo.push(`lead.appt_date:gte:${apptDateStart}`);
    }
    if (res.value.apptDateEnd) {
      const apptDateEnd = dayjs(res.value.apptDateEnd).format(
        "YYYY-MM-DD 23:59:00"
      );
      filterInfo.push(`lead.appt_date:lte:${apptDateEnd}`);
    }
    if (filterInfo.length) {
      filter.value = filterInfo.join();
    } else {
      filter.value = null;
    }
    stopLoad.value = false;
    page.value = 1;
    leadList.value = [];
    getData();
  });
}

function newLead() {
  router.push("lead");
}
onMounted(() => {
  getData();
});
</script>

<template>
  <el-card
    ref="mv"
    class="h-screen pl-5 pr-5 pb-5 pt-12 !bg-[#F7F8F9] pad-card"
    shadow="never"
  >
    <el-row class="mb-2 justify-between items-center foot-action">
      <IconifyIconOffline
        @click="showFilter"
        class="filter-icon"
        :icon="Filter"
      />
      <el-button
        type="primary"
        :loading="loading"
        @click="newLead"
        class="w-200 h-[40px]"
        >Create New Lead</el-button
      >
    </el-row>
    <div
      v-infinite-scroll="load"
      :infinite-scroll-immediate="false"
      class="infinite-list"
      style="overflow: auto"
    >
      <div
        class="infinite-list-item pr-2 pl-2 mb-5"
        v-for="(lead, index) in leadList"
        :key="index"
      >
        <p class="flex justify-between pb-1">
          <span class="mr-2"
            >{{ formatTime(lead.created_at, "DD/MM/YYYY HH:mm") }}
          </span>
          <span v-if="!lead.lead" class="status-item status-warning">
            {{ "Wait Confirm" }}
          </span>
          <span
            v-else-if="lead.lead.deleted_at"
            class="status-item status-delete"
          >
            {{ "Deleted" }}
          </span>
          <span class="result-item" v-else>
            <span
              class="status-item"
              :class="
                lead.lead.result != 'cancelled'
                  ? 'status-confirm'
                  : 'status-delete'
              "
            >
              {{ lead.lead.result }}
            </span>
            <span class="sub-result-item" v-if="lead.lead.sub_result">{{
              lead.lead.sub_result
            }}</span>
          </span>
        </p>
        <el-row>
          <el-col
            :span="12"
            :xs="24"
            :tag="'list-item'"
            class="flex justify-between pr-5"
            ><span class="title-item">Client: </span
            ><span v-if="lead.lead"
              >{{ lead.lead.client.given_name }}
              {{ lead.lead.client.surname }}</span
            >
            <span v-else>{{ lead.given_name }} {{ lead.surname }}</span>
          </el-col>
          <el-col
            :span="12"
            :xs="24"
            :tag="'list-item'"
            class="flex justify-between pr-5"
            ><span class="title-item">Product: </span>{{ lead.product }}
          </el-col>
          <el-col
            :span="12"
            :tag="'list-item'"
            class="flex justify-between pr-5"
            ><span class="title-item">Lead ID: </span>{{ lead.lead?.id }}
          </el-col>
          <el-col
            :span="12"
            :tag="'list-item'"
            class="flex justify-between pr-5"
            ><span class="title-item">Sold: </span>
            <EmptySpan :text="lead.lead?.sold ? '$ ' + lead.lead?.sold : ''" />
          </el-col>
          <el-col
            :span="12"
            :tag="'list-item'"
            class="flex justify-between pr-5"
          >
            <span class="title-item">Enquiry Date:</span>
            {{
              formatTime(
                lead.lead?.enquiry_date
                  ? lead.lead?.enquiry_date
                  : lead.enquiry_date,
                "DD/MM/YYYY"
              )
            }}
          </el-col>
          <el-col
            :span="12"
            :tag="'list-item'"
            class="flex justify-between pr-5"
          >
            <span class="title-item pr-5">Appointment Date:</span>
            {{ formatTime(lead.lead?.appt_date, "DD/MM/YYYY") }}
          </el-col>
          <el-col
            :span="12"
            :tag="'list-item'"
            class="flex justify-between pr-5"
          >
            <span class="title-item">Source:</span
            >{{
              lead.lead?.source?.name || lead.source?.name || lead.external_type
            }}
          </el-col>
          <el-col
            :span="12"
            :tag="'list-item'"
            class="flex justify-between pr-5"
          >
            <span class="title-item">Subsource:</span
            >{{ lead.lead?.subsource?.name || lead.subsource?.name }}
          </el-col>
        </el-row>
      </div>
    </div>
    <el-row class="user-info-item -ml-8 w-full">
      <span class="flex mr-2">
        <img :src="userAvatar" class="avatar-img" />
        <p v-if="username" class="dark:text-white">{{ username }}</p>
      </span>
      <el-dropdown trigger="click">
        <span class="arrow-down">
          <IconifyIconOffline :icon="MoreFilled" class="dark:text-white" />
        </span>
        <template #dropdown>
          <el-dropdown-menu class="logout">
            <el-dropdown-item @click="changePwd">
              {{ transformI18n("common.changePwd") }}
            </el-dropdown-item>
            <el-dropdown-item @click="viewProfile">
              {{ transformI18n("common.viewProfile") }}
            </el-dropdown-item>
            <el-dropdown-item @click="logout">
              {{ transformI18n("buttons.hsLoginOut") }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-row>
    <changePassword ref="changePasswordDialog" />
    <Profile ref="ProfileDialogRef" />
    <FilterDialog ref="FilterDialogRef" />
  </el-card>
</template>
<style scoped>
:deep(.el-image__inner) {
  width: 120px;
  max-height: 80px;
}
</style>
<style lang="scss" scoped>
@media screen and (width <= 420px) {
  list-item {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.infinite-list {
  height: calc(100vh - 1.3rem - 90px);
  padding: 0;
  padding-top: 10px;
  margin: 0;
  border-top: 1px solid #ccc;
}

.status-item {
  padding: 0 10px;
  color: #fff;
  text-transform: capitalize;
  border-radius: 10px;
}

.infinite-list-item {
  padding-bottom: 10px;
  border-bottom: 1px solid #ccc;
}

.title-item {
  font-weight: bold;
}

.status-warning {
  background: #e6a23c;
}

.status-confirm {
  background: #67c23a;
}

.status-delete {
  background: #909399;
}

.user-info-item {
  position: absolute;
  top: 0;
  left: 30px;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
  padding: 0 18px;
  background: #f7f8f9;
  border-top: 1px solid #e9ebf0;
}

.user-info-item .avatar-img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  border-radius: 12px;
}

//@media screen and (width <= 768px) {
:deep(.el-date-range-picker) {
  width: 100% !important;
}

.el-date-table td,
.el-date-table th {
  padding: 0; /* 压缩单元格间距 */
}

.filter-icon {
  font-size: 24px;
}

.result-item {
  text-align: center;
}

.sub-result-item {
  display: block;
  font-size: 12px;
  font-weight: bold;
  color: orange;
}
//}
</style>
