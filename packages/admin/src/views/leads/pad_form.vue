<script setup lang="ts">
import { reactive, ref, onMounted, nextTick } from "vue";
import {
  rules,
  clientTitleOption,
  roof,
  blinds,
  garageDoor,
  landscaping,
  patio
} from "./components/leads_data";
import { draftLeadsControllerStore } from "@/api/admin/leads";
import { sendFormFilter } from "@/utils/form";
import GoogelMap from "../../components/GoogleAmap/mapDialog.vue";
import { transformI18n } from "@/plugins/i18n";
import LocationIcon from "@/assets/svg/location.svg?component";
import { useCommonStoreHook } from "@/store/modules/common";
import Search from "@iconify-icons/ri/search-line";
import { clientsControllerIndex } from "@/api/admin/clients";
import CheckFillIcon from "@iconify-icons/ri/check-fill";
import ResultDialog from "@/components/ResultDialog";
import { Warning } from "@element-plus/icons-vue";
import { ElLoading } from "element-plus";
import { message } from "@/utils/message";
import { handleAddress } from "@/utils/common";
import { useRouter } from "vue-router";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const router = useRouter();

const resultDialogRef = ref(null);
const form = reactive({
  id: null,
  title: null,
  given_name: "",
  surname: "",
  phone: "",
  email: "",
  sec_title: null,
  sec_given_name: "",
  sec_surname: "",
  sec_phone: "",
  address: "",
  suburb: "",
  longitude: 0,
  latitude: 0,
  status: null,
  product_category_id: 1,
  sub_category_ids: "",
  sub_category_ids_array: [],
  comment: "",
  source_id: null,
  sub_source_id: null,
  enquiry_date: null,
  spoken_to: null,
  promotion_id: null,
  sale_manager_id: null,
  note: "",
  company_region: ""
});
const mv = ref();
const formRef = ref(null);
const loading = ref(false);
const googelMapRef = ref(null);
const productCategoryList = ref([]);
const subProductCategoryList = ref({});
const sourceList = ref([]);
const subSourceList = ref({});
const promotionList = ref([]);

const showSelectClientBox = ref(false);
const showPhoneSelectClientBox = ref(false);
const searchClientKey = ref("");
const clientList = ref(null);
const selectedCurrentClient = ref(null);
const dialogPanel = ref();
const clientField = [
  "title",
  "given_name",
  "surname",
  "phone",
  "email",
  "sec_title",
  "sec_given_name",
  "sec_surname",
  "sec_phone",
  "address",
  "longitude",
  "latitude",
  "enquiry_date",
  "company_region"
];
const clientLoading = ref(false);
// const totalClient = ref(2);
// const noMore = computed(
//   () => clientList.value && clientList.value.length >= totalClient.value
// );
// const disabled = computed(() => clientLoading.value || noMore.value);

function initFormData(useSameClient = false) {
  selectedCurrentClient.value = null;
  Object.keys(form).forEach(key => {
    if (!(useSameClient && clientField.includes(key))) {
      switch (key) {
        case "sub_category_ids_array":
          form[key] = [];
          break;
        case "product_category_id":
        case "source_id":
          form[key] = 1;
          break;
        case "longitude":
        case "latitude":
          form[key] = 0;
          break;
        default:
          form[key] = null;
          break;
      }
    }
  });
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

async function onSave() {
  await formRef.value.validate(valid => {
    if (valid) {
      if (!form.phone && !form.email) {
        showResultDialog();
        return;
      }
      if (form.suburb.match(/5\d{3}/)) {
        form.company_region = "SA";
      } else if (form.suburb.match(/2\d{3}/)) {
        form.company_region = "ACT";
      }

      if (!form.company_region) {
        form.company_region = userBaseInfo.value.accountCompanyRegion;
      }
      loading.value = true;
      dialogLoading();
      form.sub_category_ids = form.sub_category_ids_array.join(",");
      const sendForm = sendFormFilter(form);
      draftLeadsControllerStore(sendForm)
        .then(res => {
          loading.value = false;

          dialogPanel.value.close();
          message(res.message);
          if (res.success) {
            initFormData();
            backToHome();
          }
        })
        .catch(() => {
          loading.value = false;
          dialogPanel.value.close();
        });
    }
  });
}

function handleCategoryChange(id) {
  form.product_category_id = id;
  form.sub_category_ids_array = [];
}
// when click address input open amap
function addressFocus() {
  googelMapRef.value.show(form).then(_res => {});
}

//search client by keyword
function handleSearchClient() {
  let filter = "";
  let search = "";
  if (showPhoneSelectClientBox.value && form.phone) {
    filter = `phone:like:%${form.phone}%`;
  } else if (showSelectClientBox.value && searchClientKey.value) {
    search = `%${searchClientKey.value}%`;
  }
  clientList.value = [];
  clientLoading.value = true;
  const page = 1;
  const size = 50;
  clientsControllerIndex(search, filter, "-id", "", "", page, size).then(
    res => {
      const { data } = res;
      if (data) {
        clientList.value = data;
      }
      clientLoading.value = false;
    }
  );
}

function openSelectClientDialog() {
  showSelectClientBox.value = !showSelectClientBox.value;
  searchClientKey.value = "";
  if (showSelectClientBox.value) {
    showPhoneSelectClientBox.value = false;
    handleSearchClient();
  }
}

function closeSubDialog() {
  showSelectClientBox.value = false;
}

function openPhoneClientDialog() {
  showPhoneSelectClientBox.value = true;
  searchClientKey.value = form.phone;
  if (showPhoneSelectClientBox.value) {
    showSelectClientBox.value = false;
    handleSearchClient();
  }
}

function closePhoneClientDialog() {
  setTimeout(() => {
    showPhoneSelectClientBox.value = false;
  }, 200);
}

function selectClientHandle(clientInfo) {
  selectedCurrentClient.value = clientInfo;
  clientField.forEach(key => {
    form[key] = clientInfo[key];
  });
  closeSubDialog();
}

function toUpdateAddress(addressInfo) {
  if (addressInfo) {
    form.address = addressInfo.address;
    form.longitude = addressInfo.longitude;
    form.latitude = addressInfo.latitude;
    form.suburb = handleAddress(addressInfo.address);
  }
}
function initData() {
  initFormData();
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  subProductCategoryList.value = basicData["subProductCategoryList"];
  sourceList.value = basicData["sourceList"];
  subSourceList.value = basicData["subSourceList"];
  promotionList.value = basicData["promotionList"];
}

function disabledDate(time) {
  return time.getTime() > Date.now();
}

function showResultDialog() {
  let data = {};
  data = {
    title: "incomplete Client Info",
    content: "Please input either phone or the email before create a new lead",
    type: "warning",
    buttons: [
      {
        text: "Got it",
        onClick: hideResultDialog
      }
    ]
  };
  nextTick(() => {
    resultDialogRef.value.show(data).then(_res => {});
  });
}

function hideResultDialog() {
  resultDialogRef.value.hide();
}
function backToHome() {
  router.push("leadHistory");
}
onMounted(() => {
  initData();
});
</script>

<template>
  <el-card
    ref="mv"
    class="h-screen pl-5 pr-5 pb-5 pt-0 !bg-[#F7F8F9] pad-card"
    shadow="never"
    @click="closeSubDialog"
  >
    <div class="back-item">
      <span
        @click="backToHome"
        class="back-action iconfont just-icon-arrow-left mr-1"
      />Leads Histroy
    </div>
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      class="pl-8 pr-8 pb-8 main-content overflow-scroll bg-[#ffffff] rounded-xl"
    >
      <el-row
        class="text-[#2A2E34] leading-10 border-b border-[#E9EBF0] title-item -ml-8 pl-8"
      >
        {{ transformI18n("leads.createNewLeads") }}
      </el-row>
      <!-- Client Information -->
      <div class="header1">
        {{ transformI18n("leads.clientInformation") }}
        <el-button type="info" @click.stop="openSelectClientDialog()">
          <span class="iconfont just-icon-client mr-1" />
          {{ transformI18n("leads.existingClientBtn") }}
        </el-button>
        <div class="selectClientBox" v-if="showSelectClientBox" @click.stop="">
          <el-input
            ref="inputRef"
            size="large"
            clearable
            placeholder="Search"
            v-model="searchClientKey"
            class="search-input rounded"
            @input="handleSearchClient"
          >
            <template #prefix>
              <IconifyIconOffline :icon="Search" class="w-[16px] h-[16px]" />
            </template>
          </el-input>
          <div
            class="table-list"
            style="overflow: auto"
            v-loading="clientLoading"
          >
            <!-- v-infinite-scroll="handleSearchClient"
              :infinite-scroll-disabled="disabled" -->
            <ul class="list" v-if="clientList && clientList.length">
              <li
                v-for="(item, index) in clientList"
                :key="index"
                @click.stop="selectClientHandle(item)"
                class="item"
              >
                <div class="left">
                  <span class="mr-2 iconfont just-icon-client text-[20px]" />
                  <span class="name-text"
                    >{{ item.given_name || "" }} {{ item.surname || "" }}</span
                  >
                  <span class="name-text text-[12px]" v-if="item.phone"
                    >({{ item.phone || "" }})</span
                  >
                </div>
                <div class="right">
                  <IconifyIconOffline
                    :icon="CheckFillIcon"
                    class="text-[16px] checkIcon"
                    v-if="selectedCurrentClient === item"
                  />
                </div>
              </li>
            </ul>
            <ul v-else>
              <li class="empty nodata">No data</li>
            </ul>
          </div>
        </div>
      </div>
      <div class="header2">{{ transformI18n("client.client") }}</div>
      <el-row :gutter="10">
        <el-col :span="6" :xs="12" :tag="'small-model'">
          <el-form-item
            :label="transformI18n('client.title') + ':'"
            prop="title"
          >
            <el-select
              v-model="form.title"
              :placeholder="transformI18n('common.search')"
            >
              <el-option
                v-for="item in clientTitleOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="12" :tag="'small-model'">
          <el-form-item
            :label="transformI18n('client.givenName') + ':'"
            prop="given_name"
          >
            <el-input
              v-model="form.given_name"
              :placeholder="transformI18n('common.input')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="12" :tag="'small-model'">
          <el-form-item
            :label="transformI18n('client.surname') + ':'"
            prop="surname"
          >
            <el-input
              v-model="form.surname"
              :placeholder="transformI18n('common.input')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="12" :tag="'small-model'">
          <el-form-item
            :label="transformI18n('client.phone') + ':'"
            prop="phone"
          >
            <el-input
              v-model="form.phone"
              :placeholder="transformI18n('leads.telephoneOrmobilePhone')"
              autocomplete="off"
              clearable
              @input="handleSearchClient"
              @focus="openPhoneClientDialog"
              @blur="closePhoneClientDialog"
            />
            <div class="selectClientBox" v-if="showPhoneSelectClientBox">
              <div
                class="table-list"
                style="overflow: auto"
                v-loading="clientLoading"
              >
                <ul class="list" v-if="clientList && clientList.length">
                  <li
                    v-for="(item, index) in clientList"
                    :key="index"
                    @click.stop="selectClientHandle(item)"
                    class="item"
                  >
                    <div class="left">
                      <span
                        class="mr-2 iconfont just-icon-client text-[20px]"
                      />
                      <span class="name-text"
                        >{{ item.given_name || "" }}
                        {{ item.surname || "" }}</span
                      >
                      <span class="name-text text-[12px]" v-if="item.phone"
                        >({{ item.phone || "" }})</span
                      >
                    </div>
                    <div class="right">
                      <IconifyIconOffline
                        :icon="CheckFillIcon"
                        class="text-[16px] checkIcon"
                        v-if="selectedCurrentClient === item"
                      />
                    </div>
                  </li>
                </ul>
                <p v-else class="empty">No data</p>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="header2">{{ transformI18n("leads.backupContact") }}</div>
      <el-row :gutter="10">
        <el-col :span="6" :xs="12" :tag="'small-model'">
          <el-form-item
            :label="transformI18n('client.title') + ':'"
            prop="sec_title"
          >
            <el-select
              v-model="form.sec_title"
              :placeholder="transformI18n('common.search')"
            >
              <el-option
                v-for="item in clientTitleOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="12" :tag="'small-model'">
          <el-form-item
            :label="transformI18n('client.givenName') + ':'"
            prop="sec_given_name"
          >
            <el-input
              v-model="form.sec_given_name"
              :placeholder="transformI18n('common.input')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="12" :tag="'small-model'">
          <el-form-item
            :label="transformI18n('client.surname') + ':'"
            prop="sec_surname"
          >
            <el-input
              v-model="form.sec_surname"
              :placeholder="transformI18n('common.input')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6" :xs="12" :tag="'small-model'">
          <el-form-item
            :label="transformI18n('client.phone') + ':'"
            prop="sec_phone"
          >
            <el-input
              v-model="form.sec_phone"
              :placeholder="transformI18n('leads.telephoneOrmobilePhone')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="10">
        <el-col :span="8" :xs="12" :tag="'small-model'">
          <div class="header2">{{ transformI18n("client.address") }}</div>
          <el-form-item :label="''" prop="address" @click.stop="addressFocus">
            <el-input
              v-model="form.address"
              :placeholder="transformI18n('leads.addressOfTheClient')"
              autocomplete="off"
              :readonly="true"
            >
              <template #append>
                <LocationIcon class="mr-1" />
              </template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="12" :tag="'small-model'">
          <div class="header2">{{ transformI18n("client.suburb") }}</div>
          <el-form-item :label="''" prop="suburb" style="margin-left: 0">
            <el-input
              v-model="form.suburb"
              :placeholder="transformI18n('client.suburb')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="12" :tag="'small-model'">
          <div class="header2">{{ transformI18n("client.email") }}</div>
          <el-form-item :label="''" prop="email" style="margin-left: 0">
            <el-input
              v-model="form.email"
              :placeholder="transformI18n('leads.emailOfTheClient')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <!-- Product -->
      <div class="header1">
        {{ transformI18n("leads.productInterestedIn") }}
      </div>
      <div class="product-box">
        <div
          v-for="category in productCategoryList"
          :key="category.id"
          :class="[
            category.id === form.product_category_id ? 'active' : '',
            'img-box'
          ]"
          @click="handleCategoryChange(category.id)"
        >
          <el-image
            v-if="category.picUrl == 'landscaping.png'"
            :src="landscaping"
            fit="contain"
          />
          <el-image
            v-if="category.picUrl == 'blinds.png'"
            :src="blinds"
            fit="contain"
          />
          <el-image
            v-if="category.picUrl == 'roof.png'"
            :src="roof"
            fit="contain"
          />
          <el-image
            v-if="category.picUrl == 'garageDoor.png'"
            :src="garageDoor"
            fit="contain"
          />
          <el-image
            v-if="category.picUrl == 'patio.jpg'"
            :src="patio"
            fit="contain"
          />
          <span>{{ category.name }}</span>
        </div>
      </div>
      <div class="product-note">
        <Warning class="mr-1" style="width: 16px" />
        {{ transformI18n("leads.productSelectNote") }}
      </div>
      <div class="header2">{{ transformI18n("productSpec.product") }}</div>
      <div>
        <el-form-item
          :label="''"
          prop="sub_category_ids_array"
          style="border: 0"
        >
          <el-checkbox-group
            v-model="form.sub_category_ids_array"
            size="small"
            v-if="
              subProductCategoryList[form.product_category_id] &&
              subProductCategoryList[form.product_category_id].length
            "
          >
            <el-checkbox
              :label="sub_category.id"
              border
              class="mb-2"
              v-for="(sub_category, sub_index) in subProductCategoryList[
                form.product_category_id
              ]"
              :key="sub_index"
              >{{ sub_category.name }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
      </div>
      <el-row>
        <el-col :span="12" :xs="24" :tag="'small-model'">
          <div class="header2 mt-4">Product Detail</div>
          <el-form-item :label="''" prop="comment">
            <el-input
              v-model="form.comment"
              :placeholder="transformI18n('leads.commentsHold')"
              autocomplete="off"
              :rows="3"
              maxlength="50"
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 3.Leads Source -->
      <div class="header1">
        {{ transformI18n("leads.leadsSourceInformation") }}
      </div>

      <el-row :gutter="10">
        <el-col :span="8" :xs="12" :tag="'small-model'">
          <div class="header2">{{ transformI18n("leads.leadsSource") }}</div>
          <el-form-item
            :label="transformI18n('basicData.source') + ':'"
            prop="source_id"
          >
            <el-select
              v-model="form.source_id"
              :placeholder="transformI18n('common.select')"
            >
              <el-option
                v-for="item in sourceList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col
          :span="8"
          :xs="12"
          :tag="'small-model'"
          v-if="subSourceList[form.source_id]"
        >
          <div class="header2 hide-header">&nbsp;</div>
          <el-form-item
            :label="transformI18n('basicData.subSource') + ':'"
            prop="sub_source_id"
          >
            <el-select
              v-model="form.sub_source_id"
              :placeholder="transformI18n('common.select')"
            >
              <el-option
                v-for="item in subSourceList[form.source_id]"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" :xs="12" :tag="'small-model'">
          <div class="header2">{{ transformI18n("leads.dateOfEnquiry") }}</div>
          <el-form-item :label="''" prop="enquiry_date">
            <el-date-picker
              v-model="form.enquiry_date"
              type="date"
              :placeholder="transformI18n('common.select')"
              format="YYYY/MM/DD"
              value-format="YYYY/MM/DD"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="header2">{{ transformI18n("basicData.promotion") }}</div>
      <el-row>
        <el-col :span="12" :tag="'small-model'">
          <el-form-item
            :label="transformI18n('client.title') + ':'"
            prop="promotion_id"
          >
            <el-select
              v-model="form.promotion_id"
              :placeholder="transformI18n('common.select')"
            >
              <el-option
                v-for="item in promotionList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="header1">{{ transformI18n("leads.leadsNote") }}</div>
      <el-row>
        <el-col :span="12" :xs="24">
          <div class="header2">{{ transformI18n("common.comments") }}</div>
          <el-form-item :label="''" prop="note">
            <el-input
              v-model="form.note"
              :placeholder="transformI18n('leads.leadsNoteHold')"
              autocomplete="off"
              :rows="3"
              type="textarea"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <GoogelMap ref="googelMapRef" @toUpdateAddress="toUpdateAddress" />
    <ResultDialog ref="resultDialogRef" />
    <el-row class="m-5 justify-end foot-action">
      <el-button
        type="primary"
        :loading="loading"
        @click="onSave"
        class="w-200 h-[40px]"
        >{{
          form.id
            ? transformI18n("buttons.hssave")
            : transformI18n("common.create")
        }}</el-button
      >
    </el-row>
  </el-card>
</template>
<style scoped>
:deep(.el-image__inner) {
  width: 120px;
  max-height: 80px;
}
</style>
<style lang="scss" scoped>
@media screen and (width <= 520px) {
  small-model {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .hide-header {
    display: none;
  }
}

@media screen and (width <= 430px) {
  .pad-card {
    padding-right: 12px !important;
    padding-left: 12px !important;
  }

  .pad-card .main-content {
    padding-right: 12px !important;
    padding-left: 12px !important;
  }
}

@media screen and (width <= 430px) {
  .selectClientBox {
    width: 280px;
  }
}

.help-block {
  font-size: 12px;
  color: #999;
}

.form-item-with {
  width: 80%;
}

.header1 {
  position: relative;
  display: flex;
  align-self: center;
  justify-content: space-between;
  margin: 20px 0 15px;
  font-size: 18px;
  font-weight: 600;
  line-height: 27px;
  color: var(--font-color-level1);

  :deep(.el-button--info) {
    font-size: 13px;
  }
}

.header2 {
  font-size: 13px;
  font-weight: 500;
  line-height: 16px;
  color: var(--font-color-level1);
}

.product-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  overflow-x: auto; /* 允许横向滚动 */

  .img-box {
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    padding: 10px;
    margin-right: 20px;
    border: 1px solid #e9ebf0;
    border-radius: 4px;

    .el-image {
      margin-bottom: 10px;
    }
  }

  .img-box.active {
    background: rgb(153 55 229 / 10%);
    border: 1px solid var(--el-color-primary);
  }
}

.product-note {
  display: flex;
  align-items: center;
  margin: 10px 0;
  font-size: 12px;
  color: var(--font-color-level2);
}

// select client box
.nodata {
  margin-top: 10px;
  font-size: 14px;
  font-weight: normal;
}

.selectClientBox {
  position: absolute;
  top: 33px;
  right: 0;
  z-index: 2;
  display: flex;
  flex-direction: column;
  width: 320px;
  padding: 10px 14px;
  background: #fff;
  border: 1px solid var(--el-border-color-light);
  border-radius: 10px;
  box-shadow: var(--el-dropdown-menu-box-shadow);

  .search-input {
    height: 30px;
    border: 1px solid #e9ebf0;
  }
}

.table-list {
  width: 100%;
  // display: flex;
  //flex-direction: column;
  //align-items: center;
  //justify-content: flex-start;
  // height: 500px;
  min-height: 100px;
  max-height: 50vh;
  overflow: auto;
  text-align: center;

  .list {
    padding: 0;
    margin: 0;
    list-style: none;
  }

  .item {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 44px;
    font-weight: 400;

    .left {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      color: var(--font-color-level1);

      .name-text {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
      }
    }
  }
}

.checkIcon {
  color: var(--el-color-primary);
}

.main-content {
  min-height: 150px;
  max-height: calc(100vh - 110px);
}

.main-content .el-col {
  margin-bottom: 15px;
}

.main-content .el-form-item {
  margin-left: 0;
}

.el-col .el-select {
  width: 100%;
}

.title-item {
  margin-right: 0;
}

.foot-action {
  position: absolute;
  right: 0;
  bottom: 0;
}

.back-item {
  position: relative;
  // top: 0;
  // left: 30px;
  // z-index: 999;
  // display: flex;
  align-items: center;
  height: 44px;
  padding: 0 18px;
  font-size: 20px;
  line-height: 44px;
  text-align: center;
  background: #f7f8f9;
}

.back-action {
  position: absolute;
  left: 0;
  font-size: 20px;
  cursor: pointer;
}
</style>
