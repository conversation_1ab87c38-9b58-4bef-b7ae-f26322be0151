<script setup lang="ts">
import { ref, onMounted, nextTick, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { transformI18n } from "@/plugins/i18n";
import Leads from "./components/Leads.vue";
import LeadsDetail from "./leads-detail.vue";
import CreateForm from "./create_form.vue";
import ResultDialog from "@/components/ResultDialog";
import dayjs from "dayjs";
import { accountControllerIndex } from "@/api/admin/admin-account";
import { leadsControllerGetSummaryInfo } from "@/api/admin/leads";
import MapView from "./components/LeadsMapView.vue";
import { storageLocal } from "@pureadmin/utils";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const companyRegion = ref([]);
const mapCompanyRegion = ref([]);

const activeName = ref(null);
// List total value
const listCount = ref({
  all: 0,
  draft: 0,
  new: 0,
  followup: 0,
  appointed: 0,
  quoted: 0,
  sold: 0,
  cancelled: 0
});

const changeTab = (tab, event) => {
  nextTick(() => {
    if (activeName.value === "mapView" && mapViewRef.value) {
      mapViewRef.value.initViewMap();
    } else {
      tabsList.forEach(item => {
        if ("tab_" + item.name === tab.props.name) {
          loadDataFlag[item.name] = event._vts + "";
        }
      });
    }
    storageLocal().setItem(`leadsActiveName`, activeName.value);
  });
};
const MapMarksNum = ref(0);
const LeadsDetailRef = ref(null);
const CreateFormRef = ref(null);
const resultDialogRef = ref(null);
const lastCreateInfo = ref(null);
// const draftRef = ref(null);
const allStaffList = ref();

const tabsList = [
  {
    label: transformI18n("all"),
    name: "all",
    component: Leads,
    props: {
      result: "all",
      companyRegion: companyRegion.value
    }
  },
  {
    label: transformI18n("draft"),
    name: "draft",
    component: Leads,
    props: { result: "draft", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("new"),
    name: "new",
    component: Leads,
    props: { result: "new", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("followup"),
    name: "followup",
    component: Leads,
    props: { result: "followup", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("appointed"),
    name: "appointed",
    component: Leads,
    props: { result: "appointed", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("quoted"),
    name: "quoted",
    component: Leads,
    props: { result: "quoted", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("sold"),
    name: "sold",
    component: Leads,
    props: { result: "sold", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("cancelled"),
    name: "cancelled",
    component: Leads,
    props: { result: "cancelled", companyRegion: companyRegion.value }
  }
];

const loadDataFlag = reactive({
  draft: "",
  new: "",
  followup: "",
  appointed: "",
  quoted: "",
  sold: "",
  cancelled: ""
}); // Dynamic components refresh the data

const mapViewRef = ref(null);

// to Create draft leads
function onCreate(data = null) {
  lastCreateInfo.value = null;
  CreateFormRef.value.show(data).then(result => {
    lastCreateInfo.value = result;
    activeName.value = "tab_draft";
    loadDataFlag["draft"] =
      "" + (Math.floor(Math.random() * (1000 - 10 + 1)) + 10);
    showResultDialog();
  });
}
function toPassNewInfo(data: any, type = "draft") {
  listCount.value[type] = data.total;
}

// view details page
function toDetail(row, type = "draft") {
  row.enquiry_date = dayjs(row.enquiry_date).format("YYYY-MM-DD");
  LeadsDetailRef.value.show(row, type);
}
// open create success result dialog
function showResultDialog() {
  const data = {
    title: "Create Successfully",
    content: "Your new draft leads have been successfully created.",
    type: "success",
    buttons: [
      {
        text: "Create Another New",
        onClick: createOther
      },
      {
        text: "Another for The Same Client",
        onClick: createWithSameClient
      }
    ]
  };
  nextTick(() => {
    resultDialogRef.value.show(data).then(_res => {});
  });
}

function beforeResultClose(done) {
  done();
}
function createOther() {
  nextTick(() => {
    resultDialogRef.value.hide();
    onCreate();
  });
}

function createWithSameClient() {
  nextTick(() => {
    resultDialogRef.value.hide();
    onCreate(lastCreateInfo.value);
  });
}

function createWithSameClientFromDetail(data) {
  if (data) {
    onCreate(data);
  }
}

function reLoadTableData() {
  tabsList.forEach(item => {
    if ("tab_" + item.name === activeName.value) {
      loadDataFlag[item.name] =
        "" + (Math.floor(Math.random() * (1000 - 10 + 1)) + 10);
    }
    item.props.companyRegion = companyRegion.value;
  });
  getSummaryInfo();
}

function getAccount() {
  // staff info
  const search = "";
  let filter = "";
  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  const size = 666;
  const filterInfo = [];
  let companyRegionStr = null;
  if (companyRegion.value.length) {
    companyRegionStr = companyRegion.value.join(",");
  }

  filter = filterInfo.length === 0 ? "" : filterInfo.join();
  accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    companyRegionStr
  ).then(res => {
    const { data } = res;
    allStaffList.value = data || [];
  });
}

// get every type order list total number
function getSummaryInfo() {
  let companyRegionStr = "";
  if (companyRegion.value.length) {
    companyRegionStr = companyRegion.value.join(",");
  }
  leadsControllerGetSummaryInfo(companyRegionStr).then(res => {
    const { data } = res;
    if (data) {
      for (const key in data) {
        listCount.value[key] = data[key];
      }
    }
  });
}

function initData() {
  const lastTabActiveName = storageLocal().getItem(`leadsActiveName`);
  activeName.value = lastTabActiveName || "tab_draft";
  getAccount();
  getSummaryInfo();
}

function changeCompany() {
  getAccount();
  reLoadTableData();
  mapCompanyRegion.value = companyRegion.value;
}
function changeMapMarksNum(total) {
  MapMarksNum.value = total;
}

onMounted(() => {
  initData();
});
</script>

<template>
  <el-card shadow="never">
    <div class="font-medium page-header">
      {{ transformI18n("menus.hsLeads") }}
    </div>
    <div class="page-action-box">
      <el-select
        v-if="userBaseInfo.isPlatformUser"
        class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
        v-model="companyRegion"
        @change="changeCompany"
        multiple
        collapse-tags
        collapse-tags-tooltip
        :placeholder="'Company'"
      >
        <el-option
          v-for="item in userBaseInfo.companyRegion"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button
        :icon="Plus"
        type="primary"
        @click="onCreate"
        v-auth="'addLeads'"
        >{{ transformI18n("leads.newDraftLeads") }}</el-button
      >
    </div>
    <el-tabs
      v-model="activeName"
      :class="
        userBaseInfo.isPlatformUser ? 'demo-tabs platform-tabs' : 'demo-tabs'
      "
      @tab-click="changeTab"
    >
      <el-tab-pane
        :label="tabItem.label"
        :name="'tab_' + tabItem.name"
        v-for="(tabItem, index) in tabsList"
        :key="index"
      >
        <template #label>
          <span class="custom-tabs-label">
            <span>{{ transformI18n("leads." + tabItem.label) }}</span>
            <el-tag
              type="info"
              effect="light"
              round
              v-if="listCount[tabItem.name] > 0"
            >
              {{ listCount[tabItem.name] }}
            </el-tag>
          </span>
        </template>
        <component
          :is="tabItem.component"
          v-if="activeName == 'tab_' + tabItem.name"
          :ref="`${tabItem.name}Ref`"
          @toPassNewInfo="data => toPassNewInfo(data, tabItem.name)"
          @toDetail="row => toDetail(row, tabItem.name)"
          :args="tabItem.props"
          :loadDataFlag="loadDataFlag[tabItem.name]"
          :allStaffList="allStaffList"
          @updateListSummary="getSummaryInfo"
        />
      </el-tab-pane>

      <el-tab-pane label="Map View" name="mapView">
        <template #label>
          <span class="custom-tabs-label">
            <span>Map View</span>
            <el-tag type="info" effect="light" round v-if="MapMarksNum > 0">
              {{ MapMarksNum }}
            </el-tag>
          </span>
        </template>
        <mapView
          ref="mapViewRef"
          :dataType="'leads'"
          :companyRegion="mapCompanyRegion"
          @updateMapMarksNum="changeMapMarksNum"
        />
      </el-tab-pane>
    </el-tabs>
    <leads-detail
      ref="LeadsDetailRef"
      @closed="reLoadTableData"
      @createWithSameClient="createWithSameClientFromDetail"
      :allStaff="allStaffList"
    />
    <create-form ref="CreateFormRef" />
    <ResultDialog ref="resultDialogRef" :before-close="beforeResultClose" />
  </el-card>
</template>
<style lang="scss" scoped>
.platform-tabs {
  // height: calc(100% - 50px);
  margin-top: 50px;
}

::v-deep(.platform-tabs.el-tabs .el-tabs__header) {
  padding: 0 10px;
}
</style>
