<script setup lang="ts">
import { reactive, ref, onMounted, nextTick, computed, watch } from "vue";
import {
  rules,
  clientTitleOption,
  detailActionList,
  allResults,
  cancelChildren
} from "./components/leads_data";
import {
  draftLeadsControllerUpdate,
  leadsControllerUpdate,
  draftLeadsControllerConfirm,
  draftLeadsControllerClose,
  leadsControllerChangeSalesConsultant,
  leadsControllerShow
} from "@/api/admin/leads";
import {
  uploadControllerDestroy,
  uploadControllerShow
} from "@/api/admin/basic-data";
import { sendFormFilter } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
import ResultDialog from "@/components/ResultDialog";
import ConvertToOrderDialog from "@/components/ConvertToOrderDialog";
import GoogelMap from "../../components/GoogleAmap/mapDialog.vue";
import { useCommonStoreHook } from "@/store/modules/common";
import CirCleText from "@/components/CircleText";
import AddIcon from "@/assets/svg/add.svg?component";
import Edit2LineIcon from "@iconify-icons/ri/edit-2-line";
import { Check } from "@element-plus/icons-vue";
import { ElMessageBox, ElLoading, ElMessage } from "element-plus";
import LeadsNote from "./components/LeadsNote.vue";
import ChangeResult from "@/components/ChangeResult/ChangeResult.vue";
import { useUserStoreHook } from "@/store/modules/user";
import { accountControllerIndex } from "@/api/admin/admin-account";
import { removeElesFromArray } from "@/utils/common";
import { useNav } from "@/layout/hooks/useNav";
import { hasAuth } from "@/router/utils";
import EmptySpan from "@/components/EmptySpan";
import { UploadFile } from "@/components/Upload";
import { clientsControllerIndex } from "@/api/admin/clients";
import CheckFillIcon from "@iconify-icons/ri/check-fill";
import { appointmentTypeText } from "@/views/appointment/data";
import { handleAddress } from "@/utils/common";

const canCreateAppointment = ref(false);
const currentRoles = ref(useUserStoreHook()?.roles);
const { isAppointmentUser, userBaseInfo } = useNav();
const getDetailLoading = ref(false);
const canConfirm = computed(() => {
  return hasAuth("confirmLeads");
});
const companyRegionList = ref([
  {
    label: "SA",
    value: "SA"
  },
  {
    label: "ACT",
    value: "ACT"
  }
]);
const showSelectClientBox = ref(false);
const showPhoneSelectClientBox = ref(false);
const searchClientKey = ref("");
const clientList = ref(null);
const selectedCurrentClient = ref(null);
const clientLoading = ref(false);
const form = reactive({
  id: 0,
  client: null,
  title: null,
  given_name: "",
  surname: "",
  phone: "",
  email: "",
  sec_title: 0,
  sec_given_name: "",
  sec_surname: "",
  sec_phone: "",
  address: "",
  suburb: "",
  longitude: 0,
  latitude: 0,
  product: null,
  product_category_id: null,
  sub_category_ids: "",
  comment: "",
  source_id: null,
  sub_source_id: null,
  enquiry_date: "",
  spoken_to: null,
  promotion_id: null,
  sale_manager_id: 0,
  note: "",
  appt_setter_id: null,
  result: "",
  sub_result: "",
  retail: "",
  client_id: "",
  quoted: 0,
  sold: 0,
  order_id: 0,
  contract_id: 0,
  followup_time: "string",
  draft_leads_id: 0,
  sales: null,
  appt_setter: null,
  sub_category_ids_array: [],
  sales_consultant: null,
  contracts: null,
  order: null,
  campaign: null,
  sale_appointment: [],
  marketing_consent: 0,
  company_region: null
});
const company_region = ref(null);
const errorFlag = ref(false);
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const resultOptionHandled = ref([]);
const googelMapRef = ref(null);
const productCategoryList = ref([]);
const subProductCategoryList = ref({});
const sourceList = ref([]);
const subSourceList = ref({});
const promotionList = ref([]);
const resultDialogRef = ref(null);
const salesList = ref([]);
const spokenToUserList = ref([]);
const apptSetterList = ref([]);
const allStaffList = ref();
const updateLeadsFlag = ref(false);
const staffName = reactive({
  saleName: { login_name: null, color: null },
  apptSetterName: { login_name: null, color: null },
  spokenTo: { login_name: null, color: null }
});
const currentTagResult = ref(null); // draft new followup ...
const detailType = ref(""); // draft leads
const dialogPanel = ref();
const mv = ref();
const resultCascaderProps = {
  checkStrictly: true,
  emitPath: true
};
const allResultsHasAppointed = [
  ...allResults,
  {
    value: "appointed",
    label: "Appointed",
    icon: "appointed"
  }
];
// for change result
const changeResultValue = ref(null);
const currentTableAction = ref([]);
const clientFields = [
  // "id",
  "title",
  "given_name",
  "surname",
  "phone",
  "email",
  "sec_title",
  "sec_given_name",
  "sec_surname",
  "sec_phone",
  "address",
  "suburb",
  "longitude",
  "latitude"
];
const canEdit = ref(true);
const convertToOrderDialogRef = ref(null);
const emit = defineEmits<{
  (e: "createWithSameClient", val: Object): void;
}>();
const oldFormValues = reactive({
  retail: null,
  quoted: null,
  sold: null
});
const uploadParam = ref(null);
// const canAssignTo = ref(false);
const salesConsultantId = ref(null); // assign to sales Consultant Id
const salesConsultantOption = ref([]);
const props = defineProps({
  allStaff: {
    type: Array,
    default() {
      return [];
    }
  }
});

watch(
  () => props.allStaff,
  _newVal => {
    allStaffList.value = _newVal;
    updateStaff();
  },
  {
    deep: true,
    immediate: true
  }
);

function handleResultClose(isShow) {
  if (!isShow) {
    changeResultValue.value = currentTagResult.value;
  }
}

function updateStaff() {
  if (allStaffList.value) {
    salesList.value = [];
    const allStaft = JSON.parse(JSON.stringify(allStaffList.value));
    if (form.company_region) {
      apptSetterList.value = allStaft.filter(item => {
        return item.company_region == form.company_region || item.is_platform;
      });
      spokenToUserList.value = allStaft.filter(item => {
        return item.company_region == form.company_region || item.is_platform;
      });
    }

    salesConsultantOption.value = [];
    allStaft.map(item => {
      if (item.roles?.length && item.company_region == form.company_region) {
        item.roles.map(role => {
          if (role.name === "sale_manager") {
            salesList.value.push(item);
          }
          if (
            appointmentTypeText["sale"]["filterRoleName"].includes(role.name)
          ) {
            salesConsultantOption.value.push(item);
          }
        });
      }
    });
  }
}
// function convertToOrder() {
//   // to confirm if do convert
//   const data = {
//     lead_id: form.id
//   };
//   convertToOrderDialogRef.value.show(data).then(_res => {
//     if (_res) {
//       hide();
//     }
//   });
// }

function show(data = null, type = "draft", isCanEdit = true) {
  initFormData();
  canEdit.value = isCanEdit;
  detailType.value = type;
  errorFlag.value = false;
  company_region.value = null;
  if (data) {
    // if Data is number
    if (typeof data === "number") {
      defaultDataHandelByLeadsId(data);
    } else {
      defaultDataHandleByLeadsInfo(data, type);
    }
    updateStaff();
  } else {
    dialogVisible.value = false;
  }
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

// reFresh lead detail
function reFreshLead() {
  defaultDataHandelByLeadsId(form.id);
}

function defaultDataHandelByLeadsId(id) {
  dialogVisible.value = true;
  getDetailLoading.value = true;
  const _with = [
    "sales",
    "source",
    "category",
    "spokenTo",
    "client",
    "apptSetter",
    "saleAppointment",
    "salesConsultant",
    "contracts"
  ];
  dialogLoading();
  leadsControllerShow(id, _with)
    .then(res => {
      if (res.data) {
        defaultDataHandleByLeadsInfo(res.data, "leads");
      }
      getDetailLoading.value = false;
      dialogPanel.value.close();
    })
    .catch(() => {
      getDetailLoading.value = false;
      dialogPanel.value.close();
    });
  getAccount();
}

function defaultDataHandleByLeadsInfo(data, type) {
  Object.assign(form, data);
  if (type != "draft") {
    clientFields.map(item => {
      if (["longitude", "latitude", "address", "suburb"].indexOf(item) !== -1) {
        ["longitude", "latitude", "address", "suburb"].map(e => {
          form[e] = data[e] ? data[e] : data.client[e];
        });
      } else {
        form[item] = data.client[item];
      }
    });
  } else {
    form["campaign"] = data.campaign;
  }
  company_region.value = data.company_region;
  // salse attachment upload parasm
  uploadParam.value = {
    object_id: form.id,
    type: "contract"
  };
  canCreateAppointment.value =
    data.sale_appointment && data.sale_appointment.length ? false : true;
  if (["new", "followup", "quoted"].includes(data.result)) {
    canCreateAppointment.value =
      data.sale_appointment && data.sale_appointment.length ? false : true;
  } else {
    canCreateAppointment.value = false;
  }
  staffName.saleName = {
    login_name: form.sales?.login_name,
    color: form.sales?.color
  };
  staffName.apptSetterName = {
    login_name: form.appt_setter?.login_name,
    color: form.appt_setter?.color
  };
  staffName.spokenTo = {
    login_name: form.spoken_to?.login_name,
    color: form.spoken_to?.color
  };
  form.spoken_to = form.spoken_to?.id || "";
  form.sub_category_ids_array = form.sub_category_ids
    ? form.sub_category_ids.split(",")
    : [];
  form.sub_category_ids_array = form.sub_category_ids_array.map(Number);
  currentTagResult.value = form.result || type;
  changeResultValue.value = form.result;
  currentTableAction.value = getCurrentAction(currentTagResult.value);
  // if (form.order_id) {
  //   canEdit.value = false;
  // }
  resultOptionHandled.value = getResultOptions(form.result);
  Object.assign(oldFormValues, {
    retail: form.retail,
    quoted: form.quoted,
    sold: form.sold
  });
  dialogVisible.value = true;
  // if can assign to
  salesConsultantId.value = data.sales_consultant?.id;
}

// Get the current row  operation
function getCurrentAction(result) {
  // get result by result
  let actionList = [];
  if (result === "draft") {
    actionList = detailActionList["draft"];
  } else if (result && detailActionList[result]) {
    actionList = detailActionList[result];
  } else {
    return [];
  }
  if (!actionList.length) {
    return [];
  }
  if (currentRoles.value.includes("super_admin")) {
    return actionList;
  }
  // Filter actions by role
  let removeList = [];
  const canCancel = hasAuth("cancelLeads");
  const canConfirm = hasAuth("confirmLeads");
  const canAddLeads = hasAuth("addLeads");
  if (!canCancel) {
    removeList.push("cancel");
  }
  switch (result) {
    case "draft":
      if (!canConfirm) {
        removeList = [...removeList, ...["confirm"]];
      }
      if (!canAddLeads) {
        removeList = [...removeList, ...["createWithSameClient"]];
      }
      break;
  }
  if (removeList.length) {
    actionList = removeElesFromArray(actionList, removeList);
  }
  return actionList;
}

function getAccount() {
  // staff info
  const search = "";
  const filter = "";
  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  const size = 666;

  accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    form.company_region
  ).then(res => {
    const { data } = res;
    allStaffList.value = data || [];
    updateStaff();
  });
}

function initFormData() {
  Object.keys(form).forEach(key => {
    switch (key) {
      case "sub_category_ids_array":
      case "sale_appointments":
        form[key] = [];
        break;
      case "product_category_id":
      case "source_id":
        form[key] = 1;
        break;
      case "longitude":
      case "latitude":
        form[key] = 0;
        break;
      default:
        form[key] = null;
        break;
    }
  });

  Object.assign(oldFormValues, {
    retail: null,
    quoted: null,
    sold: null,
    email: null,
    sold_date: null
  });
  salesConsultantId.value = null;
}

// 获取当前result icon
function getCurrentResultIcon(result) {
  if (!result) {
    return;
  }
  if (typeof result === "string") {
    return allResultsHasAppointed.find(item => item.value === result)?.icon;
  } else if (result instanceof Array) {
    return allResultsHasAppointed.find(item => item.value === result[0])?.icon;
  }
}
const changeResult = ref(null);
// Change leads result
function handleResultChange(value, row) {
  const hasSonResult = ["followup", "quoted", "cancelled"];
  if (hasSonResult.includes(value[0]) && !value[1]) {
    changeResultValue.value = value[0];
    return;
  }
  const result = value[0];
  const subResult = value[1];

  const data = {
    id: row.id,
    type: "leads",
    result: result,
    sub_result: subResult,
    row
  };
  changeResult.value
    .show(data)
    .then(overAction => {
      if (overAction) {
        hide();
        promise.resolve();
      } else {
        changeResultValue.value = currentTagResult.value;
      }
    })
    .catch(() => {
      changeResultValue.value = currentTagResult.value;
    });
}

function toUpdate() {
  if (updateLeadsFlag.value && dialogVisible.value) {
    nextTick(() => {
      if (!company_region.value && hasAuth("changeLeadsCompany")) {
        console.log("======");
        errorFlag.value = true;
        return;
      }
      formRef.value.validate(valid => {
        if (valid) {
          loading.value = true;
          if (form.sub_category_ids_array.length) {
            form.sub_category_ids = form.sub_category_ids_array.join(",");
          } else {
            form.sub_category_ids = "";
          }
          //Synchronize client information
          if (form.client) {
            clientFields.map(item => {
              form.client[item] = form[item];
            });
          }
          if (company_region.value && hasAuth("changeLeadsCompany")) {
            form.company_region = company_region.value;
          }
          const sendForm = sendFormFilter(form);
          delete sendForm.draft_leads_id;
          dialogLoading();
          (detailType.value === "draft"
            ? draftLeadsControllerUpdate(form.id, sendForm)
            : leadsControllerUpdate(form.id, sendForm)
          )
            .then(res => {
              dialogPanel.value.close();
              loading.value = false;
              if (res.success) {
                ElMessage.success(res.message);
              } else {
                ElMessage.error(res.message);
              }
            })
            .catch(() => {
              dialogPanel.value.close();
              loading.value = false;
              ElMessage.error("Operation failed!");
            });
        }
      });
    });
  }
}

function hide() {
  dialogVisible.value = false;
}

function handleCategoryChange(id) {
  if (!canEdit.value) {
    return;
  }
  form.product_category_id = id;
  form.sub_category_ids_array = [];
}
// when click address input open amap
function addressFocus() {
  if (!canEdit.value) {
    return;
  }
  googelMapRef.value.show(form).then(_res => {});
}

function toUpdateAddress(addressInfo) {
  if (addressInfo) {
    form.address = addressInfo.name
      ? addressInfo.name + " " + addressInfo.address
      : addressInfo.address;
    form.longitude = addressInfo.longitude;
    form.latitude = addressInfo.latitude;
    form.suburb = handleAddress(addressInfo.address);
  }
}

function handChangeStaffName(id, nameType) {
  if (!canEdit.value) return;
  const item = allStaffList.value.find(item => item.id === id);
  if (item) {
    staffName[nameType] = { login_name: item.login_name, color: item.color };
  }
}

// change appointment assgin to
function appointmentAssignTo() {
  leadsControllerChangeSalesConsultant(form.id, {
    user_id: salesConsultantId.value
  }).then(res => {
    if (res?.success) {
      ElMessage.success(res.message);
      const theOne = salesConsultantOption.value.find(
        item => item.id === salesConsultantId.value
      );
      if (theOne) {
        form["sales_consultant"] = theOne;
        form["sales_consultant_id"] = theOne.id;
      } else {
        form["sales_consultant"] = null;
        form["sales_consultant_id"] = null;
      }
    } else {
      ElMessage.error(res.message);
    }
  });
}

function confirmLeads() {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>change</strong> the <strong style='color:var(--el-color-primary)'>${currentTagResult.value}</strong> leads to a new leads?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    if (!form.phone || !form.product_category_id || !form.source_id) {
      showResultDialog("confirmLeads");
    } else {
      dialogLoading();
      const sendForm = sendFormFilter(form);
      draftLeadsControllerConfirm(form.id, sendForm)
        .then(res => {
          if (res.success) {
            ElMessage.success(res.message);
            hide();
          } else {
            ElMessage.error(res.message);
          }
          dialogPanel.value.close();
          promise.resolve(true);
        })
        .catch(() => {
          ElMessage.error("Confirm processed fail");
          dialogPanel.value.close();
        });
    }
  });
}

// Dialog pop-up window, you can customize prompt content and operations
function showResultDialog(actionType = "confirmLeads") {
  let data = {};

  switch (actionType) {
    case "confirmLeads":
      data = {
        title: "incomplete Client Info",
        content:
          "Please complete the client information, source information and product information before change to new lead",
        type: "warning",
        buttons: [
          {
            text: "Got it",
            onClick: hideResultDialog
          }
        ]
      };
      break;
  }

  nextTick(() => {
    resultDialogRef.value.show(data).then(_res => {});
  });
}

function hideResultDialog() {
  resultDialogRef.value.hide();
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function handleAction(actionName, other = null) {
  if (actionName == "cancel") {
    if (detailType.value === "draft") {
      closeLeads(form);
    } else {
      leadsCanceled(other);
    }
  } else if (actionName == "createWithSameClient") {
    emit("createWithSameClient", form);
    hide();
  }
}

function leadsCanceled(subResult = null) {
  if (
    form.order &&
    (form.order.result == "completion" || form.order.result == "cancelled")
  ) {
    const result = form.order.result == "cancelled" ? "canceled" : "completed";
    ElMessage.error(
      "As the product order has been " +
        result +
        ", the associated leads cannot be canceled anymore."
    );
    return;
  }
  const data = {
    id: form.id,
    type: "leads",
    result: "cancelled",
    sub_result: subResult
  };
  changeResult.value
    .show(data)
    .then(overAction => {
      if (overAction) {
        hide();
        promise.resolve();
      }
    })
    .catch(() => {
      hide();
    });
}

function closeLeads(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>close</strong> the <strong style='color:var(--el-color-primary)'>${currentTagResult.value}</strong> leads?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    draftLeadsControllerClose(row.id)
      .then(() => {
        dialogPanel.value.close();
        hide();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

// Gets a resultList based on the current lead result and the login role
function getResultOptions(result) {
  let resultsListNoHandle = JSON.parse(JSON.stringify(allResults));
  if (result === "appointed") {
    resultsListNoHandle = [
      ...allResults,
      {
        value: "appointed",
        label: "Appointed",
        icon: "appointed",
        disabled: true
      }
    ];
  }
  //Change the result disabled property based on the role
  const resultsListHandle = [];
  let canToResultsList = [];
  if (hasAuth("changeLeadsResult")) {
    switch (result) {
      case "new":
        canToResultsList = ["followup", "quoted"];
        break;
      case "followup":
        canToResultsList = ["quoted", "followup"];
        break;
      case "appointed":
        canToResultsList = ["followup", "quoted", "sold"];
        break;
      case "quoted":
        canToResultsList = ["followup", "sold", "quoted"];
        break;
    }
  } else if (hasAuth("followupLeads")) {
    switch (result) {
      case "new":
        canToResultsList.push("followup");
        break;
      case "appointed":
        canToResultsList.push("followup");
        break;
      case "followup":
        canToResultsList.push("followup");
        break;
    }
  }
  resultsListNoHandle.map(resultItem => {
    if (
      canToResultsList.length &&
      canToResultsList.includes(resultItem.value)
    ) {
      resultItem.disabled = false;
    } else {
      resultItem.disabled = true;
    }
    if (resultItem.children && resultItem.children.length) {
      resultItem.children.forEach(child => {
        child.disabled = resultItem.disabled;
      });
    }
    resultsListHandle.push(resultItem);
  });
  return resultsListHandle;
}

function initData() {
  updateLeadsFlag.value = false;
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  subProductCategoryList.value = basicData["subProductCategoryList"];
  sourceList.value = basicData["sourceList"];
  subSourceList.value = basicData["subSourceList"];
  promotionList.value = basicData["promotionList"];
  updateLeadsFlag.value = true;
}

// update form.quoted,form.retail,form.sold
function updateAmount(fieldInfo = "this info", key = "") {
  if (form.result == "cancelled") {
    return;
  }
  if (form.result != "sold") {
    return;
  }
  if (key == "sold" && form.order_id && !hasAuth("updatePayment")) {
    return;
  }
  ElMessageBox.confirm(
    `Are you sure you want to change ${fieldInfo}?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  )
    .then(() => {
      oldFormValues[key] = form[key];
      toUpdate();
    })
    .catch(() => {
      form[key] = oldFormValues[key];
    });
}

function uploadFileChange() {
  reFreshLead();
}

// delete attachment
function delAttachment(file) {
  uploadControllerDestroy(file.id).then(res => {
    if (res.success) {
      ElMessage.success(res.message);
      reFreshLead();
    } else {
      ElMessage.error(res.message || "File deletion failure!");
    }
  });
}

function previewFile(file) {
  const fileUrl = file.url;
  if (file.storage == "s3") {
    dialogLoading();
    uploadControllerShow(file.id)
      .then(res => {
        if (res.data.s3Url) {
          window.open(res.data.s3Url, "_blank");
        } else {
          window.open(res.data.url, "_blank");
        }
        dialogPanel.value.close();
      })
      .catch(_err => {
        dialogPanel.value.close();
      });
  } else {
    window.open(fileUrl, "_blank");
  }
}

//search client by keyword
function handleSearchClient() {
  let filter = "";
  let search = "";
  if (showPhoneSelectClientBox.value && form.phone) {
    filter = `phone:like:%${form.phone}%`;
  } else if (showSelectClientBox.value && searchClientKey.value) {
    search = `%${searchClientKey.value}%`;
  }
  clientList.value = [];
  clientLoading.value = true;
  clientsControllerIndex(search, filter).then(res => {
    const { data } = res;
    if (data) {
      clientList.value = data;
    }
    clientLoading.value = false;
  });
}

function openPhoneClientDialog() {
  showPhoneSelectClientBox.value = true;
  searchClientKey.value = form.phone;
  if (showPhoneSelectClientBox.value) {
    showSelectClientBox.value = false;
    handleSearchClient();
  }
}

function selectClientHandle(clientInfo) {
  selectedCurrentClient.value = clientInfo;
  clientFields.forEach(key => {
    form[key] = clientInfo[key];
  });
  form.client_id = clientInfo.id;
  closePhoneClientDialog();
}

function closePhoneClientDialog() {
  setTimeout(() => {
    showPhoneSelectClientBox.value = false;
  }, 200);
}

onMounted(() => {
  initData();
});
defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="Leads Detail"
    :close-on-click-modal="false"
    width="85%"
    ref="mv"
    class="no-footer-dialog"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="detail-header">
        <div class="left">
          <div class="level-1">
            {{ transformI18n("menus.hsLeads") }}:<span class="ml-1">{{
              form.id
            }}</span>
          </div>
          <div class="sub-title mr-2">
            <span class="mr-1 iconfont just-icon-client" />
            {{ form.given_name }} {{ form.surname }}
          </div>
          <div class="sub-title mr-2">
            <span class="mr-1 iconfont just-icon-product" />
            {{ form.product ? form.product.name : "" }}
          </div>
        </div>
        <div class="flex justify-end">
          <div class="action-box" v-if="canEdit">
            <!-- <el-button
            type="info"
            @click="convertToOrder"
            class="mr-2"
            v-if="form.result == 'sold' && !form.order_id"
          >
            <span class="mr-1 iconfont just-icon-convert" />
            {{ transformI18n("leads.convertToOrder") }}</el-button
          > -->
            <el-button
              type="primary"
              :loading="loading"
              @click="toUpdate"
              class="mr-2"
              v-if="form.result !== 'cancelled' && !isAppointmentUser"
            >
              {{ transformI18n("buttons.hssave") }}</el-button
            >
            <el-button
              type="primary"
              @click="confirmLeads"
              :icon="Check"
              v-if="detailType === 'draft' && canConfirm"
              class="mr-2"
            >
              {{ transformI18n("buttons.hsConfirm") }}</el-button
            >
            <!-- change result button -->
            <div
              v-if="form.result === 'sold'"
              class="resultChangeChange mr-2"
              style="width: fit-content"
            >
              <span
                :class="
                  'mr-1 iconfont just-icon-' + getCurrentResultIcon(form.result)
                "
              />
              <span class="capitalize">{{ form.result }}</span>
            </div>
            <div
              v-else-if="
                detailType !== 'draft' &&
                form.result !== 'cancelled' &&
                resultOptionHandled.length &&
                !isAppointmentUser
              "
              class="resultChangeChange mr-2"
            >
              <span
                :class="
                  'mr-1 iconfont just-icon-' +
                  getCurrentResultIcon(changeResultValue)
                "
              />
              <el-cascader
                :options="resultOptionHandled"
                :show-all-levels="false"
                v-model="changeResultValue"
                @change="value => handleResultChange(value, form)"
                :props="resultCascaderProps"
                @visible-change="isShow => handleResultClose(isShow)"
              >
                <template #default="{ data }">
                  <span
                    :class="
                      'cascader-label' + (data.disabled ? ' disabled' : '')
                    "
                  >
                    <span
                      :class="'mr-1 iconfont just-icon-' + data.icon"
                      v-if="data.icon"
                    />
                    {{ data.label }}</span
                  >
                </template>
              </el-cascader>
            </div>
          </div>
          <div v-if="form.result != 'cancelled' && currentTableAction.length">
            <el-menu
              class="table-menu border-[0px] h-[20px]"
              mode="horizontal"
              :ellipsis="false"
              menu-trigger="hover"
            >
              <el-sub-menu
                index="1"
                class="!p-[0px] w-[50px]"
                popperClass="table-menu-popper"
              >
                <template #title
                  ><span class="iconfont just-icon-gengduo"
                /></template>
                <template
                  v-for="(actionItem, actionIndex) in currentTableAction"
                >
                  <el-sub-menu
                    :index="`1-${actionIndex}`"
                    :key="actionIndex + 'cancel'"
                    v-if="
                      actionItem == 'cancel' && currentTagResult !== 'draft'
                    "
                  >
                    <template #title>
                      <span class="font-bold">
                        {{ transformI18n("leads." + actionItem) }}
                      </span>
                    </template>
                    <el-menu-item
                      v-for="(cancelChildItem, childIndex) in cancelChildren"
                      :index="`1-${actionIndex}-${childIndex}`"
                      :key="childIndex"
                      @click="handleAction('cancel', cancelChildItem.value)"
                      >{{ cancelChildItem.label }}</el-menu-item
                    >
                  </el-sub-menu>
                  <el-menu-item
                    :index="`1-${actionIndex}`"
                    v-else
                    :key="actionIndex"
                    @click="handleAction(actionItem)"
                    >{{ transformI18n("leads." + actionItem) }}</el-menu-item
                  >
                </template>
              </el-sub-menu>
            </el-menu>
          </div>
        </div>
      </div>
      <div class="detail-content">
        <div class="detail-info">
          <div class="item-block">
            <!-- Company info -->
            <div v-if="hasAuth('changLeadsCompany')" class="header2">
              Company Info
            </div>
            <el-row :gutter="10" v-if="hasAuth('changeLeadsCompany')">
              <el-col :span="12">
                <el-form-item prop="company_region"
                  ><el-select v-model="company_region" :placeholder="'Company'">
                    <el-option
                      v-for="item in companyRegionList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row
              v-if="
                userBaseInfo.isPlatformUser && !form.company_region && errorFlag
              "
            >
              <p class="-mt-4 text-[#f56c6c] text-[12px]">
                Please select the company
              </p>
            </el-row>
            <!-- client info -->
            <div class="item-title level-1">
              {{ transformI18n("client.client") }}
            </div>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <div class="level-2 mb-[8px]">
                  {{ transformI18n("client.client") }}
                </div>
                <el-popover
                  placement="bottom"
                  :width="210"
                  trigger="click"
                  :disabled="!canEdit"
                >
                  <template #reference>
                    <div class="item-value level-3 edit-client">
                      <span
                        v-if="form.title || form.surname || form.given_name"
                      >
                        {{ form.title }} {{ form.surname }}
                        {{ form.given_name }}
                      </span>
                      <span v-else class="empty">
                        {{ transformI18n("common.empty") }}</span
                      >
                      <IconifyIconOffline
                        :icon="Edit2LineIcon"
                        v-if="canEdit"
                        class="editIcon"
                      />
                    </div>
                  </template>
                  <div>
                    <el-form-item
                      :label="'Title:'"
                      prop="title"
                      class="!mb-[0]"
                    >
                      <el-select
                        v-model="form.title"
                        :placeholder="transformI18n('common.empty')"
                      >
                        <el-option
                          v-for="item in clientTitleOption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :label="'Given name:'"
                      prop="given_name"
                      class="!mb-[0]"
                    >
                      <el-input
                        v-model="form.given_name"
                        :placeholder="transformI18n('common.empty')"
                        autocomplete="off"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="'Surname:'"
                      prop="surname"
                      class="!mb-[0]"
                    >
                      <el-input
                        v-model="form.surname"
                        :placeholder="transformI18n('common.empty')"
                        autocomplete="off"
                      />
                    </el-form-item>
                  </div>
                </el-popover>
              </el-col>
              <!-- <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.clientPhone')"
                  prop="phone"
                >
                  <el-input
                    v-model="form.phone"
                    :placeholder="transformI18n('common.input')"
                    autocomplete="off"
                    :readonly="!canEdit"
                  />
                </el-form-item>
              </el-col> -->
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.clientPhone')"
                  prop="phone"
                >
                  <el-input
                    v-model="form.phone"
                    :placeholder="transformI18n('leads.telephoneOrmobilePhone')"
                    autocomplete="off"
                    clearable
                    :readonly="!canEdit"
                    @input="handleSearchClient"
                    @focus="openPhoneClientDialog"
                    @blur="closePhoneClientDialog"
                  />
                  <div class="selectClientBox" v-if="showPhoneSelectClientBox">
                    <div
                      class="table-list"
                      style="overflow: auto"
                      v-loading="clientLoading"
                    >
                      <ul class="list" v-if="clientList && clientList.length">
                        <li
                          v-for="(item, index) in clientList"
                          :key="index"
                          @click.stop="selectClientHandle(item)"
                          class="item"
                        >
                          <div class="left">
                            <span
                              class="mr-2 iconfont just-icon-client text-[20px]"
                            />
                            <span class="name-text"
                              >{{ item.given_name || "" }}
                              {{ item.surname || "" }}</span
                            >
                            <span
                              class="name-text text-[12px]"
                              v-if="item.phone"
                              >({{ item.phone || "" }})</span
                            >
                          </div>
                          <div class="right">
                            <IconifyIconOffline
                              :icon="CheckFillIcon"
                              class="text-[16px] checkIcon"
                              v-if="selectedCurrentClient === item"
                            />
                          </div>
                        </li>
                      </ul>
                      <ul v-else>
                        <li class="empty nodata">No data</li>
                      </ul>
                    </div>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <div class="level-2 mb-[8px]">
                  {{ transformI18n("leads.backupContact") }}
                </div>
                <el-popover
                  placement="bottom"
                  :width="210"
                  :disabled="!canEdit"
                  trigger="click"
                >
                  <template #reference>
                    <div class="item-value level-3 edit-client">
                      <span
                        v-if="
                          form.sec_title ||
                          form.sec_surname ||
                          form.sec_given_name
                        "
                      >
                        {{ form.sec_title }} {{ form.sec_surname }}
                        {{ form.sec_given_name }}
                      </span>
                      <span v-else class="empty">{{
                        transformI18n("common.empty")
                      }}</span>
                      <IconifyIconOffline
                        :icon="Edit2LineIcon"
                        class="editIcon"
                        v-if="canEdit"
                      />
                    </div>
                  </template>
                  <div>
                    <el-form-item
                      :label="transformI18n('client.title') + ':'"
                      prop="sec_title"
                      class="!mb-[0]"
                    >
                      <el-select
                        v-model="form.sec_title"
                        :placeholder="transformI18n('common.empty')"
                      >
                        <el-option
                          v-for="item in clientTitleOption"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :label="'Given name:'"
                      prop="sec_given_name"
                      class="!mb-[0]"
                    >
                      <el-input
                        v-model="form.sec_given_name"
                        :placeholder="transformI18n('common.empty')"
                        autocomplete="off"
                        :readonly="!canEdit"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="'Surname:'"
                      prop="sec_surname"
                      class="!mb-[0]"
                    >
                      <el-input
                        v-model="form.sec_surname"
                        :placeholder="transformI18n('common.empty')"
                        autocomplete="off"
                        :readonly="!canEdit"
                      />
                    </el-form-item>
                  </div>
                </el-popover>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.backupContactPhone')"
                  prop="sec_phone"
                >
                  <el-input
                    v-model="form.sec_phone"
                    :placeholder="transformI18n('common.input')"
                    autocomplete="off"
                    :readonly="!canEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" class="item-box">
                <el-form-item
                  :label="transformI18n('client.address')"
                  prop="address"
                  @click.stop="addressFocus"
                >
                  <el-input
                    v-model="form.address"
                    :placeholder="transformI18n('common.empty')"
                    autocomplete="off"
                    :readonly="true"
                  >
                    <!-- <template #append>
                      <LocationIcon class="mr-1" />
                    </template> -->
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('client.suburb')"
                  prop="suburb"
                >
                  <el-input
                    v-model="form.suburb"
                    :placeholder="transformI18n('common.input')"
                    autocomplete="off"
                    clearable
                    :readonly="form.result == 'cancelled'"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('client.email')"
                  prop="email"
                >
                  <el-input
                    v-model="form.email"
                    :placeholder="transformI18n('common.input')"
                    autocomplete="off"
                    clearable
                    @blur="updateAmount(transformI18n('client.email'), 'email')"
                    :readonly="form.result == 'cancelled'"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- management -->
          <div class="item-block" v-if="detailType != 'draft'">
            <div class="item-title level-1">
              {{ transformI18n("leads.management") }}
            </div>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.salesManager')"
                  prop="sale_manager_id"
                >
                  <div class="flex items-center" v-if="form.sales">
                    <CirCleText
                      :text="form.sales.login_name"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="form.sales.color"
                    />
                    {{ form.sales.login_name }}
                  </div>
                  <div class="empty" v-else>
                    {{ transformI18n("common.empty") }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item label="Appt Setter" prop="appt_setter_id">
                  <div class="flex items-center">
                    <CirCleText
                      :text="staffName.apptSetterName.login_name"
                      v-if="staffName.apptSetterName"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="staffName.apptSetterName.color"
                    />
                    <EmptySpan
                      v-if="!(canEdit && hasAuth('editManagement'))"
                      :text="
                        staffName.apptSetterName
                          ? staffName.apptSetterName.login_name
                          : ''
                      "
                    />
                    <el-select
                      v-model="form.appt_setter_id"
                      v-else
                      :placeholder="transformI18n('common.empty')"
                      filterable
                      @change="
                        val => handChangeStaffName(val, 'apptSetterName')
                      "
                    >
                      <el-option
                        v-for="item in apptSetterList"
                        :key="item.id"
                        :label="item.login_name"
                        :value="item.id"
                      >
                        <div class="flex items-center">
                          <CirCleText
                            :text="item.login_name"
                            :size="20"
                            :fontSize="'10px'"
                            :customBgColor="item.color"
                          />
                          <span>{{ item.login_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item label="Sales Consultant" prop="salesConsultantId">
                  <div
                    class="flex items-center"
                    v-if="form.result == 'cancelled'"
                  >
                    <CirCleText
                      :text="form.sales_consultant.login_name"
                      v-if="form.sales_consultant"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="form.sales_consultant.color"
                    />
                    <EmptySpan
                      :text="
                        form.sales_consultant
                          ? form.sales_consultant.login_name
                          : ''
                      "
                    />
                  </div>

                  <div class="flex items-center" v-else>
                    <CirCleText
                      :text="form.sales_consultant.login_name"
                      v-if="form.sales_consultant"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="form.sales_consultant.color"
                    />
                    <el-select
                      v-model="salesConsultantId"
                      :placeholder="transformI18n('common.empty')"
                      filterable
                      clearable
                      class="w-[240px] mr-[10px]"
                      @change="appointmentAssignTo()"
                    >
                      <el-option
                        v-for="item in salesConsultantOption"
                        :key="item.id"
                        :label="item.login_name"
                        :value="item.id"
                      >
                        <div class="flex items-center">
                          <CirCleText
                            :text="item.login_name"
                            :size="20"
                            :fontSize="'10px'"
                            :customBgColor="item.color"
                          />
                          <span>{{ item.login_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('common.result')"
                  prop="result"
                >
                  <div class="flex items-center">
                    <span
                      :class="
                        'mr-1  input-result-icon iconfont just-icon-' +
                        form.result
                      "
                    />
                    <span class="capitalize">{{ form.result }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <div class="level-2 mb-[8px]">
                  {{ transformI18n("common.subresult") }}
                </div>
                <div class="item-value level-3">
                  <span v-if="form.sub_result" class="capitalize"
                    >{{ form.sub_result }}
                  </span>
                  <span v-else class="empty">{{
                    transformI18n("common.empty")
                  }}</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.retail')"
                  prop="retail"
                >
                  <el-input
                    v-model="form.retail"
                    :placeholder="transformI18n('common.input')"
                    autocomplete="off"
                    @blur="
                      updateAmount(
                        transformI18n('common.retailAmount'),
                        'retail'
                      )
                    "
                    :readonly="
                      !hasAuth('editManagement') || form.result == 'cancelled'
                    "
                  >
                    <template #prefix v-if="form.retail"
                      ><span class="mr-2">$</span>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.quoted')"
                  prop="quoted"
                >
                  <!-- <div class="item-value level-3">
                    <span v-if="form.quoted">{{ form.quoted }} </span>
                    <span v-else class="empty">{{
                      transformI18n("common.notYet")
                    }}</span>
                  </div> -->
                  <el-input
                    v-model="form.quoted"
                    :placeholder="transformI18n('common.input')"
                    autocomplete="off"
                    @blur="
                      updateAmount(
                        transformI18n('common.quotedAmount'),
                        'quoted'
                      )
                    "
                    :readonly="
                      !hasAuth('editManagement') || form.result == 'cancelled'
                    "
                  >
                    <template #prefix v-if="form.quoted"
                      ><span class="mr-2">$</span>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item :label="transformI18n('leads.sold')" prop="sold">
                  <!-- <div class="item-value level-3">
                    <span v-if="form.sold">{{ form.sold }} </span>
                    <span v-else class="empty">{{
                      transformI18n("common.notYet")
                    }}</span>
                  </div> -->
                  <el-input
                    v-model="form.sold"
                    :placeholder="transformI18n('common.input')"
                    autocomplete="off"
                    @blur="
                      updateAmount(transformI18n('common.soldAmount'), 'sold')
                    "
                    :readonly="
                      !hasAuth('editManagement') ||
                      (!hasAuth('updatePayment') && form.order_id) ||
                      form.result == 'cancelled'
                    "
                  >
                    <template #prefix v-if="form.sold"
                      ><span class="mr-2">$</span>
                    </template></el-input
                  >
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <div class="level-2 mb-[8px]">
                  {{ transformI18n("leads.relatedOrder") }}
                </div>
                <div class="item-value level-3">
                  <span v-if="form.order_id">{{ form.order_id }} </span>
                  <span v-else class="empty">{{
                    transformI18n("common.empty")
                  }}</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.soldDate')"
                  prop="sold_date"
                >
                  <el-date-picker
                    v-model="form.sold_date"
                    type="date"
                    :placeholder="transformI18n('common.select')"
                    format="DD/MM/YYYY"
                    value-format="YYYY/MM/DD"
                    @blur="
                      updateAmount(transformI18n('leads.soldDate'), 'sold_date')
                    "
                    :disabled="
                      !hasAuth('editManagement') || form.result == 'cancelled'
                    "
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
          <!-- Product Requirements -->
          <div class="item-block">
            <div class="item-title level-1">
              {{ transformI18n("leads.productRequirements") }}
            </div>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('productSpec.category')"
                  prop="product_category_id"
                >
                  <el-select
                    v-model="form.product_category_id"
                    :placeholder="transformI18n('common.select')"
                    @change="handleCategoryChange"
                    :disabled="
                      form.result == 'cancelled' || form.result == 'sold'
                    "
                  >
                    <el-option
                      v-for="item in productCategoryList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="item-box product-select">
                <el-form-item
                  :label="transformI18n('productSpec.product')"
                  prop="sub_category_ids_array"
                >
                  <el-select
                    v-model="form.sub_category_ids_array"
                    :placeholder="transformI18n('common.empty')"
                    multiple
                    collapse-tags
                    collapse-tags-tooltip
                    :disabled="!canEdit"
                    :suffix-icon="AddIcon"
                    :multiple-limit="form.order_id ? 1 : 0"
                    filterable
                    class="min-w-[230px]"
                  >
                    <el-option
                      v-for="item in subProductCategoryList[
                        form.product_category_id
                      ]"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" class="item-box">
                <el-form-item :label="'Product Detail'" prop="comment">
                  <el-input
                    v-model="form.comment"
                    :readonly="!canEdit"
                    :placeholder="transformI18n('leads.commentsHoldDetail')"
                    autocomplete="off"
                    :rows="3"
                    maxlength="50"
                    type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- source -->
          <div class="item-block">
            <div class="item-title level-1">
              {{ transformI18n("basicData.source") }}
            </div>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('basicData.source')"
                  prop="source_id"
                >
                  <el-select
                    v-model="form.source_id"
                    :placeholder="transformI18n('common.select')"
                    :disabled="
                      detailType != 'draft' && !hasAuth('changeSource')
                    "
                  >
                    <el-option
                      v-for="item in sourceList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('basicData.subSource')"
                  prop="sub_source_id"
                >
                  <el-select
                    v-model="form.sub_source_id"
                    :disabled="
                      !canEdit &&
                      detailType != 'draft' &&
                      !hasAuth('changeSource')
                    "
                    :placeholder="transformI18n('common.select')"
                  >
                    <el-option
                      v-for="item in subSourceList[form.source_id]"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.dateOfEnquiry')"
                  prop="enquiry_date"
                >
                  <el-date-picker
                    v-model="form.enquiry_date"
                    type="date"
                    :placeholder="transformI18n('common.select')"
                    format="DD/MM/YYYY"
                    value-format="YYYY/MM/DD"
                    :disabled="!canEdit"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="12" :md="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.spokenTo')"
                  prop="spoken_to"
                >
                  <div v-if="!hasAuth('editManagement')" class="noEdit">
                    <div class="flex items-center" v-if="staffName.spokenTo">
                      <CirCleText
                        :text="staffName.spokenTo.login_name"
                        :size="20"
                        :fontSize="'10px'"
                        :customBgColor="staffName.spokenTo.color"
                      />
                      {{ staffName.spokenTo.login_name }}
                    </div>
                    <div v-else class="empty">
                      {{ transformI18n("common.empty") }}
                    </div>
                  </div>

                  <div class="flex items-center" v-else>
                    <CirCleText
                      :text="staffName.spokenTo.login_name"
                      v-if="staffName.spokenTo"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="staffName.spokenTo.color"
                    />
                    <el-select
                      v-model="form.spoken_to"
                      :placeholder="transformI18n('common.empty')"
                      filterable
                      :disabled="!canEdit"
                      @change="val => handChangeStaffName(val, 'spokenTo')"
                    >
                      <el-option
                        v-for="item in spokenToUserList"
                        :key="item.id"
                        :label="item.login_name"
                        :value="item.id"
                      >
                        <div class="flex items-center">
                          <CirCleText
                            :text="item.login_name"
                            :size="20"
                            :fontSize="'10px'"
                            :customBgColor="item.color"
                          />
                          <span>{{ item.login_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="12" class="item-box">
                <el-form-item
                  :label="transformI18n('basicData.promotion')"
                  prop="promotion_id"
                >
                  <el-select
                    v-model="form.promotion_id"
                    :placeholder="transformI18n('common.select')"
                    :disabled="!canEdit"
                  >
                    <el-option
                      v-for="item in promotionList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="detailType == 'draft'" :span="12" class="item-box">
                <el-form-item
                  :label="transformI18n('basicData.campaign')"
                  prop="campaign"
                >
                  <el-input
                    v-model="form.campaign"
                    :placeholder="transformI18n('leads.campaignHoldDetail')"
                    autocomplete="off"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" class="item-box">
                <el-form-item
                  label="Marketing Consent"
                  prop="marketing_consent"
                >
                  <el-radio-group v-model="form.marketing_consent">
                    <el-radio is-checked :label="1">Yes</el-radio>
                    <el-radio is-checked :label="0">No</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- Leads AttachMents -->
          <div
            v-if="detailType != 'draft'"
            class="item-title level-1 w-full mt-5 flex justify-between items-center"
          >
            <span>Attachments</span>
            <UploadFile
              ref="uploadFileRef"
              :otherParam="uploadParam"
              :btnTxt="'Upload'"
              :btnIcon="'just-icon-upload-line'"
              @fileChange="uploadFileChange"
              :btnClass="'uploadBtn'"
              v-if="hasAuth('uploadFileForLeads')"
            />
          </div>
          <div>
            <div
              class="empty mt-[20px]"
              v-if="!(form.contracts && form.contracts.length)"
            >
              Not yet
            </div>
            <el-row
              class="flex justify-between items-center border text-base pl-2 pr-2 pb-1.5 pt-1.5 mt-5 border-[#E9EBF0] rounded-lg"
              v-for="(contract, index) in form.contracts"
              :key="index"
            >
              <span
                ><i class="text-[#9B3CE5] iconfont just-icon-upload" />{{
                  contract.name
                }}</span
              >
              <div>
                <i
                  @click="previewFile(contract)"
                  class="cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
                />
                <span
                  v-if="hasAuth('delLeadsUploadFile')"
                  class="iconfont just-icon-delete ml-2 primary-color"
                  @click="delAttachment(contract)"
                />
              </div>
            </el-row>
          </div>
        </div>
        <div class="notes-box">
          <LeadsNote
            ref="leadsNoteRef"
            v-if="dialogVisible"
            :id="form.id"
            :type="detailType === 'draft' ? 'draft' : 'leads'"
            :result="form.result"
            :row="form"
            :canEdit="canEdit"
            @updateDetail="reFreshLead"
            :canCreateAppointment="canCreateAppointment"
          />
        </div>
      </div>
    </el-form>
    <GoogelMap ref="googelMapRef" @toUpdateAddress="toUpdateAddress" />
    <ResultDialog ref="resultDialogRef" />
    <ChangeResult ref="changeResult" />
    <ConvertToOrderDialog ref="convertToOrderDialogRef" />
  </el-dialog>
</template>

<style lang="scss" scoped>
.help-block {
  font-size: 12px;
  color: #999;
}

.form-item-with {
  width: 80%;
}

.detail-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 15px;
  border-bottom: 1px solid #e9ebf0;

  .left {
    display: flex;
    align-items: center;
  }

  .sub-title {
    margin-left: 10px;
  }
}

.detail-content {
  display: flex;
  justify-content: space-between;

  .detail-info {
    flex-grow: 1;
    min-height: 380px;
    max-height: calc(100vh - 300px);
    padding: 0 30px 30px;
    overflow: auto;
  }

  .notes-box {
    flex-shrink: 0;
    width: 450px;
    background: #fff;
    border: 1px solid #e9ebf0;
  }

  .item-block {
    padding: 0 0 20px;
    margin-top: 20px;
    border: 0;
    border-bottom: 1px solid #e9ebf0;

    &:last-child {
      border-bottom: none;
    }
  }

  .item-box {
    position: relative;
    height: 50px;
    margin: 20px 0 0;

    .item-value {
      line-height: 34px;
    }
  }
}

.input-result-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.edit-client {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  .editIcon {
    display: none;
    margin-right: 10px;
  }

  &:hover {
    .editIcon {
      display: block;
    }
  }
}

.action-box {
  display: flex;
  align-items: center;
}

.noEdit {
  cursor: not-allowed;
}

.selectClientBox {
  position: absolute;
  top: 33px;
  left: 0;
  z-index: 2;
  display: flex;
  flex-direction: column;
  width: 320px;
  padding: 10px 14px;
  background: #fff;
  border: 1px solid var(--el-border-color-light);
  border-radius: 10px;
  box-shadow: var(--el-dropdown-menu-box-shadow);

  .table-list {
    width: 100%;
    // display: flex;
    //flex-direction: column;
    //align-items: center;
    //justify-content: flex-start;
    // height: 500px;
    min-height: 100px;
    max-height: 50vh;
    overflow: auto;
    text-align: center;

    .list {
      padding: 0;
      margin: 0;
      list-style: none;
    }

    .item {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 44px;
      font-weight: 400;
      cursor: pointer;

      .left {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        color: var(--font-color-level1);

        .name-text {
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
        }
      }
    }
  }
}

@media screen and (width <= 768px) {
  .detail-content {
    display: block;

    .detail-info {
      width: 100%;
      min-width: 100%;
      height: auto;
      max-height: unset !important;
    }

    .notes-box {
      width: 100%;
      min-width: 100%;
    }
  }
}

@media screen and (width <= 480px) {
  .el-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .detail-content {
    .item-block {
      padding-bottom: 10px;
    }

    .item-box {
      margin-top: 10px;
    }

    .detail-info {
      padding: 10px 15px;
    }

    .notes-box {
      padding: 10px;
    }
  }
}
</style>
