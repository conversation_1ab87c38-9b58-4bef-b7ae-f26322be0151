<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick, watch } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { leadsControllerIndex } from "@/api/admin/leads";
import dayjs from "dayjs";
import { useCommonStoreHook } from "@/store/modules/common";
import { Loader } from "@googlemaps/js-api-loader";
import { googleMapConfig } from "@/config";
import { MarkerClusterer, DefaultRenderer } from "@googlemaps/markerclusterer";
import { orderControllerIndex } from "@/api/admin/order";
import { Search } from "@element-plus/icons-vue";
import LocationIcon from "@/assets/svg/location.svg?component";
import { serviceControllerIndex } from "@/api/admin/service";
import { accountControllerIndex } from "@/api/admin/admin-account";
import CirCleText from "@/components/CircleText";

// 自定义渲染器（重点修改颜色部分）
class ColorfulRenderer extends Default<PERSON>enderer {
  render(cluster: { count: number; position: google.maps.LatLng }) {
    // 根据聚合数量选择颜色
    // console.log(cluster);
    // console.log("********");
    let defalut = 0;
    const regex = /[A-Za-z]{2}/;
    if (cluster.markers.length) {
      cluster.markers.forEach(item => {
        // console.log(item.innerText);
        const match = item.innerText.match(regex);
        if (match) {
          defalut = 1;
        }
      });
    }
    // const match = str.match(regex);
    // if ()
    const clusterColor = defalut ? "green" : "blue";

    // 创建SVG图标
    const svg = `
      <svg width="48" height="48" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
        <circle cx="24" cy="24" r="22" fill="${clusterColor}" fill-opacity="0.2" stroke="${clusterColor}" stroke-width="2" stroke-opacity="0.3"/>
        <circle cx="24" cy="24" r="18" fill="${clusterColor}" fill-opacity="0.3" stroke="${clusterColor}" stroke-width="2" stroke-opacity="0.4"/>
        <circle cx="24" cy="24" r="14" fill="${clusterColor}" stroke="${clusterColor}" stroke-width="2" stroke-opacity="0.8"/>
        <text x="24" y="30"
              text-anchor="middle"
              fill="white"
              font-size="12"
              font-family="Arial"
              font-weight="bold">
          ${cluster.count}
        </text>
      </svg>
    `;

    return new google.maps.Marker({
      position: cluster.position,
      icon: {
        url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(svg)}`,
        scaledSize: new google.maps.Size(48, 48),
        anchor: new google.maps.Point(24, 24)
      }
    });
  }
}

const props = defineProps({
  dataType: {
    type: String,
    default: "leads" // leads order service
  },
  companyRegion: {
    type: Array, // company region
    default: null
  },
  mapID: {
    type: String,
    default: "googleMapID"
  },
  googleMapStyle: {
    type: Object,
    default: () => {
      return {
        wdith: "100%",
        height: "100vh"
      };
    }
  },
  mapOptions: {
    type: Object,
    default: () => {
      return {
        disableDefaultUI: false,
        gestureHandling: "greedy",
        panControl: true,
        zoomControl: true,
        scaleControl: true,
        streetViewControl: false
      };
    }
  },
  zoom: {
    type: Number,
    default() {
      return 5;
    }
  },
  roundNumber: {
    type: Number,
    default() {
      return 0;
    }
  }
});

const form = reactive({
  date: "",
  product: null,
  result: [],
  installer: null
});
const loading = ref(false);
const dataList = ref([]);
const productCategoryList = ref([]);
const searchInput = ref(null);
const autoAddressList = ref([]);
const resultOptions = ref([]);
// map data
const googleApi = ref(null); // map object
let googleMap, markerClusterer, service, marker, searchBox;
const apiKey = googleMapConfig.apiKey;
const defaultMapCenter = googleMapConfig.defaultMapCenter;
const properties = ref([]);
const startPlaceholder = ref("Start Enquiry date");
const getStaffOptionsLoading = ref(false);
const staffOptions = ref([]);

watch(
  () => props.dataType,
  _value => {
    onReset();
  },
  {
    immediate: true
  }
);

watch(
  () => props.companyRegion,
  _value => {
    onReset();
  },
  {
    immediate: true
  }
);

watch(
  () => [dataList, googleApi.value],
  _val => {
    handleDataList();
  },
  {
    immediate: true,
    deep: true
  }
);

watch(
  () => props.roundNumber,
  _val => {
    if (_val != 0) {
      getDataList();
    }
  },
  {
    immediate: true,
    deep: true
  }
);
async function initMap() {
  //1.load js
  const loader = new Loader({
    apiKey: apiKey,
    version: "weekly",
    libraries: ["places", "maps", "marker"],
    language: googleMapConfig.language
    // region: "Canana"
  });
  const google = await loader.load();
  googleApi.value = google;
  //2.init map
  const option = {
    zoom: props.zoom,
    mapTypeControl: false,
    mapId: props.mapID,
    zoomControl: true,
    zoomControlOptions: {
      position: google.maps.ControlPosition.INLINE_END_BLOCK_START
    }
  };
  option["center"] = defaultMapCenter;
  const map = new google.maps.Map(document.getElementById(props.mapID), option);
  googleMap = map;
  markerClusterer = new MarkerClusterer({
    map: googleMap,
    renderer: new ColorfulRenderer()
  });
  defaultInitmarker(google, option);
}

const emit = defineEmits<{
  (e: "updateMapMarksNum"): Number;
  (e: "toDetail", val: Object): void;
}>();

// for default map
function defaultInitmarker(google, option) {
  searchBox = new google.maps.places.AutocompleteService();
  service = new google.maps.places.PlacesService(googleMap);
  // geocoder = new google.maps.Geocoder();
  marker = new googleApi.value.maps.Marker({
    map: googleMap,
    position: option["center"],
    draggable: true
  });
}

function initSearchData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  getStaffOptions("", 9999);
}

function getStaffOptions(query = "", size = 99) {
  staffOptions.value = [];

  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  getStaffOptionsLoading.value = true;
  const search = query ? "%" + query + "%" : "";
  let companyRegionStr = null;
  if (props.companyRegion.length) {
    companyRegionStr = props.companyRegion.join(",");
  }
  accountControllerIndex(
    search,
    "roles.name:in:install_user|production_assistant|general_manager|super_admin",
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    companyRegionStr
  )
    .then(res => {
      const { data } = res;
      staffOptions.value = data || [];
      getStaffOptionsLoading.value = false;
    })
    .catch(_error => {
      getStaffOptionsLoading.value = false;
    });
}

function onReset() {
  form.date = null;
  if (props.dataType == "order") {
    form.result = ["new_order", "cm", "ready", "installation", "extra"];
    resultOptions.value = [
      {
        value: "new_order",
        label: "New",
        icon: "new_order"
      },
      {
        value: "cm",
        label: "CM",
        icon: "cm"
      },
      {
        value: "ready",
        label: "Ready",
        icon: "ready"
      },
      {
        value: "installation",
        label: "Installation",
        icon: "installation"
      },
      {
        value: "extra",
        label: "Extra",
        icon: "extra"
      }
    ];
    startPlaceholder.value = "Appt Date";
  } else if (props.dataType == "service") {
    form.result = [
      "new",
      "onProduction",
      "ready",
      "installation",
      "outstanding",
      "hold"
    ];
    resultOptions.value = [
      {
        value: "new",
        label: "New Service",
        icon: "new_order"
      },
      {
        value: "onProduction",
        label: "On Production",
        icon: "onProduction"
      },
      {
        value: "ready",
        label: "Ready",
        icon: "ready"
      },
      {
        value: "installation",
        label: "Installation",
        icon: "installation"
      },
      {
        value: "outstanding",
        label: "Outstanding",
        icon: "outstanding"
      },
      {
        value: "hold",
        label: "On Hold",
        icon: "hold"
      }
    ];
  } else {
    form.result = ["new", "followup"];
    resultOptions.value = [
      {
        value: "new",
        label: "New",
        icon: "new"
      },
      {
        value: "followup",
        label: "Followup",
        icon: "followup"
      },
      {
        value: "appointed",
        label: "Appointed",
        icon: "appointed"
      },
      {
        value: "quoted",
        label: "Quoted",
        icon: "quoted"
      },
      {
        value: "sold",
        label: "Sold",

        icon: "sold"
      }
    ];
  }

  getDataList();
}

function getDataList() {
  console.log(props);
  console.log("========");
  if (props.dataType == "order") {
    getOrdersList();
  } else if (props.dataType == "service") {
    getServiceList();
  } else {
    getLeadsList();
  }
}

function getServiceList() {
  const search = "";
  const filterInfo = [];
  dataList.value = [];
  if (form.date) {
    const startDate = dayjs(form.date[0]).format("YYYY-MM-DD 00:00:00");
    const endDate = dayjs(form.date[1]).format("YYYY-MM-DD 23:59:59");
    filterInfo.push(`lead.enquiry_date:gte:${startDate}`);
    filterInfo.push(`lead.enquiry_date:lte:${endDate}`);
  }
  if (form.result && form.result.length) {
    const resultInfo = form.result.join("|");
    filterInfo.push(`result:in:${resultInfo}`);
  }
  if (props.companyRegion && props.companyRegion.length) {
    filterInfo.push("company_region:in:" + props.companyRegion.join("|"));
  }
  const filter = filterInfo.join(",");
  const sort = "";
  const _with = "lead.client,lead.category,payments";
  const _withCount = null;
  const page = null;
  const size = null;
  let subCategoryIds = null;
  let other_param = null;
  if (form.product) {
    subCategoryIds = form.product.join(",");
    other_param = JSON.stringify({ subCategoryIds: subCategoryIds });
  }
  loading.value = true;
  serviceControllerIndex(
    search,
    filter,
    sort,
    _with,
    _withCount,
    page,
    size,
    other_param
  )
    .then(res => {
      loading.value = false;
      const { data } = res;
      dataList.value = data || [];
    })
    .catch(_err => {
      loading.value = false;
    });
}

function getOrdersList() {
  const search = "";
  const filterInfo = [];
  dataList.value = [];
  let apptDate = null;
  if (form.date) {
    const startDate = dayjs(form.date[0]).format("YYYY-MM-DD 00:00:00");
    const endDate = dayjs(form.date[1]).format("YYYY-MM-DD 23:59:59");
    apptDate = startDate + "," + endDate;
  }
  console.log(form);
  let subCategoryIds = null;
  let installerIds = null;
  let other_param = null;
  if (form.product) {
    subCategoryIds = form.product.join(",");
  }
  if (form.installer) {
    installerIds = form.installer.join(",");
  }
  other_param = JSON.stringify({
    subCategoryIds: subCategoryIds,
    installerIds: installerIds,
    apptDate: apptDate
  });

  if (form.result && form.result.length) {
    const resultInfo = form.result.join("|");
    filterInfo.push(`result:in:${resultInfo}`);
  }
  if (props.companyRegion && props.companyRegion.length) {
    filterInfo.push("company_region:in:" + props.companyRegion.join("|"));
  }
  const filter = filterInfo.join(",");
  const sort = "";
  const _with = "lead.client,lead.category,payments,install,cm";
  const _withCount = null;
  const page = null;
  const size = null;
  loading.value = true;
  orderControllerIndex(
    search,
    filter,
    sort,
    _with,
    _withCount,
    page,
    size,
    other_param
  )
    .then(res => {
      loading.value = false;
      const { data } = res;
      dataList.value =
        data.filter(item => {
          return (
            item.result != "extra" ||
            (item.result == "extra" && item.extras == "Yes")
          );
        }) || [];
    })
    .catch(_err => {
      loading.value = false;
    });
}

function getLeadsList() {
  const search = "";
  const filterInfo = [];
  dataList.value = [];
  if (form.date) {
    const startDate = dayjs(form.date[0]).format("YYYY-MM-DD 00:00:00");
    const endDate = dayjs(form.date[1]).format("YYYY-MM-DD 23:59:59");
    filterInfo.push(`enquiry_date:gte:${startDate}`);
    filterInfo.push(`enquiry_date:lte:${endDate}`);
  }
  if (form.result && form.result.length) {
    const resultInfo = form.result.join("|");
    filterInfo.push(`result:in:${resultInfo}`);
  }
  if (props.companyRegion && props.companyRegion.length) {
    filterInfo.push("company_region:in:" + props.companyRegion.join("|"));
  }
  const filter = filterInfo.join(",");
  const sort = "";
  const _with = "client,saleAppointment";
  const _withCount = null;
  const page = null;
  const size = null;
  let subCategoryIds = null;
  let other_param = null;
  if (form.product) {
    subCategoryIds = form.product.join(",");
    other_param = JSON.stringify({ subCategoryIds: subCategoryIds });
  }
  loading.value = true;
  leadsControllerIndex(
    search,
    filter,
    sort,
    _with,
    _withCount,
    page,
    size,
    other_param
  )
    .then(res => {
      loading.value = false;
      const { data } = res;
      dataList.value = data || [];
    })
    .catch(_err => {
      loading.value = false;
    });
}

function handleDataList() {
  if (!googleApi.value) {
    initMap();
    return;
  }
  const list = [];
  const data = JSON.parse(JSON.stringify(dataList.value));
  emit("updateMapMarksNum", data?.length);
  if (data?.length > 0) {
    data.map((item: any) => {
      if (typeof item === "object" && item !== null) {
        let lat = 0;
        let lng = 0;
        let title = "";
        if (props.dataType == "leads") {
          lat = parseFloat(
            item.latitude ? item.latitude : item.client.latitude
          );
          lng = parseFloat(
            item.longitude ? item.longitude : item.client.longitude
          );
          title = item.id;
        } else if (item.lead) {
          lat = parseFloat(
            item.latitude ? item.latitude : item.lead.client.latitude
          );
          lng = parseFloat(
            item.longitude ? item.longitude : item.lead.client.longitude
          );
          title = item.lead.id;
        }
        if (lat && lng) {
          list.push({
            ...item,
            position: {
              lat: lat,
              lng: lng
            },
            title: title,
            marker_type: props.dataType
          });
        }
      }
    });
  }
  properties.value = list;
  setMapMaker();
}

// Add some markers to the map.
function setMapMaker() {
  markerClusterer.clearMarkers();
  if (!googleApi.value.maps.marker || !markerClusterer) {
    return;
  }
  if (!(properties.value && properties.value.length)) {
    return;
  }
  const markers = properties.value.map(property => {
    const AdvancedMarkerElement =
      new googleApi.value.maps.marker.AdvancedMarkerElement({
        map: googleMap,
        content: buildContent(property),
        position: property.position,
        title: property.title + ""
      });

    AdvancedMarkerElement.addListener("click", () => {
      if (property.marker_type == "order") {
        emit("toDetail", property);
      }
    });
    return AdvancedMarkerElement;
  });
  // // Add a marker clusterer to manage the markers.
  markerClusterer.addMarkers(markers);
}

function buildContent(property) {
  const content = document.createElement("div");
  content.classList.add("property");
  content.innerHTML = `
    <div class="appointment-info">
        <span class="mr-1 iconfont just-icon-${property.result}"></span>
        <div class="appointment-title">${property.title}</div>
    </div>`;
  if (property.marker_type == "order") {
    let moreHtml = "";
    let smallName = "";
    let color = "green";
    let dateString = "";
    if (property.result == "installation") {
      if (property.install && property.install.name) {
        smallName = sliceText(property.install.name);
        color = property.install.color;
      }
      dateString = property.installation_date
        ? getDateString(property.installation_date)
        : "";
      moreHtml = `
        <div class="circleText ml-1 mr-1" style="background-color:${color}">${smallName}</div>
          <div>${dateString}</div>
      `;
    } else if (property.result == "cm") {
      if (property.cm && property.cm.name) {
        smallName = sliceText(property.cm.name);
        color = property.cm.color;
      }
      dateString = property.cm_booked_date
        ? getDateString(property.cm_booked_date)
        : "";
      moreHtml = `
        <div class="circleText ml-1 mr-1" style="background-color:${color}">${smallName}</div>
        <div>${dateString}</div>`;
    }
    content.innerHTML = `
    <div class="appointment-info ${property.result}-item">
        <span class="mr-1 iconfont just-icon-${property.result}"></span>
        <div class="appointment-title">${property.title}</div>
        ${moreHtml}
    </div>`;
  }

  return content;
}

function getDateString(date) {
  const checkDate = new Date(date);
  const today = new Date();
  if (checkDate.toDateString() === today.toDateString()) {
    const hours = checkDate.getHours();
    const minutes = checkDate.getMinutes().toString().padStart(2, "0"); // 补零处理
    const ampm = hours >= 12 ? "PM" : "AM";
    const formattedHours = hours % 12 || 12; // 将 0 点转为 12
    console.log(`${formattedHours}:${minutes} ${ampm}`);
    return `${formattedHours}:${minutes} ${ampm}`;
  }
  return "";
}

function sliceText(value) {
  if (!value) return "";
  if (/^[\u4E00-\u9FFF]$/.test(value[0])) {
    return value[0];
  } else {
    return value.slice(0, 2);
  }
}

function initViewMap() {
  nextTick(() => {
    if (!googleApi.value) {
      initMap();
    }
  });
}

function changeInputSearch(e) {
  searchBox.getPlacePredictions({ input: e }, (event, status) => {
    if (status === "OK") {
      autoAddressList.value = event || [];
      // place_id 后面有用，所以只保留存在place_id的数据
      autoAddressList.value = autoAddressList.value.filter(
        x => x && x.place_id
      );
    } else {
      autoAddressList.value = [];
    }
  });
}

// Click one line address
function confirm(e) {
  // 搜索地点和检索地点详细信息
  const request = {
    placeId: e.place_id,
    fields: ["name", "formatted_address", "place_id", "geometry"]
  };
  service.getDetails(request, (event, status) => {
    if (status === "OK") {
      searchInput.value = event.name;
      geocoderHandle(event, true);
      autoAddressList.value = [];
    }
  });
}

function geocoderHandle(addressDetail, resetMaker = false) {
  if (resetMaker) {
    marker.setVisible(false);
    googleMap.setCenter(addressDetail.geometry.location);
    googleMap.setZoom(props.zoom);
    marker.setPosition(addressDetail.geometry.location);
    marker.setVisible(true);
  }
  marker.setVisible(true);
}
onMounted(() => {
  initSearchData();
});
defineExpose({ initViewMap });
</script>
<template>
  <div class="map-view-page flex flex-col h-full">
    <div
      class="search-box flex justify-between align-middle p-[10px] border-0 shrink-0 flex-wrap"
    >
      <div
        class="left flex align-middle mt-[10px]"
        v-if="props.dataType == 'leads'"
      >
        <el-date-picker
          v-model="form.date"
          type="daterange"
          :start-placeholder="startPlaceholder"
          end-placeholder="End date"
          format="YYYY-MM-DD"
          date-format="YYYY/MM/DD"
          class="mr-[10px] border border-[#E9EBF0]"
        />
        <el-button type="primary" @click="getDataList">
          {{ transformI18n("common.filter") }}
        </el-button>
        <el-button @click="onReset()" type="info">{{
          transformI18n("common.clear")
        }}</el-button>
      </div>
      <div class="filter-item">
        <el-select
          v-model="form.result"
          filterable
          clearable
          multiple
          collapse-tags
          class="w-[240px] mr-[10px] ml-1 border-[#E9EBF0] border px-[10px] mt-[10px]"
          placeholder="Filter result"
          @change="getDataList"
        >
          <el-option
            v-for="(item, index) in resultOptions"
            :key="index"
            :label="item.label"
            :value="item.value"
          >
            <span :class="`mr-1 iconfont just-icon-` + item.icon" />
            {{ item.label }}
          </el-option>
        </el-select>
        <el-select
          v-model="form.product"
          clearable
          placeholder="Filter Subproduct"
          class="min-w-[230px] mr-[10px] ml-1 border-[#E9EBF0] border px-[10px] mt-[10px]"
          multiple
          collapse-tags
          collapse-tags-tooltip
          @change="getDataList"
          filterable
        >
          <el-option-group
            v-for="group in productCategoryList"
            :key="group.name"
            :label="group.name"
          >
            <el-option
              v-for="item in group.products"
              :key="item.id"
              :label="item.name"
              :value="`${item.id}`"
            />
          </el-option-group>
        </el-select>
        <el-date-picker
          v-if="props.dataType == 'order'"
          v-model="form.date"
          type="daterange"
          :start-placeholder="startPlaceholder"
          end-placeholder="End date"
          format="YYYY-MM-DD"
          date-format="YYYY/MM/DD"
          @change="getDataList"
          class="!w-[260px] ml-1 border-[#E9EBF0] border px-[10px] mt-[10px]"
        />
        <el-select
          v-if="props.dataType == 'order'"
          v-model="form.installer"
          clearable
          placeholder="Installer"
          class="min-w-[240px] mr-[10px] ml-1 border-[#E9EBF0] border px-[10px] mt-[10px]"
          multiple
          collapse-tags
          :loading="getStaffOptionsLoading"
          @change="getDataList"
          :remote-method="getStaffOptions"
          remote
        >
          <el-option
            v-for="item in staffOptions"
            :key="item.id"
            :label="item.login_name"
            :value="item.id"
          >
            <div class="flex items-center">
              <CirCleText
                :text="item.login_name"
                :size="20"
                :fontSize="'10px'"
                :customBgColor="item.color"
              />
              <span>{{ item.login_name }}</span>
            </div>
          </el-option>
        </el-select>
      </div>
    </div>

    <!--  -->
    <div class="map-box" v-loading="loading" element-loading-text="loading...">
      <div class="pac-card" id="pac-card">
        <div id="pac-container">
          <el-input
            v-model="searchInput"
            placeholder="Location map center"
            id="pac-input"
            @input="changeInputSearch"
            class="shadow"
          >
            <template #append>
              <Search class="mr-1 w-[15px]" />
            </template>
          </el-input>
        </div>
        <div class="address-list-box">
          <div
            class="list-item"
            @click="confirm(item)"
            v-for="(item, index) in autoAddressList"
            :key="index"
          >
            <LocationIcon class="locationIcon" />
            <div class="address">
              <span class="title">{{
                item.structured_formatting.main_text
              }}</span>
              <span>{{ item.structured_formatting.secondary_text }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="mapStyle">
        <div class="mapRightStyle">
          <div
            :style="props.googleMapStyle"
            class="googleMap"
            :id="props.mapID"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.map-box {
  position: relative;
  flex-grow: 1;
  max-height: calc(100vh - 180px);
}
</style>
<style>
.property {
  position: relative;
}

.property::after {
  position: absolute;
  top: 95%;
  left: 50%;
  z-index: 1;
  width: 0;
  height: 0;
  content: "";
  border-top: 7px solid #d0d5dd;
  border-right: 7px solid transparent;
  border-left: 7px solid transparent;
  transition: all 0.3s ease-out;
  transform: translate(-50%, 0);
}

.property::before {
  position: absolute;
  top: 95%;
  left: 50%;
  z-index: 2;
  width: 0;
  height: 0;
  content: "";
  border-top: 6px solid #fff;
  border-right: 6px solid transparent;
  border-left: 6px solid transparent;
  transition: all 0.3s ease-out;
  transform: translate(-50%, 0);
}

.property .appointment-info {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3px;
  font-size: 12px;
  background: #fff;
  border: 1px solid #d0d5dd;
  border-radius: 2ex;
}

.property .new_order-item,
.property .extra-item {
  background: yellow;
  border: 1px solid yellow;
}

.property .new_order-item .iconfont,
.property .extra-item .iconfont {
  color: #000;
}

.property .cm-item {
  color: #fff;
  background: blue;
  border: 1px solid blue;
}

.property .cm-item .iconfont,
.property .ready-item .iconfont,
.property .installation-item .iconfont {
  color: #fff;
}

.property .ready-item {
  color: #fff;
  background: red;
  border: 1px solid red;
}

.property .installation-item {
  color: #fff;
  background: green;
  border: 1px solid green;
}

/* .property .appointment-info .iconfont {
  color: #fff;
} */

.property .circleText {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  background: green;
  border-radius: 50%;
}
</style>

<style scoped lang="scss">
// .just-map-container {
//   position: relative;
// }

.pac-card {
  position: absolute;
  top: 10px;
  left: 20px;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 600px;
  max-height: 500px;
  padding: 0;
  overflow: hidden;
  font-size: 18px;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 1px 4px -1px rgb(0 0 0 / 30%);
}

#pac-container {
  width: 100%;
}

.address-list-box {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
  background: #fff;
  border-radius: 0 0 20px 20px;

  .list-item {
    display: flex;
    align-items: center;
    min-height: 24px;
    padding-top: 6px;
    padding-bottom: 7px;
    line-height: 32px;
    color: #70757a;
  }

  .address {
    width: 540px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    .title {
      font-weight: 500;
      color: #202124;
    }
  }
}

.pac-controls {
  display: inline-block;
  padding: 0;
}

.pac-controls label {
  /* font-family: Roboto; */
  font-size: 13px;
  font-weight: 300;
}

#pac-input {
  z-index: 3;
  width: 450px;
  height: 40px;
  padding: 0 11px 0 13px;
  padding-left: 38px;
  margin: 0;
  margin-left: 12px;
  font-size: 15px;
  font-weight: 300;
  text-overflow: ellipsis;
  background-color: #fff;
  border: 0;
  border-radius: 32px;
  box-shadow: 0 1px 2px rgb(60 64 67 / 30%), 0 1px 3px 1px rgb(60 64 67 / 15%);
}

#pac-input:focus {
  border-color: #4d90fe;
}

.searchIcon {
  position: absolute;
  top: 11px;
  left: 26px;
  width: 20px;
  font-size: 20px;
}

.filter-item {
  display: flex;
  flex-wrap: wrap;
}
</style>
