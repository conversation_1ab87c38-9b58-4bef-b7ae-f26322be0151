<script setup lang="ts">
import { transformI18n } from "@/plugins/i18n";
import { reactive, ref, onMounted, watch, nextTick } from "vue";
import { useCommonStoreHook } from "@/store/modules/common";
import {
  columns,
  allResults,
  showFields,
  tableActionList,
  cancelChildren,
  selectResults
} from "./leads_data.ts";
import {
  draftLeadsControllerIndex,
  draftLeadsControllerClose,
  draftLeadsControllerConfirm,
  leadsControllerIndex,
  leadsControllerRevokeLeads
} from "@/api/admin/leads";
import DataTable from "@/components/DataTable";
import CirCleText from "@/components/CircleText";
import SearchIcon from "@/assets/svg/search.svg?component";
import EmptySpan from "@/components/EmptySpan";
import { hasAuth } from "@/router/utils";
import { ElMessageBox, ElLoading, ElMessage } from "element-plus";
import ChangeResult from "@/components/ChangeResult/ChangeResult.vue";
import CreateAppointment from "@/components/CreateAppointment/CreateAppointment.vue";
import { accountControllerIndex } from "@/api/admin/admin-account";
import ResultDialog from "@/components/ResultDialog";
import { useUserStoreHook } from "@/store/modules/user";
import { removeElesFromArray } from "@/utils/common";
import ConvertToOrderDialog from "@/components/ConvertToOrderDialog";
import dayjs from "dayjs";
import { formatTime } from "@/utils/form";
import { storageLocal } from "@pureadmin/utils";
import MoveToBlacklistDialog from "@/components/MoveToBlacklistDialog";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const currentRoles = ref(useUserStoreHook()?.roles);
const productCategoryList = ref([]);
const sourceList = ref([]);
const staffOptions = ref([]);
const resultDialogRef = ref(null);
const convertToOrderDialogRef = ref(null);
const moveToBlacklistDialogRef = ref(null);
const props = defineProps({
  args: {
    type: Object,
    default: () => ({})
  },
  loadDataFlag: {
    type: String,
    default: null
  },
  allStaffList: {
    type: Array,
    default() {
      return [];
    }
  }
});

// Listen for changes in props properties
watch(
  () => props.args,
  newValue => {
    if (newValue) {
      initData();
    }
  }
);
watch(
  () => props.loadDataFlag,
  (_newValue, _oldValue) => {
    loadData();
  }
);
const resultCascaderProps = {
  checkStrictly: true,
  emitPath: true
};
const baseForm = {
  product_category_id: null,
  address: null,
  source_id: null,
  client: null,
  spoken_to: null,
  appt_setter_id: null,
  date: null,
  appt_date: null,
  otherParam: {
    clientKeyWords: "",
    subCategoryIds: "",
    subSource: ""
  },
  result: null,
  product: []
};
const indexQuery = reactive({
  search: "",
  sort: "-id",
  filter: "",
  _with: "sales,source,category,spokenTo",
  withCount: ""
});
const form = reactive(baseForm);
const formRef = ref(null);
const dataTableRef = ref(null);
const currentCompanyRegion = ref([]);
const currentTagResult = ref(null); // leads tab type :all,draft new
const mv = ref();
const dialogPanel = ref();
const changeResult = ref(null);
const createAppointment = ref(null);
const getStaffOptionsLoading = ref(false);
const newColumns = ref([...columns]);
const currentShowFields = reactive(JSON.parse(JSON.stringify(showFields)));
const showTable = ref(false);
const cancelResultsOption = ref([]);
const cascaderKey = ref([]);
const showCascader = ref([]);
onMounted(() => {
  initData();
  initSearchData();
});
const emit = defineEmits<{
  (e: "toPassNewInfo", val: Object): void;
  (e: "toDetail", val: Object): void;
  (e: "updateListSummary"): void;
}>();

const draftSearchTip = "You can search by Client name,phone,address";
const leadsSearchTip = "You can search by ID,Leads Result";

function initData() {
  currentTagResult.value = props.args.result;
  currentCompanyRegion.value = props.args.companyRegion;
  // get list filter info
  const lastFilterParam = storageLocal().getItem(
    `leadsfilter_${currentTagResult.value}`
  );
  if (lastFilterParam && lastFilterParam["form"]) {
    Object.assign(form, lastFilterParam["form"]);
  }
  if (lastFilterParam && lastFilterParam["search"]) {
    indexQuery.search = lastFilterParam["search"];
  }

  showTable.value = false;
  // Initialize show fields data

  const lastLeadsShowFields = storageLocal().getItem("lastLeadsShowFields");
  if (lastLeadsShowFields) {
    Object.assign(currentShowFields, lastLeadsShowFields);
  }
  let checkShowFields = currentShowFields["default"];
  if (currentShowFields[props.args.result]) {
    checkShowFields = currentShowFields[props.args.result];
  }
  newColumns.value = columns.map(column => {
    let hide = false;
    hide = !checkShowFields.includes(String(column.prop));
    return { ...column, hide };
  });

  // handle result option
  cancelResultsOption.value = JSON.parse(JSON.stringify(allResults));
  cancelResultsOption.value.forEach(item => {
    if (item.value !== "cancelled") {
      // for set disabled style
      delete item.children;
      item.disabled = true;
    } else {
      item.disabled = false;
    }
  });
  showTable.value = true;
}

function getResultOptions(result) {
  let resultsListNoHandle = JSON.parse(JSON.stringify(allResults));
  if (result === "appointed") {
    resultsListNoHandle = [
      ...resultsListNoHandle,
      {
        value: "appointed",
        label: "Appointed",
        icon: "appointed",
        disabled: true
      }
    ];
  }
  //Change the result disabled property based on the role
  const resultsListHandle = [];
  let canToResultsList = [];
  if (hasAuth("changeLeadsResult")) {
    switch (result) {
      case "new":
        canToResultsList = ["followup", "quoted"];
        break;
      case "followup":
        canToResultsList = ["quoted", "followup"];
        break;
      case "appointed":
        canToResultsList = ["followup", "quoted", "sold"];
        break;
      case "quoted":
        canToResultsList = ["followup", "sold", "quoted"];
        break;
    }
  } else if (hasAuth("followupLeads")) {
    switch (result) {
      case "new":
        canToResultsList.push("followup");
        break;
      case "appointed":
        canToResultsList.push("followup");
        break;
      case "followup":
        canToResultsList.push("followup");
        break;
    }
  }

  resultsListNoHandle.map(resultItem => {
    if (
      canToResultsList.length &&
      canToResultsList.includes(resultItem.value)
    ) {
      resultItem.disabled = false;
    } else {
      resultItem.disabled = true;
    }
    if (resultItem.children && resultItem.children.length) {
      resultItem.children.forEach(child => {
        child.disabled = resultItem.disabled;
      });
    }
    resultsListHandle.push(resultItem);
  });
  return resultsListHandle;
}

// Get the current row  operation
function getActionList(result, row) {
  // get result by result
  let actionList = [];
  if (props.args.result === "draft") {
    actionList = tableActionList["draft"];
    result = "draft";
  } else if (result && tableActionList[result]) {
    actionList = tableActionList[result];
  } else {
    return [];
  }
  if (!actionList.length) {
    return [];
  }
  if (currentRoles.value.includes("super_admin")) {
    return actionList;
  }
  // Filter actions by permission
  let removeList = [];
  const canCancel = hasAuth("cancelLeads");
  const canConfirm = hasAuth("confirmLeads");
  const canMoveToBlackList = hasAuth("moveToBlacklist");
  let canAddAppointment = hasAuth("addLeadsAppointment");
  const canRevoked = hasAuth("revokeLeads");
  if (canAddAppointment) {
    canAddAppointment =
      row.sale_appointment && row.sale_appointment.length ? false : true;
  }

  if (!canCancel) {
    removeList.push("cancel");
  }
  if (!canMoveToBlackList) {
    removeList.push("addToBlacklist");
  }
  if (!canRevoked) {
    removeList.push("revoke");
  }
  switch (result) {
    case "draft":
      if (!canConfirm) {
        removeList = [...removeList, ...["confirm"]];
      }
      break;
    case "new":
    case "quoted":
    case "followup":
      if (!canAddAppointment) {
        removeList = [...removeList, ...["createAppt"]];
      }
      break;
  }
  if (removeList.length) {
    actionList = removeElesFromArray(actionList, removeList);
  }

  return actionList;
}

function getListApiName() {
  if (props.args.result === "draft") {
    return draftLeadsControllerIndex;
  } else {
    return leadsControllerIndex;
  }
}

function onSearch() {
  loadData();
}

function onReset() {
  indexQuery.search = "";
  formRef.value.resetFields();
  Object.keys(form).forEach(key => {
    switch (key) {
      case "otherParam":
        form[key] = {
          clientKeyWords: "",
          subCategoryIds: "",
          subSource: ""
        };
        break;
      default:
        form[key] = null;
        break;
    }
  });
  loadData();
}

function loadData() {
  // Cache filter info
  const filterParam = {
    form: form,
    search: indexQuery.search
  };
  storageLocal().setItem(`leadsfilter_${currentTagResult.value}`, filterParam);
  dataTableRef.value.loadData();
}

function tableQueryParams(form) {
  const filterInfo = [];
  Object.keys(baseForm).forEach(key => {
    if (form[key]) {
      if (key == "date") {
        const startDate = dayjs(form.date[0]).format("YYYY-MM-DD 00:00:00");
        const endDate = dayjs(form.date[1]).format("YYYY-MM-DD 23:59:00");
        filterInfo.push(`enquiry_date:gte:${startDate}`);
        filterInfo.push(`enquiry_date:lte:${endDate}`);
      } else if (key == "address") {
        if (props.args?.result == "draft") {
          filterInfo.push(`address:like:%${form.address}%`);
        }
      } else if (key == "appt_date") {
        const startDate = dayjs(form.appt_date[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        const endDate = dayjs(form.appt_date[1]).format("YYYY-MM-DD 23:59:00");
        filterInfo.push(`appt_date:gte:${startDate}`);
        filterInfo.push(`appt_date:lte:${endDate}`);
      } else if (key == "product") {
        form.otherParam.subCategoryIds = form.product.join(",");
      } else if (key != "otherParam") {
        filterInfo.push(`${key}:eq:` + form[key]);
      }
    }
  });
  if (props.args?.result) {
    props.args?.result == "draft"
      ? filterInfo.push("status:eq:active")
      : props.args.result != "all"
      ? filterInfo.push(`result:eq:` + props.args.result)
      : null;
  }
  if (props.args?.result != "draft") {
    indexQuery._with =
      "sales,source,category,spokenTo,client.leads,apptSetter,saleAppointment.client,saleAppointment.leads,salesConsultant,contracts,order";
  }
  // if (props.args?.result == "followup") {
  //   indexQuery.sort = "-id,-followup_time";
  // }
  if (props.args?.companyRegion?.length) {
    filterInfo.push("company_region:in:" + props.args?.companyRegion.join("|"));
  }
  const param = Object.assign(
    { ...indexQuery },
    {
      filter: filterInfo.length === 0 ? indexQuery.filter : filterInfo.join(),
      otherParam: JSON.stringify(form.otherParam)
    }
  );
  return param;
}

function initSearchData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  sourceList.value = basicData["sourceList"];
  staffOptions.value = props.allStaffList;
  // staffOptions.value = [];
  // props.allStaffList.map(item => {
  //   if (item["roles"]?.length) {
  //     item["roles"].map(role => {
  //       if (role.name === "lead_taker_user") {
  //         staffOptions.value.push(item);
  //       }
  //     });
  //   }
  // });
}

function getStaffOptions(query = "") {
  const filter = "";
  // const filter = "roles.name:eq:lead_taker_user";
  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  const size = 99;
  getStaffOptionsLoading.value = true;
  const search = query ? "%" + query + "%" : "";
  let companyRegionStr = null;
  if (props.args.companyRegion.length) {
    companyRegionStr = props.args.companyRegion.join(",");
  }
  accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    companyRegionStr
  )
    .then(res => {
      const { data } = res;
      staffOptions.value = data || [];
      getStaffOptionsLoading.value = false;
    })
    .catch(_error => {
      getStaffOptionsLoading.value = false;
    });
}

// action processing
function handleAction(row, actionName, other = null) {
  if (actionName == "cancel") {
    closeLeads(row, other);
  } else if (actionName == "confirm") {
    confirmLeads(row);
  } else if (actionName == "edit") {
    emit("toDetail", row);
  } else if (actionName == "createAppt") {
    createdAppointment(row);
  } else if (actionName == "convertToOrder") {
    convertToOrder(row);
  } else if (actionName == "addToBlacklist") {
    moveToBlacklist(row);
  } else if (actionName == "revoke") {
    revokeCanceledLeads(row);
  }
}

function revokeCanceledLeads(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>revoke</strong> the <strong style='color:var(--el-color-primary)'>canceled</strong> leads?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    leadsControllerRevokeLeads(row.id, row)
      .then(res => {
        if (res.success) {
          ElMessage.success(res.message);
        } else {
          ElMessage.error(res.message);
        }
        dialogPanel.value.close();
        loadData();
        emit("updateListSummary");
      })
      .catch(() => {
        ElMessage.error("Revoked processed fail");
        dialogPanel.value.close();
      });
  });
}

function moveToBlacklist(row) {
  const data = {
    lead_id: row.id
  };
  moveToBlacklistDialogRef.value.show(data);
}
function convertToOrder(row) {
  const data = {
    lead_id: row.id
  };
  convertToOrderDialogRef.value.show(data).then(actionOver => {
    if (actionOver) {
      loadData();
    }
  });
}

// Refusing to perform the confirm operation prompt
function showResultDialog(actionType = "confirmLeads", row = null) {
  let data = {};
  if (actionType === "confirmLeads") {
    data = {
      title: "incomplete Client Info",
      content:
        "Please complete the client information before change to new lead",
      type: "warning",
      buttons: [
        {
          text: "Got it",
          onClick: hideResultDialog
        }
      ]
    };
  } else if (actionType === "confirmLeadsWithoutCompany") {
    data = {
      title: "incomplete Client Info",
      content:
        "Please select company for the draft leads before change to new lead",
      type: "warning",
      buttons: [
        {
          text: "Got it",
          onClick: hideResultDialog
        }
      ]
    };
  }
  nextTick(() => {
    resultDialogRef.value.show(data).then(_res => {
      if (actionType === "confirmLeads") {
        onRowClick(row);
      }
    });
  });
}
function createdAppointment(row) {
  if (row.sale_appointment.length) {
    ElMessageBox.confirm(
      `There is already an <strong>outstanding</strong> you can modify it for this leads?`,
      transformI18n("buttons.hsSystemPrompts"),
      {
        confirmButtonText: transformI18n("buttons.hsConfirm"),
        cancelButtonText: transformI18n("buttons.hsCancel"),
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    ).then(() => {
      createAppointment.value
        .show(row.sale_appointment[0], "edit")
        .then(() => {
          loadData();
          emit("updateListSummary");
        })
        .catch(() => {
          dialogPanel.value.close();
        });
    });
  } else {
    createAppointment.value
      .show(row)
      .then(() => {
        loadData();
        emit("updateListSummary");
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  }
}
function confirmLeads(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>change</strong> the <strong style='color:var(--el-color-primary)'>${currentTagResult.value}</strong> leads to a new leads?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    //
    if (!row.company_region && userBaseInfo.value.isPlatformUser) {
      showResultDialog("confirmLeadsWithoutCompany", row);
    } else if (!row.phone || !row.product_category_id || !row.source_id) {
      showResultDialog("confirmLeads", row);
    } else {
      dialogLoading();
      draftLeadsControllerConfirm(row.id, row)
        .then(res => {
          if (res.success) {
            ElMessage.success(res.message);
          } else {
            ElMessage.error(res.message);
          }
          dialogPanel.value.close();
          loadData();
          emit("updateListSummary");
        })
        .catch(() => {
          ElMessage.error("Confirm processed fail");
          dialogPanel.value.close();
        });
    }
  });
}

function closeLeads(row, subResult = null) {
  if (props.args?.result != "draft") {
    if (
      row.order &&
      (row.order.result == "completion" || row.order.result == "cancelled")
    ) {
      const result = row.order.result == "cancelled" ? "canceled" : "completed";
      ElMessage.error(
        "As the product order has been " +
          result +
          ", the associated leads cannot be canceled anymore."
      );
      return;
    }
    const data = {
      id: row.id,
      type: "leads",
      result: "cancelled",
      row,
      sub_result: subResult
    };
    changeResult.value
      .show(data)
      .then(() => {
        loadData();
        emit("updateListSummary");
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  } else {
    ElMessageBox.confirm(
      `Are you sure you want to <strong>close</strong> the <strong style='color:var(--el-color-primary)'>${currentTagResult.value}</strong> leads?`,
      transformI18n("buttons.hsSystemPrompts"),
      {
        confirmButtonText: transformI18n("buttons.hsConfirm"),
        cancelButtonText: transformI18n("buttons.hsCancel"),
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    ).then(() => {
      dialogLoading();
      draftLeadsControllerClose(row.id)
        .then(() => {
          dialogPanel.value.close();
          loadData();
        })
        .catch(() => {
          dialogPanel.value.close();
        });
    });
  }
}

// remove set spokento function, for now the first create leads user is the user spokento
// function setSpokenTo(row) {
//   if (row.spoken_to || currentTagResult.value != "draft") {
//     return;
//   }
//   ElMessageBox.confirm(
//     `Are you sure you want to <strong>spoke</strong> to the client of the <strong style='color:var(--el-color-primary)'>${currentTagResult.value}</strong> leads?`,
//     transformI18n("buttons.hsSystemPrompts"),
//     {
//       confirmButtonText: transformI18n("buttons.hsConfirm"),
//       cancelButtonText: transformI18n("buttons.hsCancel"),
//       type: "warning",
//       dangerouslyUseHTMLString: true,
//       draggable: true
//     }
//   ).then(() => {
//     dialogLoading();
//     draftLeadsControllerSetspoken(row.id)
//       .then(() => {
//         dialogPanel.value.close();
//         loadData();
//       })
//       .catch(() => {
//         dialogPanel.value.close();
//       });
//   });
// }

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function hideResultDialog() {
  resultDialogRef.value.hide(true);
}

// Change leads result
function handleResultChange(value, row) {
  const hasSonResult = ["followup", "quoted"];
  if (hasSonResult.includes(value[0]) && !value[1]) {
    return false;
  }
  const result = value[0];
  const subResult = value[1];
  const data = {
    id: row.id,
    type: "leads",
    result: result,
    sub_result: subResult,
    row
  };
  changeResult.value
    .show(data)
    .then(() => {
      loadData();
      emit("updateListSummary");
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}

function stopBubbling() {
  return;
}

//  to update table list new info (total)
function toPassNewInfo(newInfo) {
  cascaderKey.value = generateUniqueRandomArray(newInfo.pageSize);
  showCascader.value = Array(newInfo.pageSize).fill(true);
  emit("toPassNewInfo", newInfo);
}

/** 行点击 */
function onRowClick(row) {
  if (!row) {
    return;
  }
  emit("toDetail", row);
}

// get current row result icon
function getCurrentResultIcon(result) {
  if (!result) {
    return;
  }
  const resultAllList = [
    ...allResults,
    {
      value: "appointed",
      label: "Appointed",
      icon: "appointed"
    }
  ];
  if (typeof result === "string") {
    return resultAllList.find(item => item.value === result)?.icon;
  } else if (result instanceof Array) {
    return resultAllList.find(item => item.value === result[0])?.icon;
  }
}

function handleResultClose(isShow, index) {
  if (!isShow) {
    cascaderKey.value[index] = Math.floor(Math.random() * 100);
    showCascader.value[index] = false;
    nextTick(() => {
      showCascader.value[index] = true;
    });
  }
}

function generateUniqueRandomArray(length: number): number[] {
  const arr: number[] = [];

  while (arr.length < length) {
    const randomNum = Math.floor(Math.random() * 100); // 生成随机数，此处范围是 0 到 99，根据需要进行调整
    if (!arr.includes(randomNum)) {
      arr.push(randomNum);
    }
  }

  return arr;
}

// table list order change
function sortChange(column) {
  const order = column.order === "ascending" ? "" : "-";
  const orderField = column.prop;
  indexQuery.sort = order + orderField;
  loadData();
}

// table column show field change handle:storage the change
function dynamicColumnsChange(value) {
  if (value && value.dynamicColumns) {
    const newShowFields = [];
    value.dynamicColumns.map(item => {
      if (!item.hide) {
        newShowFields.push(item.prop);
      }
    });
    currentShowFields[props.args.result] = newShowFields;
    storageLocal().setItem("lastLeadsShowFields", currentShowFields);
  }
}

function handleSearch() {
  loadData();
}
defineExpose({ loadData });
</script>
<template>
  <div ref="mv" class="leads-list-page">
    <div class="flex justify-between w-full h-[50px] list-form">
      <el-form ref="formRef" :inline="true" :model="form">
        <el-form-item :label="'Product:'" prop="product_category_id">
          <el-select
            v-model="form.product_category_id"
            @change="onSearch()"
            clearable
          >
            <el-option
              v-for="item in productCategoryList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="'SubProduct:'" class="sub-product-select">
          <el-select
            v-model="form.product"
            placeholder="Filter SubProduct"
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            class="w-[230px]"
          >
            <el-option-group
              v-for="group in productCategoryList"
              :key="group.name"
              :label="group.name"
            >
              <el-option
                v-for="item in group.products"
                :key="item.id"
                :label="item.name"
                :value="`${item.id}`"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="'Suburb:'"
          prop="address"
          v-if="currentTagResult === 'draft'"
        >
          <el-input
            v-model="form.address"
            autocomplete="off"
            @input="onSearch()"
            :placeholder="transformI18n('common.input')"
            clearable
          />
        </el-form-item>
        <el-form-item
          :label="'Client:'"
          prop="otherParam.clientKeyWords"
          v-if="currentTagResult !== 'draft'"
        >
          <el-input
            v-model="form.otherParam.clientKeyWords"
            autocomplete="off"
            @input="onSearch()"
            :placeholder="transformI18n('common.input')"
            clearable
          />
        </el-form-item>
        <el-form-item :label="'Source:'" prop="source_id">
          <el-select
            v-model="form.source_id"
            @change="onSearch()"
            placeholder="Select"
            clearable
            class="w-[120px]"
          >
            <el-option
              v-for="item in sourceList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item :label="'SubSource:'" prop="otherParam.subSource">
          <el-input
            v-model="form.otherParam.subSource"
            autocomplete="off"
            @input="onSearch()"
            :placeholder="transformI18n('common.input')"
            clearable
          />
        </el-form-item>
        <el-form-item :label="'Enquiry Date:'" class="!w-[320px]">
          <el-date-picker
            v-model="form.date"
            type="daterange"
            start-placeholder="Start"
            end-placeholder="End"
            format="DD/MM/YYYY"
            date-format="YYYY/MM/DD"
            class="border-0"
          />
        </el-form-item>
        <el-form-item
          v-if="
            !currentTagResult ||
            [
              'appointed',
              'sold',
              'quoted',
              'followup',
              'cancelled',
              'all'
            ].includes(currentTagResult)
          "
          :label="'Appt Date:'"
          class="!w-[320px]"
        >
          <el-date-picker
            v-model="form.appt_date"
            type="daterange"
            start-placeholder="Start"
            end-placeholder="End"
            format="DD/MM/YYYY"
            date-format="YYYY/MM/DD"
            class="border-0"
          />
        </el-form-item>
        <el-form-item
          :label="'Appt Setter:'"
          prop="appt_setter_id"
          v-if="currentTagResult !== 'draft'"
          class="!w-[210px]"
        >
          <el-select
            v-model="form.appt_setter_id"
            @change="onSearch()"
            placeholder="Select"
            filterable
            :remote-method="getStaffOptions"
            :loading="getStaffOptionsLoading"
            remote
            clearable
          >
            <el-option
              v-for="item in staffOptions"
              :key="item.id"
              :label="item.login_name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          :label="'Result:'"
          prop="result"
          v-if="currentTagResult == 'all'"
          class="!w-[210px]"
        >
          <el-select
            v-model="form.result"
            @change="onSearch()"
            placeholder="Select"
            filterable
            remote
            clearable
          >
            <el-option
              v-for="item in selectResults"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="action-box">
          <el-button type="primary" @click="onSearch()">
            {{ transformI18n("common.filter") }}
          </el-button>
          <el-button @click="onReset()" type="info">{{
            transformI18n("common.clear")
          }}</el-button>
        </el-form-item>
      </el-form>
      <div class="search-box flex">
        <!-- <el-tooltip
          class="item"
          effect="dark"
          content="You can search by ID, Client Name, or Phone number"
          placement="top"
        >
          <InfoFilled class="mr-1" style="width: 16px" />
        </el-tooltip> -->

        <el-input
          ref="inputRef"
          v-model="indexQuery.search"
          size="small"
          clearable
          :placeholder="transformI18n('leads.keywordPlaceHolder')"
          @input="handleSearch()"
          class="table-search-input"
        >
          <template #prefix>
            <el-tooltip
              class="item"
              effect="dark"
              :content="
                currentTagResult == 'draft' ? draftSearchTip : leadsSearchTip
              "
              placement="top"
            >
              <SearchIcon class="search-icon" />
            </el-tooltip>
          </template>
        </el-input>
      </div>
    </div>
    <data-table
      v-if="showTable"
      ref="dataTableRef"
      :columns="newColumns"
      :source="getListApiName()"
      :form="form"
      :slotNames="[
        'operation',
        'result',
        'salesManager',
        'salesConsultant',
        'apptSetter',
        'client',
        'phone',
        'email',
        'address',
        'spokenTo',
        'subResult',
        'category',
        'product',
        'sold',
        'retail',
        'quoted',
        'enquiryDate',
        'order_result',
        'appt_date',
        'sold_date',
        'followup_time',
        'suburb',
        'updated_at'
      ]"
      :query-params="tableQueryParams"
      :header-cell-style="{
        background: 'var(--el-table-row-hover-bg-color)',
        color: 'var(--el-text-color-primary)'
      }"
      @handleRowClick="onRowClick"
      @toPassNewInfo="toPassNewInfo"
      @sortChange="sortChange"
      @dynamicColumnsChange="dynamicColumnsChange"
    >
      <template #client="{ row }">
        <span class="iconfont just-icon-client mr-2" />
        <span v-if="row.client && row.client.given_name"
          >{{ row.client.given_name }} {{ row.client.surname }}</span
        >
        <span v-else-if="row.given_name"
          >{{ row.given_name }} {{ row.surname || "" }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #phone="{ row }">
        <span v-if="row.client && row.client.phone">{{
          row.client.phone
        }}</span>
        <EmptySpan v-else :text="row.phone || ''" />
      </template>
      <template #email="{ row }">
        <span v-if="row.client && row.client.email">
          {{ row.client.email }}
        </span>
        <span v-else-if="row.email">{{ row.email }}</span>
        <span v-else class="empty">Empty</span>
      </template>
      <template #address="{ row }">
        <span v-if="row.address">{{ row.address }}</span>
        <span v-else-if="row.client && row.client.address">
          {{ row.client.address }}
        </span>
        <span v-else class="empty">Empty</span>
      </template>
      <template #suburb="{ row }">
        <span v-if="row.client && row.client.suburb">
          {{ row.client.suburb }}
        </span>
        <span v-else-if="row.suburb">{{ row.suburb }}</span>
        <span v-else class="empty">Empty</span>
      </template>
      <template #spokenTo="{ row }">
        <div>
          <!-- <CirCleText
            :text="row.spoken_to.login_name"
            v-if="row.spoken_to && row.spoken_to.login_name"
          /> -->
          <EmptySpan :text="row.spoken_to ? row.spoken_to.login_name : ''" />
        </div>
      </template>

      <template #subResult="{ row }">
        <span
          :class="
            'mr-1 iconfont just-icon-' +
            getCurrentResultIcon(row.result) +
            ' input-result-icon'
          "
          v-if="row.sub_result"
        />
        <EmptySpan :text="row.sub_result || ''" />
      </template>

      <template #apptSetter="{ row }">
        <!-- <CirCleText
          :text="row.appt_setter.name"
          v-if="row.appt_setter && row.appt_setter.name"
        /> -->
        <EmptySpan :text="row.appt_setter ? row.appt_setter.name : ''" />
      </template>
      <template #salesConsultant="{ row }">
        <!-- <CirCleText
          :text="row.appt_setter.name"
          v-if="row.appt_setter && row.appt_setter.name"
        /> -->
        <EmptySpan
          :text="row.sales_consultant ? row.sales_consultant.name : ''"
        />
      </template>
      <template #salesManager="{ row }">
        <!-- <CirCleText
          :text="row.sales.login_name"
          v-if="row.sales && row.sales.login_name"
        /> -->
        <EmptySpan :text="row.sales ? row.sales.login_name : ''" />
      </template>
      <template #category="{ row }">
        <CirCleText
          :text="row.category.name"
          v-if="row.category"
          :customBgColor="row.category.color"
          :showNum="1"
        />
        <EmptySpan :text="row.category ? row.category.name : ''" />
      </template>
      <template #product="{ row }">
        <EmptySpan :text="row.product ? row.product : ''" />
      </template>
      <template #enquiryDate="{ row }">
        <span v-if="row.enquiry_date">
          {{ formatTime(row.enquiry_date, "DD/MM/YYYY") }}</span
        >
        <EmptySpan :text="''" v-else />
      </template>
      <template #retail="{ row }">
        <EmptySpan :text="row.retail ? '$ ' + row.retail : ''" />
      </template>
      <template #sold="{ row }">
        <EmptySpan :text="row.sold ? '$ ' + row.sold : ''" />
      </template>
      <template #quoted="{ row }">
        <EmptySpan :text="row.quoted ? '$ ' + row.quoted : ''" />
      </template>
      <template #updated_at="{ row }">
        <span v-if="row.updated_at">{{
          formatTime(row.updated_at, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #order_result="{ row }">
        <span v-if="row.order">
          {{ transformI18n("orders.result_" + row.order.result) }}
        </span>
      </template>
      <template #sold_date="{ row }">
        <span v-if="row.sold_date">{{
          formatTime(row.sold_date, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #appt_date="{ row }">
        <span v-if="row.appt_date">{{
          formatTime(row.appt_date, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #followup_time="{ row }">
        <span v-if="row.followup_time">{{
          formatTime(row.followup_time, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #result="{ row, $index }">
        <div class="result-container" @click.stop="stopBubbling()">
          <span v-if="currentTagResult === 'draft'" class="table-draft-result">
            <span class="mr-1 iconfont just-icon-draft" />
            <span>Draft Leads</span>
          </span>
          <span
            v-else-if="row.result === 'cancelled'"
            class="table-draft-result"
          >
            <span class="mr-1 iconfont just-icon-cancelled" />
            <span class="capitalize">cancelled</span>
          </span>
          <div v-else-if="row.order_id" class="table-draft-result">
            <span
              :class="
                'mr-1 iconfont just-icon-' + getCurrentResultIcon(row.result)
              "
            />
            <span class="capitalize">{{ row.result }}</span>
          </div>
          <div v-else-if="row.result">
            <span
              :class="
                'mr-1 iconfont just-icon-' +
                getCurrentResultIcon(row.result) +
                ' input-result-icon'
              "
            />
            <el-cascader
              :options="getResultOptions(row.result)"
              :show-all-levels="false"
              :model-value="row.result"
              @change="value => handleResultChange(value, row)"
              @visible-change="isShow => handleResultClose(isShow, $index)"
              :props="resultCascaderProps"
              :key="cascaderKey[$index]"
              v-if="showCascader[$index]"
            >
              <template #default="{ data }">
                <span
                  :class="'cascader-label' + (data.disabled ? ' disabled' : '')"
                >
                  <span
                    :class="'mr-1 iconfont just-icon-' + data.icon"
                    v-if="data.icon"
                  />
                  {{ data.label }}</span
                >
              </template>
            </el-cascader>
          </div>
        </div>
      </template>

      <template #operation="{ row }">
        <div
          @click.stop="stopBubbling()"
          v-if="getActionList(row.result, row).length"
        >
          <el-menu
            class="table-menu border-[0px] h-[20px]"
            mode="horizontal"
            :ellipsis="false"
            menu-trigger="hover"
          >
            <el-sub-menu
              index="1"
              class="!p-[0px] w-[50px]"
              popperClass="table-menu-popper"
            >
              <template #title
                ><span class="iconfont just-icon-gengduo"
              /></template>
              <template
                v-for="(actionItem, actionIndex) in getActionList(
                  row.result,
                  row
                )"
              >
                <el-sub-menu
                  :index="`1-${actionIndex}`"
                  :key="actionIndex + 'cancel'"
                  v-if="actionItem == 'cancel' && currentTagResult !== 'draft'"
                >
                  <template #title>
                    <span class="font-bold">
                      {{ transformI18n("leads." + actionItem) }}
                    </span>
                  </template>
                  <el-menu-item
                    v-for="(cancelChildItem, childIndex) in cancelChildren"
                    :index="`1-${actionIndex}-${childIndex}`"
                    :key="childIndex"
                    @click="handleAction(row, 'cancel', cancelChildItem.value)"
                    >{{ cancelChildItem.label }}</el-menu-item
                  >
                </el-sub-menu>
                <el-menu-item
                  :index="`1-${actionIndex}`"
                  v-else
                  :key="actionIndex"
                  @click="handleAction(row, actionItem)"
                  >{{ transformI18n("leads." + actionItem) }}</el-menu-item
                >
              </template>
            </el-sub-menu>
          </el-menu>
        </div>
      </template>
    </data-table>
    <ChangeResult ref="changeResult" />
    <ResultDialog ref="resultDialogRef" />
    <CreateAppointment ref="createAppointment" />
    <ConvertToOrderDialog ref="convertToOrderDialogRef" />
    <MoveToBlacklistDialog ref="moveToBlacklistDialogRef" />
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: 700;
}

.cascader-label {
  display: flex;
  align-items: center;

  .iconfont {
    margin-right: 5px;
    color: var(--el-color-primary);
  }
}

.result-container {
  :deep(.el-input__wrapper) {
    width: 140px;
    border: 1px solid #e9ebf0;
    border-radius: 8px;
  }
}

.table-draft-result {
  display: flex;
  align-items: center;
  padding-left: 10px;
}

:deep(.sub-product-select) {
  width: 310px !important;
}
</style>
