import { requiredField } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";

import blinds from "@/assets/image/blinds.png";
import garageDoor from "@/assets/image/garageDoor.png";
import landscaping from "@/assets/image/landscaping.png";
import roof from "@/assets/image/roof.png";
import patio from "@/assets/image/patio.jpg";
export { roof, blinds, garageDoor, landscaping, patio };
export const indexDefaultQuery = {
  search: "",
  sort: ["-id"],
  filter: [],
  _with: [],
  withCount: []
};

// note: props are used to display columns and sort
export const columns: TableColumnList = [
  {
    label: "Leads ID",
    width: "100",
    prop: "id",
    fixed: true,
    sortable: true
  },
  {
    label: "Transno",
    prop: "transno",
    width: "120"
  },
  {
    label: "Client",
    prop: "client.given_name",
    slot: "client",
    width: "140",
    sortable: true
  },
  {
    label: "Phone",
    prop: "client.phone",
    width: "120",
    slot: "phone",
    sortable: true
  },
  {
    label: "Second Phone",
    prop: "client.sec_phone",
    width: "120",
    slot: "sec_phone"
  },
  {
    label: "Email",
    prop: "client.email",
    width: "140",
    slot: "email",
    sortable: true
  },
  {
    label: "Address",
    prop: "client.address",
    width: "200",
    slot: "address",
    sortable: true
  },
  {
    label: "Suburb",
    prop: "client.suburb",
    width: "120",
    slot: "suburb",
    sortable: true
  },
  {
    label: "Source",
    prop: "source.name",
    sortable: true,
    width: "110"
  },
  {
    label: "Spoken To",
    prop: "spokenTo.login_name",
    slot: "spokenTo",
    width: "160",
    sortable: true
  },
  {
    label: "Date of enquiry",
    prop: "enquiry_date",
    width: "130",
    slot: "enquiryDate",
    sortable: true
  },
  {
    label: "Category",
    prop: "category.name",
    slot: "category",
    width: "120",
    sortable: true
  },
  {
    label: "Product",
    prop: "product",
    slot: "product",
    minWidth: "120"
  },
  {
    label: "Sales Manager",
    prop: "sales.login_name",
    slot: "salesManager",
    width: "150",
    sortable: true
  },
  {
    label: "SalesConsultant",
    prop: "salesConsultant.login_name",
    slot: "salesConsultant",
    width: "160",
    sortable: true
  },
  {
    label: "Appt Setter",
    prop: "apptSetter.login_name",
    slot: "apptSetter",
    width: "150",
    sortable: true
  },
  {
    label: "Retail",
    prop: "retail",
    slot: "retail",
    width: "110",
    sortable: true
  },
  {
    label: "Sub Result",
    prop: "sub_result",
    slot: "subResult",
    width: "150",
    sortable: true
  },
  {
    label: "Quoted Amount",
    prop: "quoted",
    width: "140",
    slot: "quoted",
    sortable: true
  },
  {
    label: "Sold Amount",
    prop: "sold",
    width: "120",
    slot: "sold",
    sortable: true
  },
  {
    label: "Followup Time",
    prop: "followup_time",
    slot: "followup_time",
    width: "140",
    sortable: true
  },
  {
    label: "Appt Date",
    width: "120",
    prop: "appt_date",
    slot: "appt_date",
    sortable: true
  },
  {
    label: "Sold Date",
    prop: "sold_date",
    slot: "sold_date",
    width: "120",
    sortable: true
  },
  {
    label: "Leads Result",
    prop: "result.name",
    slot: "result",
    width: "160"
  },
  {
    label: "Order Result",
    prop: "order.result",
    slot: "order_result",
    width: "160"
  },
  {
    label: "From old",
    prop: "from_old",
    width: "120"
  },
  {
    label: "Update At",
    prop: "updated_at",
    width: "120",
    slot: "updated_at",
    sortable: true
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation",
    prop: "operation",
    align: "center"
  }
];

export const showFields = {
  draft: [
    "id",
    "client.given_name",
    "client.phone",
    "client.address",
    "client.suburb",
    "source.name",
    "spokenTo.login_name",
    "enquiry_date",
    "category.name",
    "product",
    "sales.login_name",
    "result.name",
    "operation"
  ],
  followup: [
    "id",
    "transno",
    "client.given_name",
    "client.phone",
    "client.address",
    "source.name",
    "category.name",
    "product",
    "sales_consultant.login_name",
    "apptSetter.login_name",
    "result.name",
    "sub_result",
    "followup_time",
    "operation"
  ],
  appointed: [
    "id",
    "transno",
    "client.given_name",
    "client.phone",
    "client.address",
    "source.name",
    "category.name",
    "product",
    "sales_consultant.login_name",
    "apptSetter.login_name",
    "result.name",
    "sub_result",
    "appt_date",
    "sold_date",
    "operation"
  ],
  quoted: [
    "id",
    "transno",
    "client.given_name",
    "client.phone",
    "client.address",
    "source.name",
    "category.name",
    "product",
    "sales_consultant.login_name",
    "apptSetter.login_name",
    "retail",
    "sub_result",
    "quoted",
    "result.name",
    "operation"
  ],
  sold: [
    "id",
    "transno",
    "client.given_name",
    "client.phone",
    "client.address",
    "source.name",
    "category.name",
    "product",
    "sales_consultant.login_name",
    "apptSetter.login_name",
    "retail",
    "sold",
    "result.name",
    "order.result",
    "appt_date",
    "sold_date",
    "operation"
  ],
  cancelled: [
    "id",
    "transno",
    "client.given_name",
    "client.phone",
    "client.address",
    "source.name",
    "spokenTo.login_name",
    "sales_consultant.login_name",
    "enquiry_date",
    "category.name",
    "product",
    "sales_consultant.login_name",
    "sub_result",
    "result.name",
    "operation"
  ],
  default: [
    "id",
    "transno",
    "client.given_name",
    "client.phone",
    "client.address",
    "client.suburb",
    "source.name",
    "spokenTo.login_name",
    "enquiry_date",
    "category.name",
    "product",
    "sales.login_name",
    "apptSetter.login_name",
    "result.name",
    "operation"
  ]
};

export const tableActionList = {
  draft: ["confirm", "edit", "cancel"],
  new: ["edit", "cancel", "createAppt", "addToBlacklist"],
  followup: ["edit", "cancel", "createAppt", "addToBlacklist"],
  sold: ["edit", "cancel", "createAppt", "addToBlacklist"],
  appointed: ["edit", "cancel", "addToBlacklist"],
  quoted: ["edit", "createAppt", "cancel", "addToBlacklist"],
  cancelled: ["addToBlacklist", "revoke"],
  default: ["edit", "cancel"]
};

export const detailActionList = {
  draft: ["cancel", "createWithSameClient"],
  new: ["cancel"],
  followup: ["cancel"],
  sold: ["cancel"],
  appointed: ["cancel"],
  quoted: ["cancel"],
  cancelled: [],
  default: ["cancel"]
};

export const REGEXP_EMAIL = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
export const REGEXP_PHONE = /^((04|02|03|07|08)\d{8}|(000|111)\d{7})$/;

export const rules = {
  enquiry_date: [requiredField(transformI18n("leads.dateOfEnquiry"))],
  given_name: [requiredField(transformI18n("client.givenName"))],
  phone: [
    {
      validator: (rule, value, callback) => {
        if (!REGEXP_PHONE.test(value) && value) {
          callback(new Error(transformI18n("client.phoneRuleReg")));
        } else {
          callback();
        }
      },
      trigger: "submit"
    }
  ],
  sec_phone: [
    {
      validator: (rule, value, callback) => {
        if (!REGEXP_PHONE.test(value) && value) {
          callback(new Error(transformI18n("client.phoneRuleReg")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  email: [
    {
      validator: (rule, value, callback) => {
        if (value) {
          if (!REGEXP_EMAIL.test(value)) {
            callback(new Error(transformI18n("client.emailRuleReg")));
          }
        }
        callback();
      },
      trigger: "blur"
    }
  ],
  source_id: [requiredField("the source!")]
};

export const allResults = [
  {
    value: "new",
    label: "New",
    icon: "new",
    disabled: true
  },
  {
    value: "followup",
    label: "Followup",
    icon: "followup",
    disabled: true,
    children: [
      {
        value: "Structure not ready",
        label: "Structure not ready"
      },
      {
        value: "Havent move in yet",
        label: "Havent move in yet"
      },
      {
        value: "Waiting on approval",
        label: "Waiting on approval"
      },
      {
        value: "NoAnswer",
        label: "NoAnswer"
      },
      {
        value: "Call back later",
        label: "Call back later"
      },
      {
        value: "Remote",
        label: "Remote"
      },
      {
        value: "TBRA",
        label: "TBRA"
      }
    ]
  },
  // {
  //   value: "appointed",
  //   label: "Appointed",
  //   icon: "appointed"
  // },
  {
    value: "quoted",
    label: "Quoted",
    icon: "quoted",
    disabled: true,
    children: [
      {
        value: "s01",
        label: "A:Single Party or not homeowner"
      },
      {
        value: "s02",
        label: "A:Unemployed"
      },
      {
        value: "s03",
        label: "A:insufficient time"
      },
      {
        value: "s04",
        label: "A:Out of scope"
      },
      {
        value: "s05",
        label: "A:Don’t have approval"
      },
      {
        value: "s1",
        label: "S:insufficient rapport"
      },
      {
        value: "s2",
        label: "S:Didn’t build urgency"
      },
      {
        value: "s3",
        label: "S:Won’t make decision"
      },
      {
        value: "s4",
        label: "S:Close on company"
      },
      {
        value: "s5",
        label: "S:Close on product"
      },
      {
        value: "s6",
        label: "S:insufficient value"
      }
    ]
  },
  {
    value: "sold",
    label: "Sold",
    orderPageLabel: "review",
    disabled: true,
    icon: "sold"
  }
  // {
  //   value: "cancelled",
  //   label: "Cancelled",
  //   icon: "cancelled",
  //   disabled: true,
  //   children: [
  //     {
  //       value: "With other company",
  //       label: "With other company"
  //     },
  //     {
  //       value: "No interested",
  //       label: "No interested"
  //     },
  //     {
  //       value: "Cant do job",
  //       label: "Cant do job"
  //     },
  //     {
  //       value: "Wants Ballpark",
  //       label: "Wants Ballpark"
  //     },
  //     {
  //       value: "Renting",
  //       label: "Renting"
  //     },
  //     {
  //       value: "Made by mistake",
  //       label: "Made by mistake"
  //     },
  //     {
  //       value: "Single party",
  //       label: "Single party"
  //     },
  //     {
  //       value: "Wrong product",
  //       label: "Wrong product"
  //     },
  //     {
  //       value: "Wrong number",
  //       label: "Wrong number"
  //     },
  //     {
  //       value: "Unemployed",
  //       label: "Unemployed"
  //     },
  //     {
  //       value: "On confirmation",
  //       label: "On confirmation"
  //     }
  //   ]
  // }
];

export const cancelChildren = [
  {
    value: "With other company",
    label: "With other company"
  },
  {
    value: "No interested",
    label: "No interested"
  },
  {
    value: "Cant do job",
    label: "Cant do job"
  },
  {
    value: "Wants Ballpark",
    label: "Wants Ballpark"
  },
  {
    value: "Renting",
    label: "Renting"
  },
  {
    value: "Made by mistake",
    label: "Made by mistake"
  },
  {
    value: "Single party",
    label: "Single party"
  },
  {
    value: "Wrong product",
    label: "Wrong product"
  },
  {
    value: "Wrong number",
    label: "Wrong number"
  },
  {
    value: "Unemployed",
    label: "Unemployed"
  },
  {
    value: "On confirmation",
    label: "On confirmation"
  },
  {
    value: "By company",
    label: "By company"
  },
  {
    value: "By customer",
    label: "By customer"
  },
  {
    value: "Others",
    label: "Others"
  },
  {
    value: "Out of Area",
    label: "Out of Area"
  }
];

export const clientTitleOption = [
  {
    value: "Miss",
    label: "Miss"
  },
  {
    value: "Ms",
    label: "Ms"
  },
  {
    value: "Mr",
    label: "Mr"
  },
  {
    value: "Mrs",
    label: "Mrs"
  }
];

export const selectResults = [
  {
    value: "new",
    label: "New"
  },
  {
    value: "followup",
    label: "Followup"
  },
  {
    value: "appointed",
    label: "Appointed"
  },
  {
    value: "quoted",
    label: "Quoted"
  },
  {
    value: "sold",
    label: "Sold"
  },
  {
    value: "cancelled",
    label: "Cancelled"
  }
];
