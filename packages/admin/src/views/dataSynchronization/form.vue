<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { requiredField } from "@/utils/form";
import { ImportExcel } from "@/components/Upload";
import { ElMessage } from "element-plus";
import {
  productCategoryControllerDataSynchronizationFromOld,
  productCategoryControllerDataSynchronizationToOld
} from "@/api/admin/product";
const actionOption = [
  { label: "Import From Old System", value: "import" },
  { label: "Export From New System", value: "export" }
];
const FileOption = [
  { label: "Blinds contacts", value: "blinds_contacts" },
  { label: "Blinds Sales", value: "blinds_sales" },
  { label: "Blinds ContProd", value: "blinds_contProd" },
  { label: "Blinds SalesTran", value: "blinds_salestran" },
  { label: "GDoor contacts", value: "gdoor_contacts" },
  { label: "GDoor Sales", value: "gdoor_sales" },
  { label: "GDoor ContProd", value: "gdoor_contProd" },
  { label: "GDoor SalesTran", value: "gdoor_salestran" },
  { label: "Lawn contacts", value: "lawn_contacts" },
  { label: "Lawn Sales", value: "lawn_sales" },
  { label: "Lawn ContProd", value: "lawn_contProd" },
  { label: "Lawn SalesTran", value: "lawn_salestran" },
  { label: "Roof contacts", value: "roof_contacts" },
  { label: "Roof Sales", value: "roof_sales" },
  { label: "Roof ContProd", value: "roof_contProd" },
  { label: "Roof SalesTran", value: "roof_salestran" }
];

const dialogVisible = ref(false);
const promise: any = {};
const form = reactive({
  action: "import",
  type: null
});
const formRef = ref(null);
const rules = {
  type: [requiredField("File Name")],
  action: [requiredField("File Name")]
};
const importExcelRef = ref(null);
const uploadLoading = ref(false);
const exportLoading = ref(false);

function exportToOld() {
  productCategoryControllerDataSynchronizationToOld(form.type)
    .then(res => {
      ElMessage.success(res.message);
      hide();
      promise.resolve();
    })
    .catch(_err => {
      ElMessage.error(_err.message || "Export failed.");
    });
}
// import file change
function importFromOld(fileInfo) {
  uploadLoading.value = true;
  const param = { ...fileInfo, type: form.type };
  productCategoryControllerDataSynchronizationFromOld(param)
    .then(res => {
      ElMessage.success(res.message);
      uploadLoading.value = false;
      hide();
      promise.resolve();
    })
    .catch(_err => {
      uploadLoading.value = false;
      const msg = _err.response?.data?.message || "Import failed.";
      ElMessage.error(msg);
    });
}

function show() {
  Object.assign(form, {
    action: "import",
    type: null
  });
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function hide() {
  dialogVisible.value = false;
}

onMounted(() => {});
defineExpose({ show });
</script>

<template>
  <el-dialog v-model="dialogVisible" title="Import & Export" width="38.75rem">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120"
      label-position="top"
      class="w-[99/100] main-content border-form p-[20px]"
    >
      <el-row class="mt-[20px]">
        <el-col :span="24" class="mt-[20px]">
          <el-form-item prop="action" label="Transfer Type">
            <el-select v-model="form.action" filterable class="w-full">
              <el-option
                v-for="item in actionOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="mt-[20px]">
          <el-form-item prop="type" label="Action Type">
            <el-select v-model="form.type" filterable class="w-full">
              <el-option
                v-for="item in FileOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <ImportExcel
          ref="importExcelRef"
          @fileChange="importFromOld"
          :loading="uploadLoading"
          :btnTxt="'Upload File'"
          :btnIcon="'just-icon-upload-line'"
          :btnType="'primary'"
          v-if="form.action == 'import'"
        />
        <el-button
          v-else
          class="!text-sm h-10"
          type="primary"
          :disabled="exportLoading"
          :loading="exportLoading"
          @click="exportToOld"
        >
          <span class="mr-2 iconfont just-icon-download-line" />
          Export File</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.help-block {
  font-size: 12px;
  color: #999;
}

.form-item-with {
  width: 70%;
}
</style>
