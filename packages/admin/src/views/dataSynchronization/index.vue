<script setup lang="ts">
import {
  importTasksControllerIndex,
  importTasksControllerDestroy
} from "@/api/admin/import-tasks";
import { columns, fileTypeOption, statusOption } from "./data";
import ImportForm from "./form.vue";
import { reactive, ref, onMounted } from "vue";
import DataTable from "@/components/DataTable";
import { transformI18n } from "@/plugins/i18n";
import { ElMessageBox, ElLoading } from "element-plus";

defineOptions({
  name: "Account"
});

const form = reactive({});
const formRef = ref(null);
const dataTableRef = ref(null);
const dataFormRef = ref(null);
const mv = ref();
const dialogPanel = ref();
const indexQuery = reactive({
  search: "",
  sort: "-id",
  filter: "",
  _with: "",
  withCount: ""
});

function onSearch() {
  loadData();
}

function onReset() {
  indexQuery.search = "";
  Object.keys(form).forEach(key => {
    switch (key) {
      default:
        form[key] = null;
        break;
    }
  });
  loadData();
}

function downFile(fileUrl) {
  window.open(fileUrl, "_blank");
}

function onImport() {
  dataFormRef.value.show().then(() => {
    console.log("-----");
    loadData();
  });
}

function onDelete(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>delete</strong> the <strong style='color:var(--el-color-primary)'>${row.id}</strong> log?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    importTasksControllerDestroy(row.id)
      .then(() => {
        dialogPanel.value.close();
        loadData();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

function loadData() {
  dataTableRef.value.loadData();
}

function tableQueryParams(form) {
  const filterInfo = [];
  if (form.file_type) {
    filterInfo.push(`file_type:eq:${form.file_type}`);
  }
  if (form.status) {
    filterInfo.push(`status:eq:${form.status}`);
  }
  return Object.assign(
    { ...indexQuery },
    { filter: filterInfo.length === 0 ? indexQuery.filter : filterInfo.join() }
  );
}

function hasDownFile(row) {
  if (
    row.file_type &&
    row.file_type.includes("export_") &&
    row.status == "completed" &&
    row.file_path
  ) {
    return true;
  } else {
    return false;
  }
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

onMounted(() => {});
</script>

<template>
  <el-card ref="mv" shadow="never">
    <template #header>
      <div class="card-header">
        <span class="font-medium"> Data Synchronization Log </span>
        <div class="page-action-box">
          <el-button type="primary" @click="onImport">
            <i class="mx-[5px] iconfont just-icon-export-import" />
            <span>Import & Export</span>
          </el-button>
        </div>
      </div>
    </template>
    <div class="mt-2 px-2 pb-2 bg-bg_color">
      <div class="flex justify-between w-full h-[50px] list-form">
        <el-form ref="formRef" :inline="true" :model="form">
          <el-form-item label="Action:" prop="file_type">
            <el-select
              v-model="form.file_type"
              @change="onSearch()"
              clearable
              filterable
            >
              <el-option
                v-for="item in fileTypeOption"
                :key="item"
                :label="transformI18n('dataSynchronizationType.' + item)"
                :value="item"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="Status:" prop="status">
            <el-select v-model="form.status" @change="onSearch()" clearable>
              <el-option v-for="item in statusOption" :key="item" :value="item">
                <span class="capitalize"> {{ item }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item class="action-box">
            <el-button type="primary" @click="onSearch()">{{
              transformI18n("buttons.hssearch")
            }}</el-button>
            <el-button @click="onReset()">{{
              transformI18n("buttons.hsReset")
            }}</el-button>
          </el-form-item>
        </el-form>
      </div>
      <data-table
        ref="dataTableRef"
        :columns="columns"
        :source="importTasksControllerIndex"
        :form="form"
        :slotNames="['operation', 'filePath', 'status', 'fileType']"
        :query-params="tableQueryParams"
        :header-cell-style="{
          background: 'var(--el-table-row-hover-bg-color)',
          color: 'var(--el-text-color-primary)'
        }"
      >
        <template #fileType="{ row }">
          <span v-if="row.file_type">{{
            transformI18n("dataSynchronizationType." + row.file_type)
          }}</span>
        </template>
        <template #filePath="{ row }">
          <el-link
            type="primary"
            @click="downFile(row.file_path)"
            v-if="hasDownFile(row)"
            >Down File</el-link
          >
        </template>
        <template #status="{ row }">
          <span class="capitalize">
            {{ row.status }}
          </span>
        </template>
        <template #operation="{ row }">
          <div>
            <el-dropdown trigger="click">
              <span class="iconfont just-icon-gengduo" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    class="text-[#2A2E34] text-base"
                    @click.stop="onDelete(row)"
                  >
                    <i
                      class="!text-xl text-[#9B3CE5] iconfont just-icon-cancelled"
                    />
                    {{ transformI18n("buttons.hsdelete") }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </data-table>
      <import-form ref="dataFormRef" />
    </div>
  </el-card>
</template>
