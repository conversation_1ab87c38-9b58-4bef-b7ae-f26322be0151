import { transformI18n } from "@/plugins/i18n";
export const indexDefaultQuery = {
  search: "",
  sort: ["-id"],
  filter: [],
  _with: [""],
  withCount: []
};

export const columns: TableColumnList = [
  {
    label: "ID",
    prop: "id",
    width: "80"
  },
  {
    label: "Action Type",
    prop: "file_type",
    width: "200",
    slot: "fileType"
  },
  {
    label: "Status",
    prop: "status",
    width: "120",
    slot: "status"
  },
  {
    label: "File Path",
    prop: "file_path",
    slot: "filePath",
    width: "100"
  },
  {
    label: "Message",
    prop: "error_message"
  },
  {
    label: "Created At",
    prop: "created_at",
    width: "160"
  },
  {
    label: "Updated At",
    prop: "updated_at",
    width: "160"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];

export const fileTypeOption = [
  "export_blinds_contacts",
  "export_blinds_sales",
  "export_blinds_contProd",
  "export_blinds_salestran",
  "export_gdoor_contacts",
  "export_gdoor_sales",
  "export_gdoor_contProd",
  "export_gdoor_salestran",
  "export_lawn_contacts",
  "export_lawn_sales",
  "export_lawn_contProd",
  "export_lawn_salestran",
  "export_roof_contacts",
  "export_roof_sales",
  "export_roof_contProd",
  "export_roof_salestran",
  "blinds_contacts",
  "blinds_sales",
  "blinds_contProd",
  "blinds_salestran",
  "gdoor_contacts",
  "gdoor_sales",
  "gdoor_contProd",
  "gdoor_salestran",
  "lawn_contacts",
  "lawn_sales",
  "lawn_contProd",
  "lawn_salestran",
  "roof_contacts",
  "roof_sales",
  "roof_contProd",
  "roof_salestran"
];
export const statusOption = ["failed", "pending", "completed"];
