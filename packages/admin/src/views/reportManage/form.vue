<script setup lang="ts">
import { reactive, ref } from "vue";
import { rules } from "./data";
import {
  reportControllerStore,
  reportControllerUpdate
} from "@/api/admin/report";
import { sendFormFilter } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const companyRegionList = ref([]);

const form = reactive({
  id: null,
  name: null,
  url: null,
  is_show: 0,
  company_region: null
});
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const isEdit = ref(false);

function show(data = null) {
  isEdit.value = !!data;
  if (isEdit.value) {
    Object.assign(form, data);
  } else {
    Object.keys(form).forEach(key => {
      form[key] = null;
      if (key == "company_region") {
        form[key] = userBaseInfo.value.isPlatformUser
          ? null
          : userBaseInfo.value.accountCompanyRegion;
      }
    });
  }
  companyRegionList.value = [];
  if (userBaseInfo.value.isPlatformUser) {
    companyRegionList.value.push({
      label: "All",
      value: "All"
    });
  }

  userBaseInfo.value.companyRegion.forEach(item => {
    companyRegionList.value.push(item);
  });
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function hide() {
  dialogVisible.value = false;
}

async function onSave() {
  await formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      const sendForm = sendFormFilter(form);
      (isEdit.value
        ? reportControllerUpdate(form.id, sendForm)
        : reportControllerStore(sendForm)
      )
        .then(() => {
          loading.value = false;
          hide();
          promise.resolve();
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
}

defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="
      isEdit
        ? transformI18n('report.editReport')
        : transformI18n('report.newReport')
    "
    width="38.75rem"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      class="w-[99/100] ml-7 mr-7"
    >
      <el-row class="mt-[20px]">
        <el-col
          v-if="userBaseInfo.isPlatformUser"
          class="!mb-0 mt-5 text-sm text-[#2A2E34]"
          :span="24"
        >
          Company
        </el-col>
        <el-col
          class="text-sm text-[#2A2E34]"
          v-if="userBaseInfo.isPlatformUser"
          :span="24"
        >
          <el-form-item prop="company_region">
            <el-select
              v-model="form.company_region"
              clearable
              :placeholder="'Company'"
            >
              <el-option
                v-for="item in companyRegionList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("report.name")
        }}</el-col>
        <el-col :span="24">
          <el-form-item prop="name">
            <el-input
              v-model="form.name"
              :placeholder="transformI18n('report.name')"
              autocomplete="off"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("report.url")
        }}</el-col>
        <el-col :span="24">
          <el-form-item prop="url">
            <el-input
              v-model="form.url"
              type="textarea"
              clearable
              :rows="2"
              :placeholder="transformI18n('report.url')"
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("report.isShow")
        }}</el-col>
        <el-col :span="24">
          <el-form-item prop="is_show" class="!border-0">
            <el-switch
              v-model="form.is_show"
              :activeValue="1"
              :inactiveValue="0"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide">{{
          transformI18n("buttons.hsCancel")
        }}</el-button>
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :loading="loading"
          :disabled="loading"
          @click="onSave"
          >{{ transformI18n("buttons.hssave") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.help-block {
  font-size: 12px;
  color: #999;
}

.form-item-with {
  width: 80%;
}
</style>
