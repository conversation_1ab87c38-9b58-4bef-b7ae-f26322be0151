/* eslint-disable no-useless-escape */
import { requiredField } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
export const indexDefaultQuery = {
  search: "",
  sort: ["-id"],
  filter: [],
  _with: [],
  withCount: []
};

export const columns: TableColumnList = [
  { label: transformI18n("report.name"), prop: "name" },
  { label: transformI18n("report.url"), slot: "url", minWidth: "120" },
  { label: "Status", slot: "show", minWidth: "120" },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];

export const platformColumns: TableColumnList = [
  { label: transformI18n("report.name"), prop: "name" },
  { label: transformI18n("report.url"), slot: "url", minWidth: "120" },
  { label: "Company", prop: "company_region" },
  { label: "Status", slot: "show", minWidth: "120" },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];
export const REGEXP_URL =
  /^(https?:\/\/)([\da-z\.-]+|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(?::\d+)?([\/?\w \.-]*)*/;
export const rules = {
  name: [requiredField(transformI18n("report.name"))],
  url: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          return true;
        } else if (!REGEXP_URL.test(value)) {
          callback(new Error(transformI18n("report.urlRuleReg")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  company_region: [requiredField("Company")]
};
