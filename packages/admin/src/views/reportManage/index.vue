<script setup lang="ts">
import {
  report<PERSON>ontroller<PERSON>ndex,
  report<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  reportControllerUpdate
} from "@/api/admin/report";
import { columns, platformColumns } from "./data";
import ReportForm from "./form.vue";
import { reactive, ref, onMounted } from "vue";
import DataTable from "@/components/DataTable";
import { Plus } from "@element-plus/icons-vue";
import { transformI18n } from "@/plugins/i18n";
import RiEdit2Line from "@iconify-icons/ri/edit-2-line";
import { ElMessageBox, ElLoading } from "element-plus";
import { hasAuth } from "@/router/utils";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const companyRegion = ref([]);
const companyRegionList = ref([]);

defineOptions({
  name: "Report"
});

const form = reactive({
  search: ""
});
const dataTableRef = ref(null);
const dataFormRef = ref(null);
const mv = ref();
const dialogPanel = ref();
const statusLoading = ref([]);
const indexQuery = reactive({
  search: "",
  sort: "id",
  filter: "",
  _with: "",
  withCount: ""
});

function onCreate() {
  dataFormRef.value.show().then(() => {
    loadData();
  });
}

function onEdit(row) {
  dataFormRef.value.show(row).then(() => {
    loadData();
  });
  // uploadFormRef.value
}

function onDelete(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>delete</strong> the <strong style='color:var(--el-color-primary)'>${row.name}</strong> report?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    reportControllerDestroy(row.id)
      .then(() => {
        dialogPanel.value.close();
        loadData();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

function loadData() {
  dataTableRef.value.loadData();
}

function tableQueryParams(form) {
  const filterInfo = [];
  let search = "";
  if (form.search) {
    search = `%${form.search}%`;
  }
  if (companyRegion.value.length) {
    filterInfo.push("company_region:in:" + companyRegion.value.join("|"));
  }
  return Object.assign(
    { ...indexQuery },
    { filter: filterInfo.length === 0 ? indexQuery.filter : filterInfo.join() },
    { search: search }
  );
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function changeStatus(row, index) {
  const is_show = row.is_show ? 1 : 0;
  statusLoading.value[index] = true;
  reportControllerUpdate(row.id, { is_show: is_show })
    .then(() => {
      statusLoading.value[index] = false;
    })
    .catch(() => {
      statusLoading.value[index] = false;
    });
}

function toUpdateListCount(newInfo) {
  statusLoading.value = Array(newInfo.pageSize).fill(false);
}

onMounted(() => {
  companyRegionList.value = [];
  if (userBaseInfo.value.isPlatformUser) {
    companyRegionList.value.push({
      label: "All",
      value: "All"
    });
  }
  userBaseInfo.value.companyRegion.forEach(item => {
    companyRegionList.value.push(item);
  });
});
</script>

<template>
  <el-card ref="mv" shadow="never">
    <template #header>
      <div class="card-header">
        <span class="font-medium">
          {{ transformI18n("menus.hasReportManage") }}
        </span>
        <div class="page-action-box">
          <el-select
            v-if="userBaseInfo.isPlatformUser"
            class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
            v-model="companyRegion"
            @change="loadData"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :placeholder="'Company'"
          >
            <el-option
              v-for="item in companyRegionList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            :icon="Plus"
            type="primary"
            v-auth="'addReportManager'"
            @click="onCreate"
            >{{ transformI18n("report.newReport") }}</el-button
          >
        </div>
      </div>
    </template>
    <div class="mt-2 px-2 pb-2 bg-bg_color">
      <data-table
        ref="dataTableRef"
        :columns="userBaseInfo.isPlatformUser ? platformColumns : columns"
        :source="reportControllerIndex"
        :form="form"
        :slotNames="['operation', 'url', 'show']"
        :query-params="tableQueryParams"
        :header-cell-style="{
          background: 'var(--el-table-row-hover-bg-color)',
          color: 'var(--el-text-color-primary)'
        }"
        @toPassNewInfo="toUpdateListCount"
      >
        <template #url="{ row }">
          {{ row.url }}
        </template>

        <template #show="{ row, $index }">
          <el-switch
            v-model="row.is_show"
            :activeValue="1"
            :inactiveValue="0"
            :loading="statusLoading[$index]"
            @change="changeStatus(row, $index)"
            size="small"
            :disabled="!hasAuth('editReportManager')"
          />
        </template>

        <template #operation="{ row }">
          <div>
            <el-dropdown
              trigger="click"
              v-auth="['editReportManager', 'delReportManager']"
            >
              <span class="iconfont just-icon-gengduo" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    class="text-[#2A2E34] text-base"
                    @click.stop="onEdit(row)"
                  >
                    <IconifyIconOffline
                      class="mr-[5px]"
                      width="20px"
                      height="20px"
                      color="#9B3CE5"
                      :icon="RiEdit2Line"
                    />
                    {{ transformI18n("buttons.hseditor") }}
                  </el-dropdown-item>
                  <el-dropdown-item
                    class="text-[#2A2E34] text-base"
                    @click.stop="onDelete(row)"
                  >
                    <i
                      class="!text-xl text-[#9B3CE5] iconfont just-icon-cancelled"
                    />
                    {{ transformI18n("buttons.hsdelete") }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </data-table>
      <report-form ref="dataFormRef" />
    </div>
  </el-card>
</template>
