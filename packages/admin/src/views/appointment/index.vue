<script setup lang="ts">
import { ref, onMounted, nextTick, reactive, watch, computed } from "vue";
import { Plus, Refresh } from "@element-plus/icons-vue";
import { transformI18n } from "@/plugins/i18n";
import CreateAppointment from "@/components/CreateAppointment/CreateAppointment.vue";
import Detail from "./detail.vue";
import LeadsDetail from "@/views/leads/leads-detail.vue";
import OrderDetail from "@/views/orders/detail.vue";
import ServiceDetail from "@/views/service/detail.vue";
// import ResultDialog from "@/components/ResultDialog";
import CirCleText from "@/components/CircleText";
import {
  appointmentControllerIndex,
  appointmentControllerConfirm,
  appointmentControllerRevokeCancel
} from "@/api/admin/appointment";
import { accountControllerIndex } from "@/api/admin/admin-account";
import CalendarListView from "./components/calendarListView.vue";
import cancelAppointmentDialog from "./components/cancelAppointmentDialog.vue";
import completeAppointmentDialog from "./components/completeAppointmentDialog.vue";
import { useCommonStoreHook } from "@/store/modules/common";
import FullCalendar from "@fullcalendar/vue3";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import interactionPlugin from "@fullcalendar/interaction";
import ChangeResult from "@/components/ChangeResult/ChangeResult.vue";
import {
  statusOption,
  viewTypeOption,
  statusColor,
  staffTypeOption,
  appointmentTypeText,
  timeSlots
} from "./data";
import { ArrowLeftBold, ArrowRightBold } from "@element-plus/icons-vue";
import MapView from "./components/mapView.vue";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween"; // 引入相关插件
dayjs.extend(isBetween);
import { ElMessage } from "element-plus";
import { hasAuth } from "@/router/utils";
import { useNav } from "@/layout/hooks/useNav";
import { storageLocal } from "@pureadmin/utils";
import { handleAddress } from "@/utils/common";

const companyRegion = ref([]);
const mapCompanyRegion = ref([]);

const MapMarksNum = ref(0);
const { isAppointmentUser, userBaseInfo } = useNav();
const appointmentStaffList = ref();
const loading = ref(false);
const activeName = ref("calendar");
const form = reactive({
  viewType: "dayGridMonth",
  type: [],
  status: "all",
  staff: [],
  product: [],
  date: null
});
const selectStatusColor = ref([]);
const mapViewRef = ref(null);
const getStaffOptionsLoading = ref(false);
const fullCalendarRef = ref(null);
let fullCalendarApi;
const changeTab = (_tab, _event) => {
  nextTick(() => {
    if (activeName.value === "map" && mapViewRef.value) {
      mapViewRef.value.initMap();
    } else if (activeName.value === "calendar") {
      viewTypeChange(form.viewType);
      getappointmentsList();
    }
  });
};
const cancelAppointmentDialogRef = ref(null);
const completeAppointmentDialogRef = ref(null);
const DetailRef = ref(null);
const appointmentsList = ref([]);
const eventsList = ref([]);
const createAppointmentRef = ref(null);
const showCalendar = ref(false);
const calendarOptions = ref({
  plugins: [
    dayGridPlugin,
    timeGridPlugin,
    listPlugin,
    interactionPlugin // needed for dateClick
  ],
  //   headerToolbar: {
  //     left: "prev,next today",
  //     center: "title",
  //     right: "dayGridMonth,timeGridWeek,listWeek"
  //     // right: "dayGridMonth,timeGridWeek,timeGridDay"
  //   },
  headerToolbar: false,
  initialView: form.viewType,
  initialDate: null, // 视图初始日期
  editable: false,
  selectable: false,
  selectMirror: false,
  weekends: true,
  firstDay: 1,
  // 是否显示全天插槽
  allDaySlot: false,
  // 时间轴间距
  slotMinTime: "07:00",
  slotMaxTime: "20:00",
  slotDuration: "00:30:00", //一格时间槽代表多长时间，默认00:30:00（30分钟）
  slotLabelInterval: "01:00:00", //日期视图左边那一列多长间隔显示一条日期文字(默认跟着slotDuration走的，可自定义)
  // defaultEventMinutes: 1, // 默认新创建日程事件的最小长度，单位分钟
  // 月视图，是否为指定周数高度，true 6周高度
  fixedWeekCount: false,
  // 与dayMaxEvents类似，区别为包括+more链接
  dayMaxEventRows: true,
  handleWindowResize: "true",
  selectOverlap: false, // 是否允许重叠
  slotEventOverlap: true, // 相同时间段的多个日程视觉上是否允许重叠，默认true允许
  eventOverlap: false, // 拖拽时是否重叠
  nowIndicator: true, // 当前的时间线显示,为true时当前小时那一格有个红线，并且有红三角
  // contentHeight: 100%,
  displayEventEnd: false,
  eventTimeFormat: {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true
  },
  // 是否显示周六周日
  //   dayMaxEvents: false,
  //   eventLimit: true,
  //   moreLinkContent: "+ 更多",
  //   displayEventTime: true, // 是否显示时间
  eventClick: handleEventClick,
  // 月视图，是否显示非本月日期
  showNonCurrentDates: true,
  events: eventsList.value // alternatively, use the `events` setting to fetch from a feed
  /* you can update a remote database when these fire:
        eventAdd:
        eventChange:
        eventRemove:
        eventsSet: handleEvents,
        */
});

// const currentEvents = ref([]);
const title = ref("");
const calendarListViewRef = ref(null);
const calendarListHeaderRef = ref(null);
const columnsData = {
  appTime: { label: "Time", prop: "appTime", width: "125px" },
  type: { label: "Type", prop: "type", width: "60px" },
  status: { label: "Status", prop: "status", width: "95px" },
  product: { label: "Product", prop: "product", width: "90px" },
  staff: { label: "Staff", prop: "staff", width: "130px" },
  client: { label: "Client", prop: "client", width: "160px" },
  relatedObject: {
    label: "ID",
    prop: "relatedObject",
    width: "110px"
  },
  result: { label: "Result", prop: "result", width: "140px" },
  phone: { label: "Phone", prop: "phone", width: "100px" },
  address: { label: "Appointment", prop: "address", width: "140px" },
  suburb: { label: "Suburb", prop: "suburb", width: "120px" },
  appSetter: { label: "Appt Setter", prop: "appSetter", width: "130px" },
  action: { label: "Action", prop: "action", width: "60px" }
};

const LeadsDetailRef = ref(null);
const orderDetailRef = ref(null);
const serviceDetailRef = ref(null);
const currentAppointmentId = ref(null);
const productCategoryList = ref([]);
const canAddAppointment = computed(() => {
  return (
    hasAuth("addAppointment") ||
    hasAuth("addLeadsAppointment") ||
    hasAuth("addOrderAppointment")
  );
});
const changeResultRef = ref(null);
watch(
  () => form,
  _value => {
    getappointmentsList();
  },
  {
    immediate: false,
    deep: true
  }
);
watch(
  () => title.value,
  _value => {
    if (_value) {
      parseDateRange(_value);
    }
  },
  {
    immediate: true,
    deep: true
  }
);

function onCreate() {
  // to create page
  createAppointmentRef.value.show().then(() => {
    getappointmentsList();
  });
}

function handleStatusChange(status) {
  const selectStatusColors = [];
  statusOption.map(item => {
    if (item.value === status) {
      selectStatusColors.push(item.color);
    }
  });
  selectStatusColor.value = selectStatusColors;
}

function handleEventClick(clickInfo) {
  if (form.viewType === "listDay") {
    return;
  }
  const { id } = clickInfo.event;
  const theOne = appointmentsList.value.find(item => item.id == id);
  toAppointmentDetail(theOne);
}

function toAppointmentDetail(value) {
  if (value) {
    currentAppointmentId.value = value.id;
    DetailRef.value.show(value).then(() => {
      currentAppointmentId.value = null;
    });
  }
}

function today() {
  if (!fullCalendarApi) {
    return;
  }
  fullCalendarApi.today();
  title.value = fullCalendarApi.view.title;
}

// function getCalendarList(_result) {
//   // this.calendarOptions.events.push(data);
// }
function getPrev() {
  if (!fullCalendarApi) {
    return;
  }
  fullCalendarApi.prev();
  title.value = fullCalendarApi.view.title;
}
function getNext() {
  if (!fullCalendarApi) {
    return;
  }
  fullCalendarApi.next();
  title.value = fullCalendarApi.view.title;
}

function viewTypeChange(value) {
  if (!fullCalendarApi) {
    return;
  }

  fullCalendarApi.changeView(value);
  title.value = fullCalendarApi.view.title;
  console.log(value);
  if (value == "listDay") {
    console.log(calendarListViewRef);
    setTimeout(() => {
      nextTick(() => {
        if (calendarListViewRef.value) {
          // calendarListViewRef.value.addEventListener("scroll", () => {
          //   calendarListHeaderRef.value.scrollLeft = calendarListViewRef.value.scrollLeft;
          // });

          // 监听第二个 div 的滚动事件
          calendarListHeaderRef.value.addEventListener("scroll", () => {
            calendarListViewRef.value.scrollLeft =
              calendarListHeaderRef.value.scrollLeft;
          });
        }
      });
    }, 100);
  }
}

function getappointmentsList() {
  // Cache filter info
  const filterParam = {
    form: form
  };
  storageLocal().setItem(`apptfilter_${activeName.value}`, filterParam);

  const filterInfo = [];
  if (form.status && form.status != "all") {
    filterInfo.push(`status:eq:${form.status}`);
  } else {
    filterInfo.push(`status:nin:cancel`);
  }
  if (form.date && form.date[1]) {
    filterInfo.push(`date:gte:${form.date[0]}`);
    filterInfo.push(`date:lte:${form.date[1]}`);
  }
  if (form.staff && form.staff.length) {
    const staffStr = form.staff.join("|");
    filterInfo.push(`user_id:in:${staffStr}`);
  }

  if (form.type && form.type.length) {
    const type = form.type.join("|");
    filterInfo.push(`type:in:${type}`);
  }
  if (companyRegion.value.length) {
    filterInfo.push("company_region:in:" + companyRegion.value.join("|"));
  }
  const filter = filterInfo.join(",");
  const search = "";
  const sort = "";
  const _with =
    "leads,client,assignTo,leads.contracts,leads.order,leads.cm,leads.apptSetter,leads.install,leads.serviceFile, service,leads.category";
  const withCount = "";
  const page = 1;
  const size = 999;
  let subCategoryIds = null;
  if (form.product) {
    subCategoryIds = form.product.join(",");
  }
  loading.value = true;
  appointmentControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    subCategoryIds
  )
    .then(res => {
      loading.value = false;
      appointmentsList.value = res.data || [];
      const events = [];
      appointmentsList.value.map(item => {
        const statusColorCurrent =
          statusColor[item.status] || statusColor["all"];
        const staff = item.assign_to ? item.assign_to.login_name : "";
        const staffColor = item.assign_to ? item.assign_to.color : "#EA4335";
        const timeColor = getTimeSlotColor(item.date);
        let hasAttachment = false;
        if (
          item.type == "install" &&
          item.leads.install &&
          item.leads.install.length
        ) {
          hasAttachment = true;
        } else if (
          item.type == "sale" &&
          item.leads.contracts &&
          item.leads.contracts.length
        ) {
          hasAttachment = true;
        } else if (item.type == "cm" && item.leads.cm && item.leads.cm.length) {
          hasAttachment = true;
        } else if (
          item.type == "service" &&
          item.leads.service_file &&
          item.leads.service_file.length
        ) {
          hasAttachment = true;
        }
        const suburb = handleAddress(item.address);
        const endTime = item.end_time
          ? dayjs(item.end_time)
          : dayjs(item.date, "YYYY-MM-DD HH:mm:ss").add(0.5, "hour");

        const end = endTime.format("YYYY-MM-DD HH:mm:ss");
        const endTxt = endTime.format("h:mm A");
        events.push({
          id: item.id,
          type: item.type,
          start: item.date,
          title: item.type,
          end: end,
          extendedProps: {
            timeColor,
            bgColor: statusColorCurrent.bgColor,
            color: statusColorCurrent.color,
            staff: staff,
            staffColor: staffColor,
            product: item.leads?.product,
            hasAttachment,
            suburb,
            endTxt,
            ...item
          }
        });
      });
      calendarOptions.value.events = events;
      // eventsList.value = events;
      // to update the detail data
      if (currentAppointmentId.value && DetailRef.value) {
        const theOne = appointmentsList.value.find(
          item => item.id == currentAppointmentId.value
        );
        if (theOne) {
          DetailRef.value.updateDetailInfo(theOne);
        }
      }
    })
    .catch(_err => {
      loading.value = false;
    });
}

function getTimeSlotColor(time) {
  time = dayjs(time).format("HH:mm");
  time = dayjs(time, "HH:mm");
  let color = "#000";
  for (const slot of timeSlots) {
    const startTime = dayjs(slot.start, "HH:mm");
    const endTime = dayjs(slot.end, "HH:mm");
    if (time.isBetween(startTime, endTime, null, "[] ")) {
      color = slot.color;
      break;
    }
  }
  return color;
}

function initData() {
  getStaffOptions();
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData.productCategoryList;
}
function getStaffOptions(query = "") {
  appointmentStaffList.value = [];
  let filter = "";
  filter +=
    "roles.name:in:sales_consultant_user|install_user|sale_manager|production_assistant | general_manager|super_admin";
  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  const size = 99;
  getStaffOptionsLoading.value = true;
  const search = query ? "%" + query + "%" : "";
  let companyRegionStr = null;
  if (companyRegion.value.length) {
    companyRegionStr = companyRegion.value.join(",");
  }
  accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    companyRegionStr
  )
    .then(res => {
      appointmentStaffList.value = res.data || [];
      getStaffOptionsLoading.value = false;
    })
    .catch(_error => {
      getStaffOptionsLoading.value = false;
    });
}

function toRefresh() {
  getappointmentsList();
}

function parseDateRange(_dateRangeStr) {
  // console.log("title", dateRangeStr);
  if (!fullCalendarApi) {
    return;
  }
  // const currentRange = fullCalendarApi.getCurrentData().dateProfile.renderRange;
  const currentRange = fullCalendarApi.currentData.dateProfile.activeRange;
  if (currentRange && currentRange.start && currentRange.end) {
    form.date = [
      dayjs(currentRange.start).format("YYYY/MM/DD 00:00:00"),
      dayjs(currentRange.end).format("YYYY/MM/DD 23:59:59")
    ];
  }
}

function detailAction(value) {
  const { type, item, viewType } = value;
  if (type == "edit") {
    const actionFromType = "appointment";
    createAppointmentRef.value.show(item, actionFromType).then(() => {
      getappointmentsList();
    });
  } else if (type == "cancel") {
    cancelAppointmentDialogRef.value.show(item).then(actionOver => {
      if (actionOver) {
        getappointmentsList();
      }
    });
  } else if (type == "viewObjectDetail") {
    if (viewType == "lead") {
      // to show leads detail
      LeadsDetailRef.value.show(item.object_id, "leads");
    } else if (viewType == "service") {
      // to show service detail
      const editPage = false;
      serviceDetailRef.value.show(item.service_id, editPage);
    } else if (viewType == "order") {
      // to show order detail
      const orderId = item.leads.order_id;
      if (orderId) {
        const editPage = false;
        orderDetailRef.value.show(orderId, editPage);
      } else {
        ElMessage.error("The order information fails to be viewed");
      }
    }
  } else if (type === "complete") {
    completeAppointmentDialogRef.value.show(item).then(actionOver => {
      if (actionOver) {
        DetailRef.value.hide();
        getappointmentsList();
      }
    });
  } else if (type === "confirm") {
    appointmentControllerConfirm(item.id).then(res => {
      if (res.success === false) {
        ElMessage.error(res.message);
      } else {
        ElMessage.success(res.message);
        toRefresh();
      }
    });
  } else if (type === "view") {
    const id = value?.item?.id;
    if (id) {
      const theOne = appointmentsList.value.find(item => item.id == id);
      toAppointmentDetail(theOne);
    }
  } else if (type === "revoke") {
    appointmentControllerRevokeCancel(item.id).then(() => {
      DetailRef.value.hide();
      toRefresh();
    });
  }
}

function changeMapMarksNum(total) {
  MapMarksNum.value = total;
}

function stopClick() {
  console.log("row click stop");
}

function changeCompany() {
  mapCompanyRegion.value = companyRegion.value;
  getappointmentsList();
}
// Change leads result
function handleLeadsResultChnage(changeData) {
  const { value, row } = changeData;
  const hasSonResult = ["followup", "quoted"];
  if (hasSonResult.includes(value[0]) && !value[1]) {
    return false;
  }
  const result = value[0];
  const subResult = value[1];
  const data = {
    id: row.id,
    type: "leads",
    result: result,
    sub_result: subResult,
    row
  };
  changeResultRef.value
    .show(data)
    .then(res => {
      if (res) {
        ElMessage.success("ChangeResult processed successfully.");
        toRefresh();
      }
    })
    .catch(() => {});
}

onMounted(() => {
  // get list filter info
  showCalendar.value = false;
  const lastFilterParam = storageLocal().getItem(
    `apptfilter_${activeName.value}`
  );
  if (lastFilterParam && lastFilterParam["form"]) {
    Object.assign(form, lastFilterParam["form"]);
  }
  calendarOptions.value.initialView = form.viewType;
  if (form.date && form.date.length) {
    calendarOptions.value.initialDate = dayjs(form.date[0]).format(
      "YYYY-MM-DD"
    );
  }
  showCalendar.value = true;
  initData();
  setTimeout(() => {
    nextTick(() => {
      if (fullCalendarRef.value) {
        fullCalendarApi = fullCalendarRef.value.getApi();
        if (fullCalendarApi) {
          title.value = fullCalendarApi.view.title;
        }
      }
    });
  }, 100);
});
</script>

<template>
  <el-card shadow="never">
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="changeTab">
      <el-tab-pane
        :label="transformI18n('menus.hsAppointment')"
        name="pageHeader"
        :disabled="true"
      />
      <el-tab-pane label="Calendar View" name="calendar">
        <div class="calendar-box pl-[10px] pr-[10px]" v-loading="loading">
          <div class="calendar-header">
            <div class="calendar-header-left mb-[10px]">
              <el-button key="plain" text @click="today">Today </el-button>
              <ArrowLeftBold
                class="w-[12px] mr-[30px] ml-[30px]"
                @click="getPrev"
              />
              <ArrowRightBold
                class="w-[12px] mr-[30px] ml-[30px]"
                @click="getNext"
              />
              <span class="title">{{ title }}</span>
            </div>
            <div class="calendar-header-right">
              <el-select
                v-model="form.viewType"
                @change="viewTypeChange"
                class="w-[100px] mr-[10px]"
              >
                <el-option
                  v-for="item in viewTypeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                v-model="form.status"
                placeholder="Status"
                class="w-[140px] mr-[10px]"
                @change="handleStatusChange"
              >
                <el-option
                  v-for="item in statusOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <div class="flex items-center">
                    <el-tag
                      :color="item.bgColor"
                      class="!w-[12px] !h-[12px] !border-0 mr-[8px] !p-0"
                    />
                    <span :style="{ color: item.color }">{{ item.label }}</span>
                  </div>
                </el-option>
              </el-select>
              <el-select
                v-model="form.type"
                placeholder="Type"
                class="min-w-[220px] mr-[5px]"
                clearable
                multiple
                filterable
                collapse-tags
                collapse-tags-tooltip
              >
                <el-option
                  v-for="item in staffTypeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                v-model="form.staff"
                filterable
                clearable
                multiple
                collapse-tags
                class="min-w-[220px] mr-[5px]"
                placeholder="Filter Staff"
                :loading="getStaffOptionsLoading"
                :remote-method="getStaffOptions"
                remote
              >
                <el-option
                  v-for="item in appointmentStaffList"
                  :key="item.id"
                  :label="item.login_name"
                  :value="item.id"
                >
                  <div class="flex items-center">
                    <CirCleText
                      :text="item.login_name"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="item.color"
                    />
                    <span>{{ item.login_name }}</span>
                  </div>
                </el-option>
              </el-select>
              <el-select
                v-model="form.product"
                placeholder="Filter Subproduct"
                clearable
                multiple
                collapse-tags
                collapse-tags-tooltip
                filterable
                class="min-w-[230px]"
              >
                <el-option-group
                  v-for="group in productCategoryList"
                  :key="group.name"
                  :label="group.name"
                >
                  <el-option
                    v-for="item in group.products"
                    :key="item.id"
                    :label="item.name"
                    :value="`${item.id}`"
                  />
                </el-option-group>
              </el-select>
            </div>
          </div>

          <!-- list header -->

          <!--  header over -->
          <FullCalendar
            class="app-calendar"
            :options="calendarOptions"
            ref="fullCalendarRef"
            v-if="showCalendar"
          >
            <template v-slot:dayHeaderContent="arg">
              <p
                :class="
                  form.viewType === 'listDay' ? 'calendar-list-header-text' : ''
                "
              >
                {{ arg.text }}
              </p>
              <div
                class="calendar-list-header flex items-center al h-full text-[12px]"
                v-if="form.viewType === 'listDay'"
                ref="calendarListHeaderRef"
              >
                <div
                  :class="
                    (name == 'address'
                      ? 'grow calendar-list-header-address'
                      : '') + ' item'
                  "
                  v-for="(name, index) in Object.keys(columnsData)"
                  :key="index"
                  :style="`width: ${columnsData[name].width}`"
                >
                  {{ columnsData[name].label }}
                </div>
              </div>
            </template>
            <template v-slot:eventContent="arg">
              <div v-if="form.viewType === 'listDay'">
                <CalendarListView
                  :eventArg="arg"
                  ref="calendarListViewRef"
                  @detailAction="detailAction"
                  @changeLeadsResult="handleLeadsResultChnage"
                />
              </div>

              <div
                v-else-if="form.viewType === 'timeGridDay'"
                class="day-event-box"
                :style="`background-color: ${arg.event.extendedProps.bgColor};border:1px solid ${arg.event.extendedProps.staffColor};border-left:4px solid ${arg.event.extendedProps.staffColor}`"
              >
                <div class="day-content-box">
                  <div class="flex flex-col justify-center items-start">
                    <span
                      class="time uppercase"
                      :style="`color: ${arg.event.extendedProps.color};`"
                      >{{ arg.timeText }}-{{
                        arg.event.extendedProps.endTxt
                      }}</span
                    >
                  </div>
                  <div class="event-left flex" @click.stop="stopClick()">
                    <span class="title"
                      >#{{ arg.event.extendedProps.object_id }}</span
                    >
                    <span
                      class="type"
                      v-if="appointmentTypeText[arg.event.extendedProps.type]"
                      >{{
                        appointmentTypeText[arg.event.extendedProps.type].label
                      }}</span
                    >
                    <span
                      class="iconbox"
                      v-if="arg.event.extendedProps.hasAttachment"
                      ><span class="iconfont just-icon-upload"
                    /></span>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="arg.event.extendedProps.product"
                      placement="left"
                      @click.stop=""
                      v-if="
                        arg.event.extendedProps &&
                        arg.event.extendedProps.product
                      "
                    >
                      <span class="product type">{{
                        arg.event.extendedProps.product
                      }}</span>
                    </el-tooltip>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="arg.event.extendedProps.suburb"
                      placement="left"
                      :disabled="arg.event.extendedProps.suburb.length < 16"
                      v-if="
                        arg.event.extendedProps &&
                        arg.event.extendedProps.suburb
                      "
                      @click.stop=""
                    >
                      <span class="product type suburb">{{
                        arg.event.extendedProps.suburb
                      }}</span>
                    </el-tooltip>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      @click.stop=""
                      :content="arg.event.extendedProps.appt_duration"
                      placement="left"
                      :disabled="
                        arg.event.extendedProps.appt_duration.length < 16
                      "
                      v-if="
                        arg.event.extendedProps &&
                        arg.event.extendedProps.appt_duration
                      "
                    >
                      <span class="product type suburb">{{
                        arg.event.extendedProps.appt_duration
                      }}</span>
                    </el-tooltip>
                    <CirCleText
                      class="circle-text"
                      :text="arg.event.extendedProps.staff"
                      :size="19"
                      :fontSize="'10px'"
                      :customBgColor="arg.event.extendedProps.staffColor"
                    />
                  </div>
                </div>
              </div>

              <!--default View -->
              <div
                v-else
                class="event-box"
                :style="`background-color: ${arg.event.extendedProps.bgColor};border-left:4px solid ${arg.event.extendedProps.staffColor}`"
              >
                <div class="event-left flex">
                  <span
                    class="time uppercase"
                    :style="`color: ${arg.event.extendedProps.color};`"
                    >{{ arg.timeText }}</span
                  >
                  <div class="flex justify-start line-content">
                    <span class="title"
                      >#{{ arg.event.extendedProps.object_id }}</span
                    >
                    <span
                      class="type"
                      v-if="appointmentTypeText[arg.event.extendedProps.type]"
                      >{{
                        appointmentTypeText[arg.event.extendedProps.type].label
                      }}</span
                    >
                    <span
                      class="iconbox"
                      v-if="arg.event.extendedProps.hasAttachment"
                      ><span class="iconfont just-icon-upload"
                    /></span>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="arg.event.extendedProps.product"
                      placement="left"
                      v-if="
                        arg.event.extendedProps &&
                        arg.event.extendedProps.product &&
                        form.viewType === 'dayGridWeek'
                      "
                    >
                      <span class="product type">{{
                        arg.event.extendedProps.product
                      }}</span>
                    </el-tooltip>
                  </div>
                </div>
                <div class="event-right">
                  <CirCleText
                    class="circle-text"
                    :text="arg.event.extendedProps.staff"
                    :size="19"
                    :fontSize="'10px'"
                    :customBgColor="arg.event.extendedProps.staffColor"
                  />
                </div>
              </div>
            </template>
          </FullCalendar>
        </div>
      </el-tab-pane>
      <el-tab-pane label="Map View" name="map">
        <template #label>
          <span class="custom-tabs-label">
            <span>Map View</span>
            <el-tag type="info" effect="light" round v-if="MapMarksNum > 0">
              {{ MapMarksNum }}
            </el-tag>
          </span>
        </template>
        <mapView
          ref="mapViewRef"
          @toAppointmentDetail="toAppointmentDetail"
          :productCategoryList="productCategoryList"
          :companyRegion="mapCompanyRegion"
          @updateMapMarksNum="changeMapMarksNum"
        />
      </el-tab-pane>
    </el-tabs>
    <div class="page-action-box">
      <el-select
        v-if="userBaseInfo.isPlatformUser"
        class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
        v-model="companyRegion"
        @change="changeCompany"
        multiple
        collapse-tags
        collapse-tags-tooltip
        :placeholder="'Company'"
      >
        <el-option
          v-for="item in userBaseInfo.companyRegion"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button :icon="Refresh" type="primary" @click="getappointmentsList" />
      <el-button
        :icon="Plus"
        type="primary"
        @click="onCreate"
        v-if="canAddAppointment && !isAppointmentUser"
      >
        {{ transformI18n("appointment.new") }}
      </el-button>
    </div>
    <Detail ref="DetailRef" @refresh="toRefresh" @detailAction="detailAction" />
    <CreateAppointment ref="createAppointmentRef" />
    <leads-detail ref="LeadsDetailRef" />
    <OrderDetail ref="orderDetailRef" />
    <ServiceDetail ref="serviceDetailRef" />
    <cancelAppointmentDialog ref="cancelAppointmentDialogRef" />
    <completeAppointmentDialog ref="completeAppointmentDialogRef" />
    <!-- change leads result confirm dialog -->
    <ChangeResult ref="changeResultRef" />
  </el-card>
</template>
<style lang="scss" scoped>
.demo-tabs {
  :deep(.el-tabs__nav) {
    align-items: center;
  }

  :deep(.el-tabs__header) {
    padding: 0 170px 0 15px;

    .el-tabs__item.is-disabled {
      height: 27px;
      padding: 0 20px 0 0;
      font-size: 18px;
      font-weight: 600;
      color: #2a2e34;
      cursor: none;
      border-right: 1px solid #e9ebf0;
    }
  }

  :deep(.fc-daygrid-dot-event:hover) {
    cursor: pointer;
    background-color: transparent;
  }
}

.calendar-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;

  .calendar-header-left {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 700;
    text-align: left;

    .el-button.is-text {
      font-size: 16px;
      font-weight: 700;
      text-align: left;
    }
  }

  .calendar-header-right {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
    max-width: 100%;

    .el-select {
      padding: 0 5px;
      margin-bottom: 10px;
      border: 1px solid #d0d5dd;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
    }
  }
}

.red {
  background: pink;
}

.green {
  background: green;
}

.event-box,
.day-event-box {
  display: flex;
  flex-grow: 1;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 4px 5px;
  margin: 0 4px;
  font-size: 10px;
  border-radius: 6px;

  // &:hover {
  //   border: 1px solid var(--el-color-primary);
  // }

  .event-left {
    display: flex;
    flex-grow: 1;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-right: 5px;

    .time {
      font-size: 12px;
      font-weight: 500;
    }

    .line-content {
      flex-wrap: wrap;
    }
  }

  .circle-text {
    margin: 3px 0;
  }

  .iconbox {
    padding: 0 3px;
    color: #656f7d;
    background: #fff;
    border-radius: 20px;

    .iconfont {
      font-size: 14px;
      color: var(--el-color-primary);
    }
  }

  .product {
    max-width: 50px;
    overflow: hidden;
    font-weight: bold;
    color: #656f7d;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .suburb {
    max-width: 100px;
  }

  .event-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .el-button {
      padding: 0;
      margin: 0 5px;
    }
  }

  .time {
    font-size: 12px;
    font-weight: 500;
  }

  .type,
  .title {
    padding: 2px 5px;
    color: #656f7d;
    background: #fff;
    border-radius: 20px;
  }
}

.app-calendar {
  max-height: calc(100vh - 145px);

  :deep(.fc-h-event) {
    background-color: transparent;
    border: none;
  }
}

// list view
.calendar-list-header {
  .el-table {
    font-size: 12px;
  }
}

.circle-text,
.title,
.type,
.time,
.iconbox {
  margin-left: 3px;
}
</style>
