<script setup lang="ts">
import { ref, onMounted, nextTick, reactive, watch } from "vue";
import { transformI18n } from "@/plugins/i18n";
import Detail from "./detail.vue";
import LeadsDetail from "@/views/leads/leads-detail.vue";
import OrderDetail from "@/views/orders/detail.vue";
import ServiceDetail from "@/views/service/detail.vue";
// import ResultDialog from "@/components/ResultDialog";
import CirCleText from "@/components/CircleText";
import { appointmentControllerIndex } from "@/api/admin/appointment";
import CalendarListView from "./components/calendarListView.vue";
import completeAppointmentDialog from "./components/completeAppointmentDialog.vue";
import { useCommonStoreHook } from "@/store/modules/common";
import FullCalendar from "@fullcalendar/vue3";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import interactionPlugin from "@fullcalendar/interaction";
import { handleAddress } from "@/utils/common";

import {
  statusOption,
  viewTypeOption,
  statusColor,
  staffTypeOption,
  appointmentTypeText,
  timeSlots
} from "./data";
import { ArrowLeftBold, ArrowRightBold } from "@element-plus/icons-vue";
import MapView from "./components/mapView.vue";
import dayjs from "dayjs";
import isBetween from "dayjs/plugin/isBetween"; // 引入相关插件
dayjs.extend(isBetween);
import { ElMessage } from "element-plus";
import { useNav } from "@/layout/hooks/useNav";
import MoreFilled from "@iconify-icons/ri/more-fill";
import Profile from "@/components/Profile/Profile.vue";
import changePassword from "@/components/ChangePassword/ChangePassword.vue";
import { storageLocal } from "@pureadmin/utils";
import ChangeResult from "@/components/ChangeResult/ChangeResult.vue";

const loading = ref(false);
const { logout, userAvatar, username } = useNav();
const activeName = ref("calendar");
const changePasswordDialog = ref();
const showCalendar = ref(false);
const form = reactive({
  viewType: "dayGridMonth",
  type: [],
  status: "all",
  staff: [],
  product: [],
  date: null
});
const selectStatusColor = ref([]);
const mapViewRef = ref(null);
const changeTab = (_tab, _event) => {
  nextTick(() => {
    if (activeName.value === "map" && mapViewRef.value) {
      mapViewRef.value.initMap();
    } else if (activeName.value === "calendar") {
      viewTypeChange(form.viewType);
      getappointmentsList();
    }
  });
};
const completeAppointmentDialogRef = ref(null);
const DetailRef = ref(null);
// const AppointmentFormRef = ref(null);
// const resultDialogRef = ref(null);
// const lastCreateInfo = ref(null);
const appointmentsList = ref([]);
const eventsList = ref([]);
const calendarOptions = ref({
  plugins: [
    dayGridPlugin,
    timeGridPlugin,
    listPlugin,
    interactionPlugin // needed for dateClick
  ],
  //   headerToolbar: {
  //     left: "prev,next today",
  //     center: "title",
  //     right: "dayGridMonth,timeGridWeek,listWeek"
  //     // right: "dayGridMonth,timeGridWeek,timeGridDay"
  //   },
  headerToolbar: false,
  initialDate: null,
  initialView: form.viewType,
  editable: false,
  selectable: false,
  selectMirror: false,
  weekends: true,
  firstDay: 1,
  // 是否显示全天插槽
  allDaySlot: false,
  // 时间轴间距
  slotMinTime: "07:00",
  slotMaxTime: "20:00",
  slotDuration: "00:30:00", //一格时间槽代表多长时间，默认00:30:00（30分钟）
  slotLabelInterval: "01:00:00", //日期视图左边那一列多长间隔显示一条日期文字(默认跟着slotDuration走的，可自定义)
  // defaultEventMinutes: 1, // 默认新创建日程事件的最小长度，单位分钟
  // 月视图，是否为指定周数高度，true 6周高度
  fixedWeekCount: false,
  // 与dayMaxEvents类似，区别为包括+more链接
  dayMaxEventRows: true,
  handleWindowResize: "true",
  selectOverlap: false, // 是否允许重叠
  slotEventOverlap: true, // 相同时间段的多个日程视觉上是否允许重叠，默认true允许
  eventOverlap: false, // 拖拽时是否重叠
  nowIndicator: true, // 当前的时间线显示,为true时当前小时那一格有个红线，并且有红三角
  // contentHeight: 100%,
  displayEventEnd: false,
  eventTimeFormat: {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true
  },
  // 是否显示周六周日
  //   dayMaxEvents: false,
  //   eventLimit: true,
  //   moreLinkContent: "+ 更多",
  //   displayEventTime: true, // 是否显示时间
  eventClick: handleEventClick,
  // 月视图，是否显示非本月日期
  showNonCurrentDates: true,
  events: eventsList.value // alternatively, use the `events` setting to fetch from a feed
  /* you can update a remote database when these fire:
        eventAdd:
        eventChange:
        eventRemove:
        eventsSet: handleEvents,
        */
});
const fullCalendarRef = ref(null);
let fullCalendarApi;
const ProfileDialogRef = ref(null);
const title = ref("");
const calendarListViewRef = ref(null);
const columnsData = {
  appTime: { label: "Time", prop: "appTime", width: "125px" },
  type: { label: "Type", prop: "type", width: "60px" },
  status: { label: "Status", prop: "status", width: "95px" },
  product: { label: "Product", prop: "product", width: "90px" },
  staff: { label: "Staff", prop: "staff", width: "130px" },
  client: { label: "Client", prop: "client", width: "160px" },
  relatedObject: {
    label: "ID",
    prop: "relatedObject",
    width: "110px"
  },
  result: { label: "Result", prop: "result", width: "140px" },
  phone: { label: "Phone", prop: "phone", width: "100px" },
  address: { label: "Address", prop: "address", width: "140px" },
  suburb: { label: "Suburb", prop: "suburb", width: "120px" },
  appSetter: { label: "Appt Setter", prop: "appSetter", width: "130px" },
  action: { label: "Action", prop: "action", width: "60px" }
};

const LeadsDetailRef = ref(null);
const serviceDetailRef = ref(null);
const orderDetailRef = ref(null);
const currentAppointmentId = ref(null);
const productCategoryList = ref([]);
const changeResultRef = ref(null);
watch(
  () => form,
  _value => {
    getappointmentsList();
  },
  {
    immediate: false,
    deep: true
  }
);
watch(
  () => title.value,
  _value => {
    if (_value) {
      parseDateRange(_value);
    }
  },
  {
    immediate: true,
    deep: true
  }
);

function handleStatusChange(status) {
  const selectStatusColors = [];
  statusOption.map(item => {
    if (item.value === status) {
      selectStatusColors.push(item.color);
    }
  });
  selectStatusColor.value = selectStatusColors;
}

function handleEventClick(clickInfo) {
  if (form.viewType === "listDay") {
    return;
  }
  const { id } = clickInfo.event;
  const theOne = appointmentsList.value.find(item => item.id == id);
  toAppointmentDetail(theOne);
}

function toAppointmentDetail(value) {
  if (value) {
    currentAppointmentId.value = value.id;
    DetailRef.value.show(value).then(() => {
      currentAppointmentId.value = null;
    });
  }
}

function today() {
  if (!fullCalendarApi) {
    return;
  }
  fullCalendarApi.today();
  title.value = fullCalendarApi.view.title;
}

// function getCalendarList(_result) {
//   // this.calendarOptions.events.push(data);
// }
function getPrev() {
  if (!fullCalendarApi) {
    return;
  }
  fullCalendarApi.prev();
  title.value = fullCalendarApi.view.title;
}
function getNext() {
  if (!fullCalendarApi) {
    return;
  }
  fullCalendarApi.next();
  title.value = fullCalendarApi.view.title;
}

function viewTypeChange(value) {
  if (!fullCalendarApi) {
    return;
  }
  fullCalendarApi.changeView(value);
  title.value = fullCalendarApi.view.title;
}

function getappointmentsList() {
  // Cache filter info
  const filterParam = {
    form: form
  };
  storageLocal().setItem(`apptfilter_${activeName.value}`, filterParam);

  const filterInfo = [];
  if (form.status && form.status != "all") {
    filterInfo.push(`status:eq:${form.status}`);
  } else {
    filterInfo.push(`status:nin:cancel`);
  }
  if (form.date && form.date[1]) {
    filterInfo.push(`date:gte:${form.date[0]}`);
    filterInfo.push(`date:lte:${form.date[1]}`);
  }

  // if (form.product) {
  //   const product = form.product.split("-");
  //   filterInfo.push(
  //     `leads.sub_category_ids:like:%${product[0]}%, leads.product_category_id:eq:${product[1]}`
  //   );
  // }

  if (form.type && form.type.length) {
    const type = form.type.join("|");
    filterInfo.push(`type:in:${type}`);
  }

  const filter = filterInfo.join(",");
  const search = "";
  const sort = "";
  const _with =
    "leads,client,assignTo,leads.contracts, leads.order, leads.cm, leads.install,leads.serviceFile, service,leads.category";
  const withCount = "";
  const page = 1;
  const size = 999;
  let subCategoryIds = null;
  if (form.product) {
    subCategoryIds = form.product.join(",");
  }
  loading.value = true;
  appointmentControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    subCategoryIds
  )
    .then(res => {
      loading.value = false;
      appointmentsList.value = res.data || [];
      const events = [];
      appointmentsList.value.map(item => {
        const statusColorCurrent =
          statusColor[item.status] || statusColor["all"];
        const staff = item.assign_to ? item.assign_to.login_name : "";
        const timeColor = getTimeSlotColor(item.date);
        const staffColor = item.assign_to ? item.assign_to.color : "#EA4335";
        let hasAttachment = false;
        if (
          item.type == "install" &&
          item.leads.install &&
          item.leads.install.length
        ) {
          hasAttachment = true;
        } else if (
          item.type == "sale" &&
          item.leads.contracts &&
          item.leads.contracts.length
        ) {
          hasAttachment = true;
        } else if (item.type == "cm" && item.leads.cm && item.leads.cm.length) {
          hasAttachment = true;
        }
        const suburb = handleAddress(item.address);
        const endTime = item.end_time
          ? dayjs(item.end_time)
          : dayjs(item.date, "YYYY-MM-DD HH:mm:ss").add(0.5, "hour");

        const end = endTime.format("YYYY-MM-DD HH:mm:ss");
        const endTxt = endTime.format("h:mm A");

        events.push({
          id: item.id,
          type: item.type,
          start: item.date,
          title: item.type,
          end,
          extendedProps: {
            timeColor,
            bgColor: statusColorCurrent.bgColor,
            color: statusColorCurrent.color,
            staff: staff,
            staffColor: staffColor,
            product: item.leads?.product,
            hasAttachment,
            suburb,
            endTxt,
            ...item
          }
        });
      });
      calendarOptions.value.events = events;
      eventsList.value = events;
      // to update the detail data
      if (currentAppointmentId.value && DetailRef.value) {
        const theOne = appointmentsList.value.find(
          item => item.id == currentAppointmentId.value
        );
        if (theOne) {
          DetailRef.value.updateDetailInfo(theOne);
        }
      }
    })
    .catch(_err => {
      loading.value = false;
    });
}

function getTimeSlotColor(time) {
  time = dayjs(time).format("HH:mm");
  time = dayjs(time, "HH:mm");
  let color = "#000";
  for (const slot of timeSlots) {
    const startTime = dayjs(slot.start, "HH:mm");
    const endTime = dayjs(slot.end, "HH:mm");
    if (time.isBetween(startTime, endTime, null, "[] ")) {
      color = slot.color;
      break;
    }
  }
  return color;
}

function initData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData.productCategoryList;
}

function toRefresh() {
  getappointmentsList();
}

function parseDateRange(_dateRangeStr) {
  if (!fullCalendarApi) {
    return;
  }
  // const currentRange = fullCalendarApi.getCurrentData().dateProfile.renderRange;
  const currentRange = fullCalendarApi.currentData.dateProfile.activeRange;
  if (currentRange && currentRange.start && currentRange.end) {
    form.date = [
      dayjs(currentRange.start).format("YYYY/MM/DD 00:00:00"),
      dayjs(currentRange.end).format("YYYY/MM/DD 23:59:59")
    ];
  }
}

function detailAction(value) {
  console.log(value);
  const { type, item, viewType } = value;
  if (type == "viewObjectDetail") {
    if (viewType == "lead") {
      LeadsDetailRef.value.show(item.object_id, "leads");
    } else if (viewType == "service") {
      // to show service detail
      const editPage = false;
      serviceDetailRef.value.show(item.service_id, editPage);
    } else if (viewType == "order") {
      const orderId = item.leads.order_id;
      if (orderId) {
        orderDetailRef.value.show(orderId, false);
      } else {
        ElMessage.error("The order information fails to be viewed");
      }
    }
  } else if (type === "complete") {
    completeAppointmentDialogRef.value.show(item).then(actionOver => {
      if (actionOver) {
        getappointmentsList();
      }
    });
  } else if (type === "view") {
    const id = value?.item?.id;
    if (id) {
      const theOne = appointmentsList.value.find(item => item.id == id);
      toAppointmentDetail(theOne);
    }
  }
}

function changePwd() {
  changePasswordDialog.value.show();
}

function viewProfile() {
  ProfileDialogRef.value.show();
}

// Change leads result
function handleLeadsResultChnage(changeData) {
  const { value, row } = changeData;
  // const hasSonResult = ["followup", "quoted", "cancelled"];
  // if (hasSonResult.includes(value[0]) && !value[1]) {
  //   return false;
  // }
  const result = value[0];
  const subResult = value[1];
  const data = {
    id: row.id,
    type: "leads",
    result: result,
    sub_result: subResult,
    row
  };
  changeResultRef.value
    .show(data)
    .then(res => {
      if (res) {
        ElMessage.success("ChangeResult processed successfully.");
        toRefresh();
      }
    })
    .catch(() => {});
}

onMounted(() => {
  // get list filter info
  showCalendar.value = false;
  const lastFilterParam = storageLocal().getItem(
    `apptfilter_${activeName.value}`
  );
  if (lastFilterParam && lastFilterParam["form"]) {
    Object.assign(form, lastFilterParam["form"]);
  }
  calendarOptions.value.initialView = form.viewType;
  if (form.date && form.date.length) {
    calendarOptions.value.initialDate = dayjs(form.date[0]).format(
      "YYYY-MM-DD"
    );
  }
  showCalendar.value = true;
  initData();
  setTimeout(() => {
    nextTick(() => {
      if (fullCalendarRef.value) {
        fullCalendarApi = fullCalendarRef.value.getApi();
        if (fullCalendarApi) {
          title.value = fullCalendarApi.view.title;
        }
      }
    });
  }, 100);
});
</script>

<template>
  <el-card class="h-full pb-12 !overflow-scroll" shadow="never">
    <div class="font-medium page-header">
      {{ transformI18n("menus.hsAppointment") }}
    </div>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="changeTab">
      <el-tab-pane label="Calendar View" name="calendar">
        <div class="calendar-box pl-[10px] pr-[10px]" v-loading="loading">
          <div class="calendar-header">
            <div class="calendar-header-left mb-[10px]">
              <el-button key="plain" text @click="today">Today </el-button>
              <ArrowLeftBold
                class="w-[12px] mr-[30px] ml-[30px]"
                @click="getPrev"
              />
              <ArrowRightBold
                class="w-[12px] mr-[30px] ml-[30px]"
                @click="getNext"
              />
              <span class="title">{{ title }}</span>
            </div>
            <div class="calendar-header-right">
              <el-select
                v-model="form.viewType"
                @change="viewTypeChange"
                class="w-[100px] mr-[10px]"
              >
                <el-option
                  v-for="item in viewTypeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-select
                v-model="form.status"
                placeholder="Status"
                class="w-[140px] mr-[10px]"
                @change="handleStatusChange"
              >
                <el-option
                  v-for="item in statusOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <div class="flex items-center">
                    <el-tag
                      :color="item.color"
                      class="!w-[12px] !h-[12px] !border-0 mr-[8px] !p-0"
                    />
                    <span :style="{ color: item.color }">{{ item.label }}</span>
                  </div>
                </el-option>
              </el-select>
              <el-select
                v-model="form.type"
                placeholder="Type"
                class="min-w-[220px] mr-[10px]"
                clearable
                multiple
                collapse-tags
                collapse-tags-tooltip
                filterable
              >
                <el-option
                  v-for="item in staffTypeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>

              <el-select
                v-model="form.product"
                placeholder="Filter Subproduct"
                clearable
                multiple
                collapse-tags
                collapse-tags-tooltip
                filterable
                class="min-w-[230px]"
              >
                <el-option-group
                  v-for="group in productCategoryList"
                  :key="group.name"
                  :label="group.name"
                >
                  <el-option
                    v-for="item in group.products"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-option-group>
              </el-select>
            </div>
          </div>

          <!-- list header -->
          <FullCalendar
            class="app-calendar"
            :options="calendarOptions"
            ref="fullCalendarRef"
            v-if="showCalendar"
          >
            <template v-slot:dayHeaderContent="arg">
              <p
                :class="
                  form.viewType === 'listDay' ? 'calendar-list-header-text' : ''
                "
              >
                {{ arg.text }}
              </p>
              <div
                class="calendar-list-header flex items-center al h-full text-[12px]"
                v-if="form.viewType === 'listDay'"
              >
                <div
                  :class="
                    (name == 'address'
                      ? 'grow calendar-list-header-address'
                      : '') + ' item'
                  "
                  v-for="(name, index) in Object.keys(columnsData)"
                  :key="index"
                  :style="`width: ${columnsData[name].width}`"
                >
                  {{ columnsData[name].label }}
                </div>
              </div>
            </template>
            <template v-slot:eventContent="arg">
              <div v-if="form.viewType === 'listDay'">
                <CalendarListView
                  :eventArg="arg"
                  ref="calendarListViewRef"
                  @detailAction="detailAction"
                  @changeLeadsResult="handleLeadsResultChnage"
                />
              </div>

              <div
                v-else-if="form.viewType === 'timeGridDay'"
                class="day-event-box"
                :style="`background-color: ${arg.event.extendedProps.bgColor};border:1px solid ${arg.event.extendedProps.staffColor};border-left:4px solid ${arg.event.extendedProps.staffColor}`"
              >
                <div class="day-content-box">
                  <div class="flex flex-col justify-center items-start">
                    <span
                      class="time uppercase"
                      :style="`color: ${arg.event.extendedProps.color};`"
                      >{{ arg.timeText }}-{{
                        arg.event.extendedProps.endTxt
                      }}</span
                    >
                  </div>
                  <div class="event-left flex">
                    <span class="title"
                      >#{{ arg.event.extendedProps.object_id }}</span
                    >
                    <span
                      class="type"
                      v-if="appointmentTypeText[arg.event.extendedProps.type]"
                      >{{
                        appointmentTypeText[arg.event.extendedProps.type].label
                      }}</span
                    >
                    <span
                      class="iconbox"
                      v-if="arg.event.extendedProps.hasAttachment"
                      ><span class="iconfont just-icon-upload"
                    /></span>
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="arg.event.extendedProps.product"
                      placement="left"
                      v-if="
                        arg.event.extendedProps &&
                        arg.event.extendedProps.product
                      "
                    >
                      <span class="product type">{{
                        arg.event.extendedProps.product
                      }}</span>
                    </el-tooltip>
                    <CirCleText
                      class="circle-text"
                      :text="arg.event.extendedProps.staff"
                      :size="19"
                      :fontSize="'10px'"
                      :customBgColor="arg.event.extendedProps.staffColor"
                    />
                  </div>
                </div>
              </div>

              <!--default View -->
              <div
                v-else
                class="event-box"
                :style="`background-color: ${arg.event.extendedProps.bgColor};border-left:4px solid ${arg.event.extendedProps.staffColor}`"
              >
                <div class="event-left flex">
                  <span
                    class="time uppercase"
                    :style="`color: ${arg.event.extendedProps.color};`"
                    >{{ arg.timeText }}</span
                  >
                  <div class="flex justify-start line-content">
                    <span class="title"
                      >#{{ arg.event.extendedProps.object_id }}</span
                    >
                    <span
                      class="type"
                      v-if="appointmentTypeText[arg.event.extendedProps.type]"
                      >{{
                        appointmentTypeText[arg.event.extendedProps.type].label
                      }}</span
                    >
                    <span
                      class="iconbox"
                      v-if="arg.event.extendedProps.hasAttachment"
                      ><span class="iconfont just-icon-upload"
                    /></span>
                  </div>
                </div>
                <div class="event-right">
                  <CirCleText
                    class="circle-text"
                    :text="arg.event.extendedProps.staff"
                    :size="19"
                    :fontSize="'10px'"
                    :customBgColor="arg.event.extendedProps.staffColor"
                  />
                </div>
              </div>
            </template>
          </FullCalendar>
        </div>
      </el-tab-pane>
      <el-tab-pane label="Map View" name="map">
        <mapView
          ref="mapViewRef"
          @toAppointmentDetail="toAppointmentDetail"
          :productCategoryList="productCategoryList"
        />
      </el-tab-pane>
    </el-tabs>
    <Detail ref="DetailRef" @refresh="toRefresh" @detailAction="detailAction" />
    <leads-detail ref="LeadsDetailRef" />
    <OrderDetail ref="orderDetailRef" />
    <ServiceDetail ref="serviceDetailRef" />
    <completeAppointmentDialog ref="completeAppointmentDialogRef" />
    <el-row class="user-info-item -ml-8 w-full">
      <span class="flex mr-12">
        <img :src="userAvatar" class="avatar-img" />
        <p v-if="username" class="dark:text-white">{{ username }}</p>
      </span>
      <el-dropdown trigger="click">
        <span class="arrow-down">
          <IconifyIconOffline :icon="MoreFilled" class="dark:text-white" />
        </span>
        <template #dropdown>
          <el-dropdown-menu class="logout">
            <el-dropdown-item @click="changePwd">
              {{ transformI18n("common.changePwd") }}
            </el-dropdown-item>
            <el-dropdown-item @click="viewProfile">
              {{ transformI18n("common.viewProfile") }}
            </el-dropdown-item>
            <el-dropdown-item @click="logout">
              {{ transformI18n("buttons.hsLoginOut") }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </el-row>
    <changePassword ref="changePasswordDialog" />
    <Profile ref="ProfileDialogRef" />
    <!-- change leads result confirm dialog -->
    <ChangeResult ref="changeResultRef" />
  </el-card>
</template>
<style lang="scss" scoped>
.demo-tabs {
  :deep(.el-tabs__header) {
    padding: 0 170px;
  }

  :deep(.fc-daygrid-dot-event:hover) {
    cursor: pointer;
    background-color: transparent;
  }
}

.calendar-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;

  .calendar-header-left {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 700;
    text-align: left;

    .el-button.is-text {
      font-size: 16px;
      font-weight: 700;
      text-align: left;
    }
  }

  .calendar-header-right {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-start;
    max-width: 100%;

    .el-select {
      padding: 0 5px;
      margin-bottom: 10px;
      border: 1px solid #d0d5dd;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
    }
  }
}

.red {
  background: pink;
}

.green {
  background: green;
}

.event-box,
.day-event-box {
  display: flex;
  flex-grow: 1;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 4px 5px;
  margin: 0 8px;
  font-size: 10px;
  border-radius: 6px;

  // &:hover {
  //   border: 1px solid var(--el-color-primary);
  // }

  .event-left {
    display: flex;
    flex-grow: 1;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-right: 5px;

    .time {
      font-size: 12px;
      font-weight: 500;
    }

    .line-content {
      flex-wrap: wrap;
    }
  }

  .circle-text {
    margin: 5px 0;
  }

  .iconbox {
    padding: 0 3px;
    color: #656f7d;
    background: #fff;
    border-radius: 20px;

    .iconfont {
      font-size: 14px;
      color: var(--el-color-primary);
    }
  }

  .product {
    max-width: 50px;
    overflow: hidden;
    font-weight: bold;
    color: #656f7d;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .suburb {
    max-width: 100px;
  }

  .event-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .el-button {
      padding: 0;
      margin: 0 5px;
    }
  }

  .time {
    font-size: 12px;
    font-weight: 500;
  }

  .type,
  .title {
    padding: 2px 5px;
    margin-top: 2px;
    color: #656f7d;
    background: #fff;
    border-radius: 20px;
  }
}

.app-calendar {
  max-height: calc(100vh - 145px);

  :deep(.fc-h-event) {
    background-color: transparent;
    border: none;
  }
}

.circle-text,
.title,
.type,
.time,
.iconbox {
  margin-left: 3px;
}

// list view
.calendar-list-header {
  .el-table {
    font-size: 12px;
  }
}

.user-info-item {
  position: absolute;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 44px;
  padding: 0 18px;
  margin-right: 0;
  margin-left: 0;
  background: #f7f8f9;
  border-top: 1px solid #e9ebf0;
}

.user-info-item .avatar-img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
  border-radius: 12px;
}
</style>
