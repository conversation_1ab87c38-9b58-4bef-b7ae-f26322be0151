import { allResults } from "@/views/leads/components/leads_data";
export const statusOption = [
  { label: "All Status", value: "all", color: "#9DA7B8", bgColor: "#9DA7B8" },
  {
    label: "Overdue",
    value: "overdue",
    color: "#EA4335",
    bgColor: "rgba(234, 67, 53, 0.2)"
  },
  {
    label: "Scheduled",
    value: "scheduled",
    color: "#7F34B9",
    bgColor: "#EEE7F7"
  },
  {
    label: "Confirmed",
    value: "confirmed",
    color: "#FFA800",
    bgColor: "#FFE49F"
  },
  // {
  //   label: "Received",
  //   value: "received",
  //   color: "#00C3ED",
  //   bgColor: "#DEF7FD"
  // },
  {
    label: "In progress",
    value: "inprogress",
    color: "#1A6FE6",
    bgColor: "rgba(26, 111, 230, 0.2)"
  },
  {
    label: "Completed",
    value: "completed",
    color: "#13D46C",
    bgColor: "rgba(19, 212, 108, 0.2)"
  },
  { label: "Cancel", value: "cancel", color: "#9DA7B8", bgColor: "#e8e8e8" }
];
export const viewTypeOption = [
  { label: "Month", value: "dayGridMonth" },
  { label: "Week", value: "dayGridWeek" },
  // { label: "custom", value: "custom" },
  { label: "Day", value: "timeGridDay" },
  { label: "List", value: "listDay" }
  // { label: "listDay", value: "listDay" }
];

export const statusColor = {
  completed: {
    color: "#13D46C",
    bgColor: "#D0F6E2",
    // bgColor: "rgba(19, 212, 108, 0.2)",
    label: "Completed"
  },
  scheduled: {
    color: "#7F34B9",
    // bgColor: "rgba(153, 55, 229, 0.1)",
    bgColor: "#EEE7F7",
    label: "Scheduled"
  },
  overdue: {
    color: "#EA4335",
    bgColor: "#FBD9D7",
    // bgColor: "rgba(234, 67, 53, 0.2)",
    label: "Overdue"
  },
  inprogress: {
    color: "#1A6FE6",
    bgColor: "#D1E2FA",
    // bgColor: "rgba(26, 111, 230, 0.2)",
    label: "In Progress"
  },
  cancel: {
    color: "#9DA7B8",
    bgColor: "#e8e8e8",
    label: "Cancel"
  },
  confirmed: {
    color: "#FFA800",
    bgColor: "#FFE49F",
    label: "Confirmed"
  },
  // received: {
  //   label: "Received",
  //   color: "#00C3ED",
  //   bgColor: "#DEF7FD"
  // },

  all: { color: "#9DA7B8", bgColor: "#344054", label: "All" }
};

export const appointmentTypeText = {
  sale: {
    value: "sale",
    label: "Sales",
    position: "Sales",
    filterRoleName: [
      "sales_consultant_user",
      "sale_manager",
      "general_manager",
      "super_admin"
    ]
  },
  cm: {
    value: "cm",
    label: "CM",
    detailLabel: "Check Measurers",
    position: "Check Measurer",
    filterRoleName: [
      "install_user",
      "production_assistant",
      "general_manager",
      "super_admin"
    ]
  },
  install: {
    value: "install",
    label: "Install",
    position: "Installer",
    filterRoleName: [
      "install_user",
      "production_assistant",
      "general_manager",
      "super_admin"
    ]
  },
  service: {
    value: "service",
    label: "Service",
    position: "Installer",
    filterRoleName: [
      "install_user",
      "production_assistant",
      "general_manager",
      "super_admin"
    ]
  },
  all: {
    filterRoleName: [
      "sales_consultant_user",
      "sale_manager",
      "install_user",
      "production_assistant",
      "general_manager",
      "super_admin"
    ]
  }
};

export const staffTypeOption = [
  // { label: "All", value: "all" },
  { label: "Sales", value: "sale" },
  { label: "CM", value: "cm" },
  { label: "Install", value: "install" },
  { label: "Service", value: "service" }
];
export const orderResult = [
  {
    value: "new_order",
    label: "New Order",
    icon: "new_order"
  },
  {
    value: "cm",
    label: "CM",
    icon: "cm"
  },
  {
    value: "cmReceived",
    label: "CM Received",
    icon: "cmReceived"
  },
  {
    value: "onProduction",
    label: "On Production",
    icon: "onProduction"
  },
  {
    value: "ready",
    label: "Ready",
    icon: "ready"
  },
  {
    value: "installation",
    label: "Installation",
    icon: "installation"
  },
  {
    value: "outstanding",
    label: "Outstanding",
    icon: "outstanding"
  },
  {
    value: "completion",
    label: "Completion",
    icon: "completion"
  },
  {
    value: "hold",
    label: "Hold",
    icon: "hold"
  }
];
export const appointmentUseResult = [
  {
    value: "appointed",
    label: "Appointed",
    icon: "appointed"
  },
  ...allResults,
  ...orderResult
];

export const timeSlots = [
  { start: "08:00", end: "10:00", color: "red" },
  { start: "10:00", end: "12:00", color: "green" },
  { start: "12:00", end: "15:00", color: "blue" },
  { start: "15:00", end: "18:00", color: "orange" }
];
