<script lang="ts" setup>
import { ref, reactive, onMounted, computed } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { statusColor, appointmentUseResult, appointmentTypeText } from "./data";
import CirCleText from "@/components/CircleText";
import { accountControllerIndex } from "@/api/admin/admin-account";
import {
  appointmentControllerAssign,
  appointmentControllerNotifyClient,
  appointmentControllerSendToGoogleCalendar,
  appointmentControllerNotifyAssign,
  appointmentControllerAssignAccept,
  appointmentControllerSyncCalendar
} from "@/api/admin/appointment";
import { googleControllerSaveGoogleAccession } from "@/api/admin/google";
import { ElMessage, ElLoading, ElMessageBox } from "element-plus";
import { UploadFile } from "@/components/Upload";
import EmptySpan from "@/components/EmptySpan";
import { hasAuth } from "@/router/utils";
import { useNav } from "@/layout/hooks/useNav";
import {
  useTokenClient,
  type AuthCodeFlowSuccessResponse,
  type AuthCodeFlowErrorResponse
} from "vue3-google-signin";
import {
  uploadControllerDestroy,
  uploadControllerShow
} from "@/api/admin/basic-data";
import { notesControllerList } from "@/api/admin/note";
import { Refresh } from "@element-plus/icons-vue";
const dialogPanel = ref();
const mv = ref();
const handleOnSuccess = (response: AuthCodeFlowSuccessResponse) => {
  googleControllerSaveGoogleAccession(
    { access_token: response.access_token },
    { withCredentials: true }
  ).then(_res => {
    doSendCalender(false);
  });
};

const handleOnError = (errorResponse: AuthCodeFlowErrorResponse) => {
  console.log("Error: ", errorResponse);
};

const { login } = useTokenClient({
  onSuccess: handleOnSuccess,
  onError: handleOnError,
  scope:
    "https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/calendar.events https://www.googleapis.com/auth/calendar.calendarlist"

  // other options
});
const { isAppointmentUser, username, userBaseInfo } = useNav();
const uploadParam = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const canEditStaff = computed(() => {
  return hasAuth("assignAppointment");
});
const form = reactive({
  id: null,
  comment: null,
  address: null,
  date: null,
  end_time: null,
  latitude: null,
  longitude: null,
  type: null,
  status: null,
  name: null,
  phone: null,
  email: null,
  staffType: null,
  is_notify: 0,
  is_send: 0,
  user_id: null,
  assign_to: null,
  client: null,
  client_id: null,
  leads: null,
  service: null,
  object_id: null,
  is_assign_received: null,
  company_region: null,
  event_id: null
});
const loading = ref(false);
const notifyLoading = ref(false);
const notifyAssignLoading = ref(false);
const sendToMyCalenderLoading = ref(false);
const toReceivedLoading = ref(false);
const getStaffOptionsLoading = ref(false);
const assignToLoading = ref(false);
const staffOptions = ref([]);
const noteList = ref(null);
const noteLoading = ref(false);
const hideDetailBtn = ref(false);
const emit = defineEmits<{
  (e: "refresh", val: any): void;
  (e: "detailAction", val: Object): void;
}>();

const canActionPink = computed(() => {
  if (form.status == "cancel" || form.status == "completed") {
    return false;
  }
  if (form.type == "sale") {
    if (form.assign_to && username.value == form.assign_to.login_name) {
      return true;
    }
  } else if (userBaseInfo?.value?.role) {
    const roles = userBaseInfo.value.role;
    if (
      roles.indexOf("super_admin") !== -1 ||
      roles.indexOf("production_assistant") !== -1 ||
      roles.indexOf("general_manager") !== -1
    ) {
      return true;
    }
    if (
      roles.indexOf("install_user") !== -1 &&
      form.assign_to &&
      username.value == form.assign_to.login_name
    ) {
      return true;
    }
  }
  return false;
});

const canUpload = computed(() => {
  if (
    hasAuth("uploadFileForLeads") ||
    hasAuth("uploadFileForService") ||
    hasAuth("uploadFileForOrder") ||
    (form.assign_to && username.value == form.assign_to.login_name)
  ) {
    return true;
  }
  return false;
});

const canComplete = computed(() => {
  if (form.status == "cancel" || form.status == "completed") {
    return false;
  }
  if (hasAuth("completeAppointment")) {
    return true;
  }
  // if (form.type == "sale") {
  //   return isSaleManager.value;
  // } else
  if (userBaseInfo?.value?.role) {
    const roles = userBaseInfo.value.role;
    //   if (
    //     roles.indexOf("super_admin") !== -1 ||
    //     roles.indexOf("production_assistant") !== -1 ||
    //     roles.indexOf("general_manager") !== -1
    //   ) {
    //     return true;
    //   }
    if (
      roles.indexOf("install_user") !== -1 &&
      form.assign_to &&
      username.value == form.assign_to.login_name
    ) {
      return true;
    }
  }
  return false;
});

function show(data = null, hideDetail = false) {
  if (!data) {
    return;
  }
  hideDetailBtn.value = hideDetail;
  if (data) {
    Object.assign(form, data);
    let fileType = "contract";
    if (form.type == "cm") {
      fileType = "cm";
    } else if (form.type == "install") {
      fileType = "install";
    } else if (form.type == "service") {
      fileType = "service";
    } else if (form.type == "sale") {
      getNotes();
    }
    uploadParam.value = {
      object_id: form.object_id,
      type: fileType
    };
  } else {
    Object.keys(form).forEach(key => {
      form[key] = null;
    });
  }
  getStaffOptions();
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function getNotes() {
  if (!form.leads.id) {
    return;
  }
  loading.value = true;
  const param = {
    id: form.leads.id,
    type: "leads",
    sort: "-id",
    filter: "type:eq:comment",
    with: ""
  };

  notesControllerList(
    param.type,
    param.id,
    param.filter,
    param.sort,
    param.with
  )
    .then(res => {
      noteLoading.value = false;
      noteList.value = res.data || [];
    })
    .catch(_err => {
      noteLoading.value = false;
    });
}

function hide() {
  dialogVisible.value = false;
}

function uploadFileChange(_res) {
  // do somethin about the upload contract file
  emit("refresh", {});
}

function notifyClient() {
  ElMessageBox.confirm(
    `Are you sure you want to send a notification to the Client?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    notifyLoading.value = true;
    appointmentControllerNotifyClient(form.id, {})
      .then(res => {
        if (res.success === false) {
          ElMessage.error(res.message);
        } else {
          ElMessage.success(res.message);
        }
        notifyLoading.value = false;
        emit("refresh", {});
      })
      .catch(() => {
        notifyLoading.value = false;
      });
  });
}

// get current result icon
function getCurrentResultIcon(result) {
  if (!result) {
    return;
  }
  return appointmentUseResult.find(item => item.value === result)?.icon;
}

function getStaffOptions(query = "") {
  staffOptions.value = [];
  let filter = "";
  if (appointmentTypeText[form.type]) {
    const roleNameListStr =
      appointmentTypeText[form.type].filterRoleName.join("|");
    filter += "roles.name:in:" + roleNameListStr;
  }
  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  const size = 99;
  getStaffOptionsLoading.value = true;
  const search = query ? "%" + query + "%" : "";
  accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    form.company_region
  )
    .then(res => {
      const { data } = res;
      staffOptions.value = data || [];
      getStaffOptionsLoading.value = false;
      if (form.assign_to) {
        const findOne = staffOptions.value.find(
          item => item.id === form.assign_to.id
        );
        if (!findOne) {
          staffOptions.value.push(form.assign_to);
        }
      }
    })
    .catch(_error => {
      getStaffOptionsLoading.value = false;
    });
}
// function sendToMyCalender() {
//   ElMessageBox.confirm(
//     `Are you sure you want to add a calender to the your google Calender?`,
//     transformI18n("buttons.hsSystemPrompts"),
//     {
//       confirmButtonText: transformI18n("buttons.hsConfirm"),
//       cancelButtonText: transformI18n("buttons.hsCancel"),
//       type: "warning",
//       dangerouslyUseHTMLString: true,
//       draggable: true
//     }
//   ).then(() => {
//     doSendCalender();
//   });
// }

function doSendCalender(reLogin = true) {
  sendToMyCalenderLoading.value = true;
  appointmentControllerSendToGoogleCalendar(
    { appointment_id: form.id },
    { withCredentials: true }
  )
    .then(res => {
      if (res.success === false) {
        ElMessage.error(res.message);
      } else {
        ElMessage.success(res.message);
      }
      sendToMyCalenderLoading.value = false;
    })
    .catch(_error => {
      sendToMyCalenderLoading.value = false;
      if (reLogin) {
        login();
      }
    });
}

function assignTo() {
  assignToLoading.value = true;
  appointmentControllerAssign(form.id, { user_id: form.user_id }).then(res => {
    assignToLoading.value = false;
    if (res?.success) {
      ElMessage.success(res.message);
      if (form.user_id) {
        form.assign_to = staffOptions.value.find(
          item => item.id === form.user_id
        );
      } else {
        form.user_id = null;
        form.assign_to = null;
      }
      form.status = "scheduled";
      emit("refresh", "");
    } else {
      form.user_id = null;
      form.assign_to = null;
      ElMessage.error(res.message);
      emit("refresh", "");
    }
  });
}

function syncGoogleCalender() {
  appointmentControllerSyncCalendar(form.id).then(res => {
    if (res?.success === false) {
      ElMessage.error(res?.message);
    } else {
      ElMessage.success(res?.message);
    }
    emit("refresh", "");
  });
}

function notifyAssign() {
  notifyAssignLoading.value = true;
  appointmentControllerNotifyAssign(form.id, {})
    .then(res => {
      if (res.success === false) {
        ElMessage.error(res.message);
      } else {
        form.status = "scheduled";
        form.is_assign_received = false;
        ElMessage.success(res.message);
      }
      notifyAssignLoading.value = false;
      emit("refresh", {});
    })
    .catch(() => {
      notifyAssignLoading.value = false;
    });
}

function handleAction(item, type, viewType = "lead") {
  emit("detailAction", { item, type, viewType });
}

// update current detail info
function updateDetailInfo(data) {
  Object.assign(form, data);
}

function detailClose() {
  dialogVisible.value = false;
  promise.resolve();
}
function completeAppointment() {
  if (form.type == "sale") {
    ElMessageBox.confirm(
      `Please make sure you have upload the contract file.`,
      transformI18n("buttons.hsSystemPrompts"),
      {
        confirmButtonText: transformI18n("buttons.hsConfirm"),
        cancelButtonText: transformI18n("buttons.hsCancel"),
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    ).then(() => {
      emit("detailAction", { item: form, type: "complete" });
    });
  } else if (form.type == "cm") {
    ElMessageBox.confirm(
      `Please make sure you have upload the cm file.`,
      transformI18n("buttons.hsSystemPrompts"),
      {
        confirmButtonText: transformI18n("buttons.hsConfirm"),
        cancelButtonText: transformI18n("buttons.hsCancel"),
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    ).then(() => {
      emit("detailAction", { item: form, type: "complete" });
    });
  } else {
    emit("detailAction", { item: form, type: "complete" });
  }
}

function toReceived() {
  toReceivedLoading.value = true;
  appointmentControllerAssignAccept(form.id).then(res => {
    toReceivedLoading.value = false;
    if (res.success === false) {
      ElMessage.error(res.message);
    } else {
      form.is_assign_received = true;
      ElMessage.success(res.message);
      emit("refresh", {});
    }
  });
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function previewFile(file) {
  const fileUrl = file.url;
  if (file.storage == "s3") {
    dialogLoading();
    uploadControllerShow(file.id)
      .then(res => {
        if (res.data.s3Url) {
          window.open(res.data.s3Url, "_blank");
        } else {
          window.open(res.data.url, "_blank");
        }
        dialogPanel.value.close();
      })
      .catch(_err => {
        dialogPanel.value.close();
      });
  } else {
    window.open(fileUrl, "_blank");
  }
}

// delete attachment
function delAttachment(file) {
  uploadControllerDestroy(file.id).then(res => {
    if (res.success) {
      ElMessage.success(res.message);
      emit("refresh", {});
    } else {
      ElMessage.error(res.message || "File deletion failure!");
    }
  });
}

onMounted(() => {});

defineExpose({ show, dialogVisible, updateDetailInfo, hide });
</script>
<template>
  <div>
    <el-drawer
      v-model="dialogVisible"
      title=""
      direction="rtl"
      @close="detailClose"
      ref="mv"
      class="!w-[400px] appointment-detail-drawer"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <div
            class="flex items-center text-xs"
            v-if="statusColor[form.status]"
          >
            <el-tag
              :color="statusColor[form.status].bgColor"
              class="!w-[12px] !h-[12px] mr-1 !p-[0] !border-0"
            />
            <span :style="{ color: statusColor[form.status].color }">{{
              statusColor[form.status].label
            }}</span>
          </div>

          <div
            class="flex items-center header-action"
            v-if="form.status !== 'cancel' && form.status != 'completed'"
          >
            <div
              class="mr-2 flex items-center btn"
              @click.stop="handleAction(form, 'confirm')"
              v-if="
                form.status !== 'cancel' &&
                form.status != 'completed' &&
                form.status != 'confirmed' &&
                username != form.assign_to?.login_name
              "
            >
              <span class="iconfont just-icon-appointed mr-1" />{{
                transformI18n("buttons.hsConfirm")
              }}
            </div>
            <div
              class="mr-2 flex items-center btn"
              @click.stop="handleAction(form, 'edit')"
              v-if="hasAuth('editAppointment') && !isAppointmentUser"
            >
              <span class="iconfont just-icon-pencil mr-1" />{{
                transformI18n("buttons.hseditor")
              }}
            </div>
            <div
              v-if="!isAppointmentUser && hasAuth('cancelAppointment')"
              style="opacity: 0.6"
              class="flex items-center btn"
              @click.stop="handleAction(form, 'cancel')"
            >
              <span class="iconfont just-icon-delete mr-1" />
              {{ transformI18n("buttons.hsCancel") }}
            </div>
          </div>
          <div
            v-if="
              !isAppointmentUser &&
              hasAuth('revokeAppointment') &&
              (form.status == 'cancel' || form.status == 'completed') &&
              form.type == 'sale' &&
              (form.leads.result == 'followup' ||
                form.leads.result == 'cancelled')
            "
            style="opacity: 0.6"
            class="flex items-center btn"
            @click.stop="handleAction(form, 'revoke')"
          >
            <span class="iconfont just-icon-revoke mr-1" />
            Revoke
          </div>
        </div>
      </template>
      <template #footer>
        <div class="flex flex-col footer-action">
          <div
            class="flex items-center justify-between mb-[8px]"
            v-if="canActionPink"
          >
            <el-button
              v-if="
                !form.event_id &&
                form.user_id &&
                form.leads &&
                (form.leads.product_category_id == 1 ||
                  form.leads.product_category_id == 2)
              "
              class="subBtn"
              type="primary"
              :disabled="sendToMyCalenderLoading"
              :loading="sendToMyCalenderLoading"
              @click="syncGoogleCalender"
            >
              <el-icon :size="15" class="mr-2">
                <Refresh />
              </el-icon>
              {{ transformI18n("appointment.sendToMyCalender") }}</el-button
            >
            <el-button
              class="subBtn"
              :class="
                !form.event_id &&
                form.user_id &&
                form.leads &&
                (form.leads.product_category_id == 1 ||
                  form.leads.product_category_id == 2)
                  ? ''
                  : 'w-100'
              "
              type="primary"
              :disabled="!!form.is_notify"
              :loading="notifyLoading"
              @click="notifyClient"
            >
              <span class="iconfont just-icon-notification mr-2" />
              {{ transformI18n("appointment.notifyClient") }}</el-button
            >
          </div>
          <UploadFile
            ref="uploadFileRef"
            :otherParam="uploadParam"
            @fileChange="uploadFileChange"
            :btnTxt="transformI18n('appointment.uploadAttachments')"
            :btnIcon="'just-icon-upload'"
            :btnClass="'subBtn'"
            class="w-[100%] mb-[8px]"
            v-if="canUpload"
          />

          <el-button
            class="w-[100%] mb-[8px] !ml-[0]"
            type="primary"
            @click="completeAppointment"
            v-if="canComplete"
          >
            <span
              class="iconfont just-icon-appointed mr-2"
              style="color: #fff"
            />
            {{ transformI18n("appointment.completeAppointment") }}</el-button
          >
        </div>
      </template>
      <div class="flex justify-between items-center">
        <span class="level-1">
          <p>{{ form.date }}</p>
          <p>{{ form.end_time }}</p>
        </span>
        <span>{{ appointmentTypeText[form.type].label }}</span>
      </div>
      <el-form
        ref="formRef"
        :model="form"
        label-width="120"
        class="w-[99/100]"
        label-position="top"
      >
        <div class="detail-content">
          <el-row :gutter="10">
            <el-col :span="24" class="item-box">
              <el-form-item :label="appointmentTypeText[form.type].position">
                <div class="flex items-center grow justify-between">
                  <div class="flex items-center">
                    <CirCleText
                      v-if="form.assign_to"
                      :text="form.assign_to.login_name"
                      :size="20"
                      :fontSize="'10px'"
                      class="mr-2"
                      :customBgColor="form.assign_to.color"
                    />
                    <el-select
                      class="!w-[100%]"
                      v-model="form.user_id"
                      @change="assignTo"
                      filterable
                      :placeholder="transformI18n('appointment.assignTo')"
                      :loading="getStaffOptionsLoading"
                      :remote-method="getStaffOptions"
                      remote
                      clearable
                      v-if="
                        canEditStaff &&
                        form.status !== 'cancel' &&
                        form.status != 'completed'
                      "
                    >
                      <el-option
                        v-for="item in staffOptions"
                        :key="item.id"
                        :label="item.login_name"
                        :value="item.id"
                      >
                        <div class="flex items-center">
                          <CirCleText
                            :text="item.login_name"
                            :size="20"
                            :fontSize="'10px'"
                            :customBgColor="item.color"
                          />
                          <span>{{ item.login_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                    <EmptySpan
                      class="text-[13px]"
                      :text="form.assign_to ? form.assign_to.login_name : ''"
                      v-else
                    />
                    <el-tooltip
                      effect="dark"
                      v-if="
                        form.assign_to && username != form.assign_to?.login_name
                      "
                      :content="
                        form.is_assign_received ? 'Received' : 'Not Received '
                      "
                      placement="top"
                    >
                      <el-checkbox
                        class="ml-10 readonlyCheckbox"
                        disabled
                        :checked="form.is_assign_received"
                      />
                    </el-tooltip>
                  </div>
                  <div
                    v-if="form.status != 'cancel' && form.status != 'completed'"
                  >
                    <el-button
                      type="primary"
                      link
                      class="ml-[10px]"
                      @click="toReceived"
                      :disabled="toReceivedLoading"
                      :loading="toReceivedLoading"
                      v-if="
                        !form.is_assign_received &&
                        form.assign_to &&
                        username == form.assign_to.login_name
                      "
                    >
                      <span
                        class="iconfont mr-1 just-icon-account"
                        v-if="!toReceivedLoading"
                      />
                      <span class="underline">Accept Task</span>
                    </el-button>
                    <el-button
                      link
                      type="primary"
                      :loading="notifyAssignLoading"
                      :disabled="notifyAssignLoading"
                      @click="notifyAssign"
                      v-else-if="
                        form.assign_to && username != form.assign_to.login_name
                      "
                    >
                      <span
                        class="iconfont just-icon-notification mr-2"
                        v-if="!notifyAssignLoading"
                      />
                      <span class="underline">{{
                        transformI18n("appointment.notifyAssign")
                      }}</span>
                    </el-button>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12" class="item-box">
              <el-form-item :label="transformI18n('appointment.relatedObject')">
                <div class="flex items-center justify-between grow">
                  <span>{{ form.object_id }}</span>
                  <div v-if="!hideDetailBtn">
                    <span
                      v-if="form.leads"
                      :class="
                        form.leads && form.leads.order_id
                          ? 'view-detail-item view-leads'
                          : 'view-detail-item'
                      "
                      @click="handleAction(form, 'viewObjectDetail')"
                    >
                      Lead
                    </span>
                    <span
                      v-if="form.leads && form.leads.order_id"
                      class="view-detail-item"
                      @click="handleAction(form, 'viewObjectDetail', 'order')"
                    >
                      Order
                    </span>
                    <span
                      v-if="form.service && form.service_id"
                      class="view-detail-item"
                      @click="handleAction(form, 'viewObjectDetail', 'service')"
                    >
                      Service
                    </span>
                  </div>
                  <!-- <span
                    class="iconfont mr-2 just-icon-link-details primary-color"
                  /> -->
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12" class="item-box">
              <el-form-item :label="transformI18n('common.result')">
                <div class="item-value level-3" v-if="form.service">
                  <span
                    :class="
                      `iconfont mr-2 just-icon-` +
                      getCurrentResultIcon(form.service.result)
                    "
                  />
                  <!-- todo order result -->
                  <span class="capitalize"> {{ form.service.result }}</span>
                </div>
                <div class="item-value level-3" v-else-if="form.leads">
                  <span
                    :class="
                      `iconfont mr-2 just-icon-` +
                      getCurrentResultIcon(form.leads.result)
                    "
                  />
                  <!-- todo order result -->
                  <span class="capitalize"> {{ form.leads.result }}</span>
                </div>
                <span v-else class="empty"> Empty </span>
              </el-form-item>
            </el-col>
            <el-col :span="12" class="item-box">
              <el-form-item :label="transformI18n('client.client')">
                <div class="item-value level-3" v-if="form.client">
                  {{ form.client.title }} {{ form.client.surname }}
                  {{ form.client.given_name }}
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="12" class="item-box">
              <el-form-item :label="transformI18n('appointment.clientPhone')">
                <div class="item-value level-3">
                  <span v-if="form.client">
                    {{ form.client.phone }}
                  </span>
                  <span v-else class="empty"> Empty </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col
              :span="12"
              class="item-box"
              v-if="
                form.client && form.type == 'sale' && form.client.sec_given_name
              "
            >
              <el-form-item label="Secondary Contact">
                <div class="item-value level-3">
                  {{ form.client.sec_title }} {{ form.client.sec_given_name }}
                </div>
              </el-form-item>
            </el-col>

            <el-col
              :span="12"
              class="item-box"
              v-if="form.client && form.type == 'sale' && form.client.sec_phone"
            >
              <el-form-item label="Secondary Contact Phone">
                <div class="item-value level-3">
                  <span>
                    {{ form.client.sec_phone }}
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24" class="item-box">
              <el-form-item :label="transformI18n('client.address')">
                <div class="item-value level-3">
                  <span v-if="form.address">
                    {{ form.address }}
                  </span>
                  <span v-else class="empty"> Empty </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24" class="item-box">
              <el-form-item :label="'Product Detail'">
                <div class="item-value level-3">
                  <span v-if="form.leads && form.leads.comment">
                    {{ form.leads.comment }}
                  </span>
                  <span v-else class="empty"> Empty </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12" class="item-box">
              <el-form-item :label="transformI18n('productSpec.category')">
                <div class="item-value level-3">
                  <span
                    v-if="
                      form.leads &&
                      form.leads.category &&
                      form.leads.category.name
                    "
                  >
                    {{ form.leads.category.name }}
                  </span>
                  <span v-else class="empty"> Empty </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col
              v-if="
                form.leads && form.leads.order && form.leads.order.to_be_collect
              "
              :span="12"
              class="item-box"
            >
              <el-form-item :label="transformI18n('orders.toBeCollect')">
                <div class="item-value level-3">
                  <span>
                    {{ form.leads.order.to_be_collect }}
                  </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24" class="item-box">
              <el-form-item :label="transformI18n('productSpec.product')">
                <div class="item-value level-3">
                  <span v-if="form.leads && form.leads.product">
                    {{ form.leads.product }}
                  </span>
                  <span v-else class="empty"> Empty </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24" class="item-box">
              <el-form-item :label="transformI18n('client.email')">
                <div class="item-value level-3">
                  <span v-if="form.client">
                    {{ form.client.email }}
                  </span>
                  <span v-else class="empty"> Empty </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24" class="item-box">
              <el-form-item :label="transformI18n('common.comments')">
                <div class="item-value level-3">
                  <span v-if="form.comment">
                    {{ form.comment }}
                  </span>
                  <span v-else class="empty"> Empty </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col
              :span="24"
              class="item-box"
              v-if="form.type == 'sale' && noteList"
            >
              <el-form-item :label="'Notes'">
                <div class="mt-[10px] item-value">
                  <span v-if="noteList.length">
                    <el-timeline style="max-width: 90%">
                      <el-timeline-item
                        v-for="(noteInfo, index) in noteList"
                        :key="index"
                        :timestamp="noteInfo.created_at"
                        color="#409EFF"
                        :hollow="true"
                      >
                        {{ noteInfo.comment }}
                      </el-timeline-item>
                    </el-timeline>
                  </span>
                  <span v-else class="empty"> Empty </span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="24" class="item-box" v-if="form.leads">
              <el-form-item
                v-if="
                  form.type == 'cm' && form.leads.cm && form.leads.cm.length
                "
                :label="transformI18n('appointment.cmAttachment')"
              >
                <div class="flex flex-col grow">
                  <div
                    class="file-box flex flex-row justify-between items-center mt-5 pl-2.5"
                    v-for="(file, index) in form.leads.cm"
                    :key="index"
                  >
                    <div class="flex item-center">
                      <span class="mr-1 iconfont just-icon-upload" />
                      <span>
                        {{ file.name }}
                      </span>
                    </div>

                    <div>
                      <i
                        @click="previewFile(file)"
                        class="cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
                      />
                      <span
                        v-if="hasAuth('delOrderUploadFile')"
                        class="iconfont just-icon-delete ml-2 primary-color"
                        @click="delAttachment(file)"
                      />
                    </div>
                  </div>
                </div>
              </el-form-item>

              <el-form-item
                v-if="
                  form.type == 'sale' &&
                  form.leads.contracts &&
                  form.leads.contracts.length
                "
                :label="transformI18n('appointment.leadsAttachment')"
              >
                <div class="flex flex-col grow">
                  <div
                    class="file-box flex flex-row justify-between items-center mt-5 pl-2.5"
                    v-for="(file, index) in form.leads.contracts"
                    :key="index"
                  >
                    <div class="flex item-center">
                      <span class="mr-1 iconfont just-icon-upload" />
                      <span>
                        {{ file.name }}
                      </span>
                    </div>

                    <div>
                      <span
                        @click="previewFile(file)"
                        class="mr-1 iconfont just-icon-download-line !text-[#000]"
                      />
                      <span
                        v-if="hasAuth('delLeadsUploadFile')"
                        class="iconfont just-icon-delete ml-2 primary-color"
                        @click="delAttachment(file)"
                      />
                    </div>
                  </div>
                </div>
              </el-form-item>

              <el-form-item
                v-if="
                  form.type == 'install' &&
                  form.leads.install &&
                  form.leads.install.length
                "
                :label="transformI18n('appointment.cmAttachment')"
              >
                <div class="flex flex-col grow">
                  <div
                    class="file-box flex flex-row justify-between items-center mt-5 pl-2.5"
                    v-for="(file, index) in form.leads.install"
                    :key="index"
                  >
                    <div class="flex item-center">
                      <span class="mr-1 iconfont just-icon-upload" />
                      <span>
                        {{ file.name }}
                      </span>
                    </div>
                    <div>
                      <i
                        @click="previewFile(file)"
                        class="cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
                      />
                      <span
                        v-if="hasAuth('delOrderUploadFile')"
                        class="iconfont just-icon-delete ml-2 primary-color"
                        @click="delAttachment(file)"
                      />
                    </div>
                  </div>
                </div>
              </el-form-item>

              <el-form-item
                v-if="
                  form.type == 'service' &&
                  form.leads.service_file &&
                  form.leads.service_file.length
                "
                :label="transformI18n('appointment.serviceAttachment')"
              >
                <div class="flex flex-col grow">
                  <div
                    class="file-box flex flex-row justify-between items-center mt-5 pl-2.5"
                    v-for="(file, index) in form.leads.service_file"
                    :key="index"
                  >
                    <div class="flex item-center">
                      <span class="mr-1 iconfont just-icon-upload" />
                      <span>
                        {{ file.name }}
                      </span>
                    </div>
                    <div>
                      <i
                        @click="previewFile(file)"
                        class="cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
                      />
                      <span
                        v-if="hasAuth('delOrderUploadFile')"
                        class="iconfont just-icon-delete ml-2 primary-color"
                        @click="delAttachment(file)"
                      />
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </el-drawer>
  </div>
</template>
<style lang="scss" scoped>
.appointment-detail-drawer {
  .item-box {
    margin-top: 15px;
  }

  .level-3 {
    line-height: 16px;
  }

  .action-icon {
    width: 14px;
    height: 14px;
    margin-right: 5px;
    font-size: 14px;
  }

  .footer-action {
    padding: 20px 28px;
    border-top: 1px solid #e9ebf0;
  }

  .header-action {
    font-size: 12px;
    color: #656f7d;

    .iconfont {
      color: #656f7d;
    }
  }

  .item-value {
    display: flex;
    align-items: center;
    min-height: 34px;
  }

  .iconfont {
    font-size: 15px;
    color: var(--el-color-primary);
  }

  .grayBtn {
    color: var(--el-color-primary);
    background: rgb(153 55 229 / 10%);
    border: none;
    opacity: 0.5;

    .iconfont {
      color: var(--el-color-primary);
    }
  }
}

.file-box {
  border: 1px solid #e9ebf0;
  border-radius: 8px;
}

.readonlyCheckbox {
  :deep(.el-checkbox__input.is-checked) {
    .el-checkbox__inner {
      background-color: var(--el-checkbox-checked-bg-color);
      border-color: var(--el-checkbox-checked-input-border-color);
    }
  }
  // .el-checkbox__input.is-checked .el-checkbox__inner {
  //   background-color: var(--el-checkbox-checked-bg-color);
  //   border-color: var(--el-checkbox-checked-input-border-color);
  // }
}

.view-detail-item {
  margin-left: 5px;
  color: var(--el-color-primary);
  cursor: pointer;
}

.w-100 {
  width: 100%;
}

@media screen and (width <= 768px) {
  .view-leads {
    display: none;
  }
}

@media screen and (width <= 480px) {
  :deep(.el-drawer) {
    width: 100% !important;
  }
}
</style>
