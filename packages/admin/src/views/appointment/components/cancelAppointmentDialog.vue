<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { appointmentControllerCancel } from "@/api/admin/appointment";
import { transformI18n } from "@/plugins/i18n";
import { sendFormFilter } from "@/utils/form";
import { ElMessage } from "element-plus";

const dialogVisible = ref(false);
const promise: any = {};
const title = ref("Are you sure you want to cancel this appointment?");
const loading = ref(false);
const form = reactive({
  id: null,
  followup_time: null,
  note: "",
  type: null,
  on_product_date: null,
  ready_date: null
});
const formRef = ref(null);
const rules = {
  note: [
    { required: true, message: "Please enter the note", trigger: "change" }
  ],
  followup_time: [
    {
      required: true,
      message: "Please enter the followup date",
      trigger: "change"
    }
  ],
  on_product_date: [
    {
      required: true,
      message: "Please enter the production date",
      trigger: "change"
    }
  ],
  ready_date: [
    {
      required: true,
      message: "Please enter the ready date",
      trigger: "change"
    }
  ]
};

function show(data) {
  if (!data.id) return;
  initFormData();
  form.id = data.id;
  form.type = data.type;
  if (form.type !== "sale") {
    delete rules.followup_time;
  }
  if (form.type !== "service") {
    delete rules.on_product_date;
  }
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function disabledDate(time) {
  return time.getTime() < Date.now();
}

function initFormData() {
  Object.keys(form).forEach(key => {
    switch (key) {
      default:
        form[key] = null;
        break;
    }
  });
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  promise.resolve(actionOver);
}

function cancelAppointment() {
  formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      const sendForm = sendFormFilter(form);
      delete sendForm.id;
      delete sendForm.type;
      appointmentControllerCancel(form.id, sendForm)
        .then(res => {
          loading.value = false;
          if (res.success) {
            ElMessage.success(res.message);
            hide(true);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_err => {
          loading.value = false;
        });
    }
  });
  return;
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'Cancel Appointment'"
    align-center
    :width="'550px'"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="center-box">
        <div class="title level-1">{{ title }}</div>
        <el-row class="mt-[20px] w-[100%]">
          <el-col :span="24" v-if="form.type === 'sale'">
            <el-form-item prop="followup_time" label="Followup Time">
              <el-date-picker
                v-model="form.followup_time"
                type="date"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] grow"
                :placeholder="transformI18n('common.select')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
                :disabled-date="disabledDate"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24" v-if="form.type === 'service'">
            <el-form-item prop="on_product_date" label="On Production Time">
              <el-date-picker
                v-model="form.on_product_date"
                type="date"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] grow"
                :placeholder="transformI18n('common.select')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
              />
            </el-form-item>
          </el-col> -->
          <el-col :span="24" v-if="form.type === 'service'">
            <el-form-item prop="ready_date" label="Ready Date">
              <el-date-picker
                v-model="form.ready_date"
                type="date"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] grow"
                :placeholder="transformI18n('common.select')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="note" label="Note">
              <el-input
                v-model="form.note"
                type="textarea"
                size="small"
                :rows="3"
                :placeholder="transformI18n('common.input')"
                autocomplete="off"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px]"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide(false)"
          >Later</el-button
        >
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :loading="loading"
          @click="cancelAppointment"
          >{{ transformI18n("leads.confirm") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.result-icon {
  width: 50px;
  height: 50px;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.warning {
  color: #ff9000;
}

.iconfont {
  font-size: 24px;
}
</style>
