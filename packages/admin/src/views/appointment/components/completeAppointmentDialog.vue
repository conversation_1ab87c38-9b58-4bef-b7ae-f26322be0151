<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { appointmentControllerComplete } from "@/api/admin/appointment";
import { transformI18n } from "@/plugins/i18n";
import { sendFormFilter } from "@/utils/form";
import { ElMessage } from "element-plus";
import { allResults as leadsAllResults } from "@/views/leads/components/leads_data.ts";

const dialogVisible = ref(false);
const promise: any = {};
const title = ref("Are you sure you want to complete this appointment?");
const loading = ref(false);
const form = reactive({
  id: null,
  sub_result: null,
  note: "",
  quoted: null,
  sold: null,
  retail: null,
  result: null,
  type: null,
  on_product_date: null,
  followup_date: null
});
const saleLeadsResult = ref(null);
const installResultList = ref([
  {
    value: "extra",
    name: "Extra"
  },
  {
    value: "outstanding",
    name: "Outstanding"
  }
]);
const serviceResultList = [
  {
    value: "received",
    name: "Plan Received"
  },
  {
    value: "outstanding",
    name: "Outstanding"
  }
];
const formRef = ref(null);
const rules = {
  result: [
    {
      required: true,
      message: "Please select the result type",
      trigger: "change"
    }
  ],
  quoted: [
    {
      required: true,
      message: "Please enter the quoted amount",
      trigger: "change"
    }
  ],
  retail: [
    {
      required: true,
      message: "Please enter the retail amount",
      trigger: "change"
    }
  ],
  sold: [
    {
      required: true,
      message: "Please enter the sold amount",
      trigger: "change"
    }
  ]
};

function show(data) {
  if (!data.id) return;
  initFormData();
  form.id = data.id;
  form.type = data.type;
  if (form.type !== "sale") {
    delete rules.retail;
    delete rules.quoted;
    delete rules.sold;
  } else {
    form.quoted = data.leads.quoted;
    form.retail = data.leads.retail;
    form.sold = data.leads.sold;
  }
  if (form.type == "cm") {
    // rules.value = [];
    Object.assign(rules, {});
  }
  if (form.type == "install") {
    form.result = "outstanding";
  }
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function initFormData() {
  Object.keys(form).forEach(key => {
    switch (key) {
      default:
        form[key] = null;
        break;
    }
  });
  saleLeadsResult.value = null;
}

function hide(actionOver = false) {
  dialogVisible.value = false;
  promise.resolve(actionOver);
}

function completeAppointment() {
  formRef.value.validate(valid => {
    if (valid) {
      const sendForm = sendFormFilter(form);
      delete sendForm.id;
      delete sendForm.type;
      if (form.result !== "sold") {
        delete sendForm.sold;
      } else {
        sendForm.quoted = sendForm.quoted || sendForm.sold;
      }
      if (form.result == "ready" && (!form.followup_date || !form.note)) {
        ElMessage.error("Please select the followup date and input the note!");
        return;
      }
      loading.value = true;
      appointmentControllerComplete(form.id, sendForm)
        .then(res => {
          loading.value = false;
          if (res.success) {
            ElMessage.success(res.message);
            hide(true);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_err => {
          loading.value = false;
        });
    }
  });
  return;
}

// get current row result icon
function getCurrentResultIcon(result) {
  if (!result) {
    return;
  }
  let resultsListNoHandle = JSON.parse(JSON.stringify(leadsAllResults));
  resultsListNoHandle = [
    ...resultsListNoHandle,
    { value: "cancelled", label: "Cancelled", icon: "cancelled" }
  ];
  if (typeof result === "string") {
    return resultsListNoHandle.find(item => item.value === result)?.icon;
  } else if (result instanceof Array) {
    return resultsListNoHandle.find(item => item.value === result[0])?.icon;
  }
}

function getResultOptions() {
  let resultsListNoHandle = JSON.parse(JSON.stringify(leadsAllResults));
  resultsListNoHandle = [
    ...resultsListNoHandle,
    {
      value: "cancelled",
      label: "Cancelled",
      icon: "cancelled",
      children: [
        {
          value: "With other company",
          label: "With other company"
        },
        {
          value: "No interested",
          label: "No interested"
        },
        {
          value: "Cant do job",
          label: "Cant do job"
        },
        {
          value: "Wants Ballpark",
          label: "Wants Ballpark"
        },
        {
          value: "Renting",
          label: "Renting"
        },
        {
          value: "Made by mistake",
          label: "Made by mistake"
        },
        {
          value: "Single party",
          label: "Single party"
        },
        {
          value: "Wrong product",
          label: "Wrong product"
        },
        {
          value: "Wrong number",
          label: "Wrong number"
        },
        {
          value: "Unemployed",
          label: "Unemployed"
        },
        {
          value: "On confirmation",
          label: "On confirmation"
        },
        {
          value: "By company",
          label: "By company"
        },
        {
          value: "By customer",
          label: "By customer"
        },
        {
          value: "Others",
          label: "Others"
        },
        {
          value: "Out of Area",
          label: "Out of Area"
        }
      ]
    }
  ];
  //Change the result disabled property based on the role
  const resultsListHandle = [];
  const canToResultsList = ["quoted", "sold", "cancelled"];
  resultsListNoHandle.map(resultItem => {
    if (
      canToResultsList.length &&
      canToResultsList.includes(resultItem.value)
    ) {
      resultItem.disabled = false;
    } else {
      resultItem.disabled = true;
    }
    if (resultItem.children && resultItem.children.length) {
      if (resultItem.value == "quoted") {
        // resultItem.children = [];
      } else {
        resultItem.children.forEach(child => {
          child.disabled = resultItem.disabled;
        });
      }
    }
    resultsListHandle.push(resultItem);
  });
  return resultsListHandle;
}

function handleResultChange(value) {
  saleLeadsResult.value = value;
  form.result = value[0];
  form.sub_result = value[1] || null;
}
onMounted(() => {});
defineExpose({ show, hide });
</script>
<template>
  <el-dialog
    v-model="dialogVisible"
    :title="'Complete Appointment'"
    align-center
    :width="'550px'"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="center-box">
        <div class="title level-1">{{ title }}</div>
        <el-row class="mt-[20px] w-[100%]" :gutter="10">
          <el-col v-if="form.type === 'sale'" :span="24" class="item-box">
            <el-form-item
              :label="transformI18n('common.result') + ':'"
              prop="result"
            >
              <span
                :class="
                  'mr-1 iconfont just-icon-' +
                  getCurrentResultIcon(form.result) +
                  ' input-result-icon'
                "
              />
              <el-cascader
                :options="getResultOptions()"
                :show-all-levels="false"
                :model-value="saleLeadsResult"
                @change="value => handleResultChange(value)"
                class="border border-[#E9EBF0] rounded-[6px] px-[5px]"
              >
                <template #default="{ data }">
                  <span
                    :class="
                      'cascader-label' + (data.disabled ? ' disabled' : '')
                    "
                  >
                    <span
                      :class="'mr-1 iconfont just-icon-' + data.icon"
                      v-if="data.icon"
                    />
                    {{ data.label }}</span
                  >
                </template>
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.type === 'sale' && form.result != 'cancelled'"
            :span="12"
            class="item-box"
          >
            <el-form-item :label="transformI18n('leads.retail')" prop="retail">
              <el-input
                v-model="form.retail"
                :placeholder="transformI18n('common.input')"
                autocomplete="off"
                class="border border-[#E9EBF0] rounded-[6px] px-[5px]"
              />
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.type === 'sale' && form.result == 'quoted'"
            :span="12"
            class="item-box"
          >
            <el-form-item :label="transformI18n('leads.quoted')" prop="quoted">
              <el-input
                v-model="form.quoted"
                :placeholder="transformI18n('common.input')"
                autocomplete="off"
                class="border border-[#E9EBF0] rounded-[6px] px-[5px]"
              />
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.type === 'sale' && form.result == 'sold'"
            :span="12"
            class="item-box"
          >
            <el-form-item :label="transformI18n('leads.sold')" prop="sold">
              <el-input
                v-model="form.sold"
                :placeholder="transformI18n('common.input')"
                autocomplete="off"
                class="border border-[#E9EBF0] rounded-[6px] px-[5px]"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="form.type === 'install'" :span="24">
            <el-form-item
              :label="transformI18n('common.result') + ':'"
              prop="result"
            >
              <el-select
                v-model="form.result"
                class="border border-[#E9EBF0]"
                :placeholder="transformI18n('common.select')"
              >
                <el-option
                  v-for="item in installResultList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            v-if="form.type === 'install' && form.result === 'ready'"
            :span="24"
          >
            <el-form-item label="Folloup Date" prop="followup_date">
              <el-date-picker
                v-model="form.followup_date"
                type="date"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] grow"
                :placeholder="transformI18n('common.select')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="form.type === 'service'" :span="24">
            <el-form-item
              :label="transformI18n('common.result') + ':'"
              prop="result"
            >
              <el-select
                v-model="form.result"
                class="border border-[#E9EBF0]"
                :placeholder="transformI18n('common.select')"
              >
                <el-option
                  v-for="item in serviceResultList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.result === 'onProduction'">
            <el-form-item prop="on_product_date" label="On Production Time">
              <el-date-picker
                v-model="form.on_product_date"
                type="date"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px] grow"
                :placeholder="transformI18n('common.select')"
                format="YYYY/MM/DD"
                value-format="YYYY/MM/DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="note" label="Note">
              <el-input
                v-model="form.note"
                type="textarea"
                size="small"
                :rows="3"
                :placeholder="transformI18n('common.input')"
                autocomplete="off"
                class="border border-[#E9EBF0] rounded-[6px] p-[5px]"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide(false)"
          >Later</el-button
        >
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :loading="loading"
          @click="completeAppointment"
          >{{ transformI18n("leads.confirm") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
:deep(.el-dialog__header) {
  border-bottom: 0;
}

.content {
  margin-top: 8px;
  font-size: 14px;
  line-height: 20px;
  color: var(--font-color-level2);
  text-align: center;
}

.result-icon {
  width: 50px;
  height: 50px;
}

.center-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.warning {
  color: #ff9000;
}

.iconfont {
  font-size: 24px;
}
</style>
