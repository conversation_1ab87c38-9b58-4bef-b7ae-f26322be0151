<script lang="ts" setup>
import { ref, watch, nextTick } from "vue";
import { statusColor, appointmentTypeText } from "../data";
import CirCleText from "@/components/CircleText";
import { hasAuth } from "@/router/utils";
import { useNav } from "@/layout/hooks/useNav";
import { Check } from "@element-plus/icons-vue";
import { allResults as leadsAllResults } from "@/views/leads/components/leads_data.ts";

const props = defineProps({
  eventArg: {
    type: Object,
    default: () => {
      return {};
    }
  }
});
const { isAppointmentUser, username } = useNav();
const extendedProps = ref(null);
const emit = defineEmits<{
  (e: "detailAction", val: Object): void;
  (e: "changeLeadsResult", val: Object): void;
  (e: "refresh", val: any): void;
}>();
watch(
  () => props.eventArg.event.extendedProps,
  _value => {
    handData(_value);
  },
  {
    immediate: true,
    deep: true
  }
);
const resultCascaderProps = {
  checkStrictly: true,
  emitPath: true
};
const cascaderKey = ref(1.0);
const showCascader = ref(true);

function handData(value) {
  extendedProps.value = value || null;
}

function handleAction(item, type) {
  emit("detailAction", { item, type });
}

// get leads Result options
function getResultOptions(result) {
  let resultsListNoHandle = JSON.parse(JSON.stringify(leadsAllResults));
  if (result === "appointed") {
    resultsListNoHandle = [
      ...resultsListNoHandle,
      {
        value: "appointed",
        label: "Appointed",
        icon: "appointed",
        disabled: true
      }
    ];
  }
  //Change the result disabled property based on the role
  const resultsListHandle = [];
  let canToResultsList = [];
  if (hasAuth("changeLeadsResult")) {
    switch (result) {
      case "new":
        canToResultsList = ["followup", "quoted"];
        break;
      case "followup":
        canToResultsList = ["quoted"];
        break;
      case "appointed":
        canToResultsList = ["followup", "quoted", "sold"];
        break;
      case "quoted":
        canToResultsList = ["followup", "sold"];
        break;
    }
  } else if (hasAuth("followupLeads")) {
    switch (result) {
      case "new":
        canToResultsList.push("followup");
        break;
      case "appointed":
        canToResultsList.push("followup");
        break;
    }
  }

  if (hasAuth("cancelLeads")) {
    canToResultsList.push("cancelled");
  }

  resultsListNoHandle.map(resultItem => {
    if (
      canToResultsList.length &&
      canToResultsList.includes(resultItem.value)
    ) {
      resultItem.disabled = false;
    } else {
      resultItem.disabled = true;
    }
    if (resultItem.children && resultItem.children.length) {
      // if (resultItem.value == "quoted") {
      //   resultItem.children = [];
      // } else {
      resultItem.children.forEach(child => {
        child.disabled = resultItem.disabled;
      });
      //}
    }
    resultsListHandle.push(resultItem);
  });
  return resultsListHandle;
}

function handleResultClose(isShow) {
  if (!isShow) {
    cascaderKey.value = Math.floor(Math.random() * 100);
    showCascader.value = false;
    nextTick(() => {
      showCascader.value = true;
    });
  }
}

function handleResultChange(value, row) {
  emit("changeLeadsResult", { value, row });
}

defineExpose({});
</script>
<template>
  <div>
    <div
      class="list-view-page flex items-center al h-full text-[10px]"
      v-if="extendedProps"
      @click.stop="handleAction(extendedProps, 'view')"
    >
      <div class="w-[60px] item">
        <span v-if="appointmentTypeText[extendedProps.type]">{{
          appointmentTypeText[extendedProps.type].label
        }}</span>
      </div>
      <div class="w-[95px] item">
        <div class="flex items-center">
          <el-tag
            :color="extendedProps.bgColor"
            class="!w-[12px] !h-[12px] !border-0 mr-[8px] !p-0 shrink-0"
          />
          <span
            :style="{ color: extendedProps.color }"
            v-if="statusColor[extendedProps.status]"
            >{{ statusColor[extendedProps.status].label }}</span
          >
        </div>
      </div>
      <div class="w-[90px] item">
        <span>{{
          extendedProps.leads ? extendedProps.leads.category.name : ""
        }}</span>
      </div>
      <div class="w-[130px] item flex">
        <CirCleText
          :text="extendedProps.staff"
          :size="19"
          :fontSize="'10px'"
          :customBgColor="extendedProps.staffColor"
        />
        {{ extendedProps.staff }}
        <el-tooltip
          effect="dark"
          v-if="
            extendedProps.is_assign_received &&
            extendedProps.assign_to &&
            username != extendedProps.assign_to?.login_name
          "
          content="Received"
          placement="top"
        >
          <Check
            @click.stop=""
            class="ml-[5px]"
            style="font-size: 10px; color: var(--el-color-primary)"
          />
        </el-tooltip>
      </div>
      <div class="w-[160px] item">
        <span class="iconfont just-icon-client mr-2" />
        <span v-if="extendedProps.client">
          {{ extendedProps.client.surname }}
          {{ extendedProps.client.given_name }}
        </span>
      </div>
      <div class="w-[110px] item">
        {{ extendedProps.object_id }}
      </div>
      <div class="w-[140px] item">
        <div
          class="flex items-center"
          v-if="extendedProps.leads.result != 'appointed'"
        >
          <span
            :class="`iconfont primary-color just-icon-${extendedProps.leads.result} mr-2`"
          />
          <span class="capitalize">{{
            extendedProps.leads ? extendedProps.leads.result : ""
          }}</span>
        </div>
        <div class="flex items-center" v-else @click.stop="">
          <span
            :class="`mr-1 iconfont just-icon-${extendedProps.leads.result} input-result-icon`"
          />
          <el-cascader
            :options="getResultOptions(extendedProps.leads.result)"
            :show-all-levels="false"
            :model-value="extendedProps.leads.result"
            @change="value => handleResultChange(value, extendedProps.leads)"
            @visible-change="isShow => handleResultClose(isShow)"
            :props="resultCascaderProps"
            :key="cascaderKey"
            v-if="showCascader"
          >
            <template #default="{ data }">
              <span
                :class="'cascader-label' + (data.disabled ? ' disabled' : '')"
              >
                <span
                  :class="'mr-1 iconfont just-icon-' + data.icon"
                  v-if="data.icon"
                />
                {{ data.label }}</span
              >
            </template>
          </el-cascader>
        </div>
      </div>
      <div class="w-[100px] item">
        <span v-if="extendedProps.client">
          {{ extendedProps.client.phone }}
        </span>
      </div>
      <div class="grow item text-over w-[140px]" @click.stop="">
        <el-tooltip :content="extendedProps.address" placement="top">
          {{ extendedProps.address }}
        </el-tooltip>
      </div>
      <div class="w-[120px] item">
        <span v-if="extendedProps.leads">
          {{
            extendedProps.suburb ||
            extendedProps.leads.suburb ||
            extendedProps?.client?.suburb
          }}
        </span>
      </div>
      <div class="w-[130px] item">
        <CirCleText
          v-if="extendedProps.leads && extendedProps.leads.appt_setter"
          :text="extendedProps.leads.appt_setter.name"
          :size="19"
          :fontSize="'10px'"
          :customBgColor="extendedProps.staffColor"
        />
        {{ extendedProps.leads?.appt_setter?.name }}
      </div>
      <div class="w-[60px] item" @click.stop="">
        <el-dropdown
          trigger="click"
          v-if="!isAppointmentUser"
          v-auth="['editAppointment', 'cancelAppointment', 'revokeAppointment']"
        >
          <span class="iconfont just-icon-gengduo" />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                @click.stop="handleAction(extendedProps, 'view')"
              >
                View
              </el-dropdown-item>
              <el-dropdown-item
                v-if="
                  hasAuth('editAppointment') &&
                  extendedProps.status !== 'cancel' &&
                  extendedProps.status != 'completed'
                "
                @click.stop="handleAction(extendedProps, 'edit')"
              >
                Edit
              </el-dropdown-item>
              <el-dropdown-item
                v-if="
                  hasAuth('cancelAppointment') &&
                  extendedProps.status !== 'cancel' &&
                  extendedProps.status != 'completed'
                "
                @click.stop="handleAction(extendedProps, 'cancel')"
              >
                Cancel
              </el-dropdown-item>
              <el-dropdown-item
                v-if="
                  hasAuth('revokeAppointment') &&
                  (extendedProps.status == 'cancel' ||
                    extendedProps.status == 'completed')
                "
                @click.stop="handleAction(extendedProps, 'revoke')"
              >
                Revoke
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<style scoped>
.list-view-page :deep(.el-input__inner) {
  height: 20px;
}
</style>
