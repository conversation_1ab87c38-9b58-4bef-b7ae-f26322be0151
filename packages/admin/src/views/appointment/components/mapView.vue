<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { statusOption } from "../data";
import { appointmentControllerIndex } from "@/api/admin/appointment";
import { AppointmentMap } from "@/components/GoogleAmap/";
import dayjs from "dayjs";
import CirCleText from "@/components/CircleText";
import { accountControllerIndex } from "@/api/admin/admin-account";
import { useNav } from "@/layout/hooks/useNav";

const { isAppointmentUser } = useNav();

const getStaffOptionsLoading = ref(false);
const appointmentStaffList = ref();
const props = defineProps({
  productCategoryList: {
    type: Array,
    default() {
      return [];
    }
  },
  companyRegion: {
    type: Array,
    default() {
      return [];
    }
  }
});
const appointmentMapRef = ref(null);
const form = reactive({
  status: "all",
  date: "",
  mapSearch: null,
  staff: [],
  product: []
});
const loading = ref(false);
const appointmentsList = ref([]);
const emit = defineEmits<{
  (e: "refresh", val: any): void;
  (e: "toAppointmentDetail", val: any): void;
  (e: "updateMapMarksNum"): Number;
}>();

function handleStatusChange(_status) {
  getappointmentsList();
}

// Map component search criteria change processing
function mapSearchChange(value) {
  form.mapSearch = value;
  getappointmentsList();
}

function onReset() {
  form.date = null;
  getappointmentsList();
}

function getappointmentsList() {
  const search =
    form.mapSearch && form.mapSearch.search ? `%${form.mapSearch.search}%` : "";
  const filterInfo = [];
  if (form.status && form.status != "all") {
    filterInfo.push(`status:eq:${form.status}`);
  } else {
    filterInfo.push(`status:nin:cancel`);
  }
  if (form.date) {
    const startDate = dayjs(form.date[0]).format("YYYY-MM-DD 00:00:00");
    const endDate = dayjs(form.date[1]).format("YYYY-MM-DD 23:59:59");
    filterInfo.push(`date:gte:${startDate}`);
    filterInfo.push(`date:lte:${endDate}`);
  }
  if (form.mapSearch && form.mapSearch.unsigned) {
    filterInfo.push(`user_id:null`);
  }
  if (form.mapSearch && form.mapSearch.type && form.mapSearch.type.length) {
    const typeStr = form.mapSearch.type.join("|");
    filterInfo.push(`type:in:${typeStr}`);
  }
  if (form.staff && form.staff.length) {
    const staffStr = form.staff.join("|");
    filterInfo.push(`user_id:in:${staffStr}`);
  }
  if (props.companyRegion.length) {
    filterInfo.push(`company_region:in:${props.companyRegion.join("|")}`);
  }
  const filter = filterInfo.join(",");
  const sort = "";
  const _with =
    "leads,client,assignTo,leads.contracts, leads.cm, leads.install, leads.serviceFile, service,leads.category";
  const withCount = "";
  const page = 1;
  const size = 999;
  let subCategoryIds = null;
  if (form.product) {
    subCategoryIds = form.product.join(",");
  }
  loading.value = true;
  appointmentControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    subCategoryIds
  )
    .then(res => {
      loading.value = false;
      const { data } = res;
      appointmentsList.value = data || [];
      emit("updateMapMarksNum", appointmentsList.value?.length);
    })
    .catch(_err => {
      loading.value = false;
    });
}

function toDetail(value) {
  emit("toAppointmentDetail", value);
}

function getStaffOptions(query = "") {
  appointmentStaffList.value = [];
  let filter = "";
  filter +=
    "roles.name:in:sales_consultant_user|install_user|sales_consultant_user";
  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  const size = 99;
  getStaffOptionsLoading.value = true;
  const search = query ? "%" + query + "%" : "";
  let companyRegionStr = null;
  if (props.companyRegion.length) {
    companyRegionStr = props.companyRegion.join(",");
  }
  accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    companyRegionStr
  )
    .then(res => {
      appointmentStaffList.value = res.data || [];
      getStaffOptionsLoading.value = false;
    })
    .catch(_error => {
      getStaffOptionsLoading.value = false;
    });
}

function initMap() {
  nextTick(() => {
    getappointmentsList();
    appointmentMapRef.value.init();
  });
}
onMounted(() => {
  if (!isAppointmentUser) {
    getStaffOptions();
  }
});
defineExpose({ initMap });
</script>
<template>
  <div class="map-view-page flex flex-col h-full" v-loading="loading">
    <div
      class="search-box flex justify-between align-middle p-[10px] border-0 shrink-0 flex-wrap"
    >
      <div class="left flex align-middle mt-[10px]">
        <el-date-picker
          v-model="form.date"
          type="daterange"
          start-placeholder="Start date"
          end-placeholder="End date"
          format="YYYY-MM-DD"
          date-format="YYYY/MM/DD"
          class="mr-[10px] border border-[#E9EBF0]"
        />
        <el-button type="primary" @click="getappointmentsList">
          {{ transformI18n("common.filter") }}
        </el-button>
        <el-button @click="onReset()" type="info">{{
          transformI18n("common.clear")
        }}</el-button>
      </div>
      <div>
        <el-select
          v-model="form.status"
          placeholder="Status"
          class="w-[145px] mr-[10px] border-[#E9EBF0] border px-[10px] mt-[10px]"
          @change="handleStatusChange"
        >
          <el-option
            v-for="item in statusOption"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <div class="flex items-center">
              <el-tag
                :color="item.color"
                class="!w-[12px] !h-[12px] !border-0 mr-[8px] !p-0"
              />
              <span :style="{ color: item.color }">{{ item.label }}</span>
            </div>
          </el-option>
        </el-select>
        <el-select
          v-if="!isAppointmentUser"
          v-model="form.staff"
          filterable
          clearable
          multiple
          collapse-tags
          class="w-[240px] border-[#E9EBF0] border px-[10px] mt-[10px]"
          placeholder="Filter Staff"
          :loading="getStaffOptionsLoading"
          :remote-method="getStaffOptions"
          remote
          @change="getappointmentsList"
        >
          <el-option
            v-for="item in appointmentStaffList"
            :key="item.id"
            :label="item.login_name"
            :value="item.id"
          >
            <div class="flex items-center">
              <CirCleText
                :text="item.login_name"
                :size="20"
                :fontSize="'10px'"
                :customBgColor="item.color"
              />
              <span>{{ item.login_name }}</span>
            </div>
          </el-option>
        </el-select>
        <el-select
          v-model="form.product"
          clearable
          placeholder="Filter Subproduct"
          class="ml-1 border-[#E9EBF0] border px-[10px] mt-[10px] min-w-[220px]"
          @change="getappointmentsList"
          multiple
          collapse-tags
          collapse-tags-tooltip
          filterable
        >
          <el-option-group
            v-for="group in props.productCategoryList"
            :key="group.name"
            :label="group.name"
          >
            <el-option
              v-for="item in group.products"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-option-group>
        </el-select>
      </div>
    </div>
    <div class="map-box rounded-[10px]">
      <AppointmentMap
        ref="appointmentMapRef"
        :mapID="'appointmentMap'"
        :appointmentsList="appointmentsList"
        :pageType="'appointmentListPage'"
        :zoom="5"
        @toAppointmentDetail="toDetail"
        @appointmentSearchChange="mapSearchChange"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.map-box {
  position: relative;
  flex-grow: 1;
  max-height: calc(100vh - 180px);
}
</style>
