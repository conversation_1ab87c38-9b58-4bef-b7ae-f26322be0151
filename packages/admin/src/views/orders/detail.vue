<script setup lang="ts">
import { reactive, ref, onMounted, nextTick, watch, computed } from "vue";
import {
  detailActionList,
  allResults,
  calculatedAmount
} from "./components/data";
import {
  orderControllerUpdate,
  orderControllerShow,
  orderControllerGeneratorPDF,
  orderControllerSaveSpec,
  orderControllerGeneratorSatisfactionPDF,
  orderControllerGeneratorWarrantyCardPDF
} from "@/api/admin/order";
import { sendFormFilter } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
import DetailGeneral from "./components/DetailGeneral.vue";
import DetailPayment from "./components/DetailPayment.vue";
import DetailSpecs from "./components/DetailSpecs.vue";
import DetailInstallation from "./components/DetailInstall.vue";
import LeadsNote from "./components/LeadsNote.vue";
import { useCommonStoreHook } from "@/store/modules/common";
import { ElLoading, ElMessage } from "element-plus";
import dayjs from "dayjs";
import ChangeResult from "@/components/ChangeResult/ChangeResult.vue";
import { hasAuth } from "@/router/utils";
import { removeElseFromObjArr } from "@/utils/common";
import InvoiceDownDialog from "@/components/InvoiceDownDialog";

const activeName = ref("general");
const detailGeneralRef = ref(null);
const detailPaymentRef = ref(null);
const detailSpecsRef = ref(null);
const canCreateAppointment = ref(false);
const canEdit = computed(() => {
  if (
    form.result !== "cancelled" &&
    form.result !== "completion" &&
    editPage.value
  ) {
    return true;
  }
  return false;
});
const editPage = ref(true);
const firstProduct = computed(() => {
  if (form.lead && form.lead.product) {
    const commaIndex = form.lead.product.indexOf(",");
    if (commaIndex === -1) {
      return form.lead.product; // 没有逗号，返回整个字符串
    } else {
      return form.lead.product.substring(0, commaIndex); // 返回第一个逗号前的内容
    }
  }
  return "";
});
const form = reactive({
  id: null,
  lead_id: null,
  received_date: "",
  result: "new_order",
  under_sell: null,
  estimated_comm: null,
  start_date: null,
  product_staff_id: null,
  cm_id: null,
  cm_booked_date: null,
  cm_received_date: null,
  on_product_date: null,
  ready_date: null,
  install_id: null,
  installation_date: null,
  completion_date: null,
  reason: null,
  finance_company: null,
  finance_type: null,
  finance_amount: null,
  finance_approved_date: null,
  finance_approved_no: null,
  payments: [],
  cm: null,
  install: null,
  lead: null,
  paid_amount: null,
  installed_date: null,
  electrician: 0,
  extras: "No"
});
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const downInvoiceLoading = ref(false);
const downSatisfactionLoading = ref(false);
const downloadWarrantyCardLoading = ref(false);
const productCategoryList = ref([]);
const subProductCategoryList = ref({});
const sourceList = ref([]);
const subSourceList = ref({});
const promotionList = ref([]);
const allStaffList = ref();
const currentTagResult = ref(null);
const dialogPanel = ref();
const orderDetailDialogRef = ref();
const resultCascaderProps = {
  checkStrictly: true,
  emitPath: true
};
// for change result
const changeResultValue = ref(null);
const currentTableAction = ref([]);
const resultOptionHandled = ref([]);
const changeResultRef = ref(null);
const detailInstallationRef = ref(null);
const cascaderKey = ref(null); // use to reflash current result cascader
const showCascader = ref(true);
const InvoiceDownDialogRef = ref(null);

const emit = defineEmits<{
  (e: "createService", val: Object): void;
}>();

const props = defineProps({
  allStaff: {
    type: Array,
    default() {
      return [];
    }
  }
});

watch(
  () => props.allStaff,
  _newVal => {
    allStaffList.value = _newVal;
  },
  {
    deep: true,
    immediate: true
  }
);

function show(data = null, isEditPage = true) {
  editPage.value = isEditPage;
  initFormData();
  activeName.value = "general";
  if (data) {
    if (typeof data === "number") {
      form.id = data;
      reloadOrderInfo();
    } else {
      form.id = data.id;
      reloadOrderInfo();
    }
  } else {
    dialogVisible.value = false;
  }
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

// Get the current row  operation
function getCurrentAction(result) {
  let actionList = [];
  if (result && detailActionList[result]) {
    actionList = detailActionList[result];
  } else {
    actionList = detailActionList["default"];
  }
  if (!actionList.length) {
    return [];
  }
  // Filter actions by role
  const removeList = [];
  const canCancel = hasAuth("cancelService");
  const canCreateService = hasAuth("createService");
  const canConvertToOrder = hasAuth("convertToOrder");
  if (!canCancel) {
    removeList.push("cancel");
  }
  if (!canCreateService) {
    removeList.push("createService");
  }
  if (!canConvertToOrder) {
    removeList.push("convertToOrder");
  }
  const canCompletion = hasAuth("changeOrderResult");
  // if ((form.lead && form.lead.sold != form.paid_amount) || !canCompletion) {
  //   removeList.push("completion");
  // }
  if (!canCompletion) {
    removeList.push("completion");
  }
  if (removeList.length) {
    actionList = removeElseFromObjArr(actionList, removeList);
  }
  return actionList;
}

function defaultDataHandle(data) {
  Object.assign(form, data);
  changeResultValue.value = form.result;
  currentTagResult.value = form.result;
  currentTableAction.value = getCurrentAction(currentTagResult.value);

  // Whether to display appointment create butteon
  if (
    [
      "new_order",
      "cnReceived",
      "onProduction",
      "ready",
      "outstanding",
      "extra"
    ].includes(data.result) &&
    hasAuth("addOrderAppointment")
  ) {
    canCreateAppointment.value =
      data.appointment && data.appointment.length ? false : true;
  } else {
    canCreateAppointment.value = false;
  }
  resultOptionHandled.value = getResultOptions(form);
  dialogVisible.value = true;
}

// Gets a resultList based on the current lead result and the login role
function getResultOptions(row) {
  const result = row.result;
  const lastResult = row.last_result;
  const HoldCanConvertList = [
    "new_order",
    "cmReceived",
    "onProduction",
    "ready"
  ];
  if (!result) {
    return [];
  }
  const resultsListNoHandle = JSON.parse(JSON.stringify(allResults));
  //Change the result disabled property based on the permission
  const resultsListHandle = [];
  let canToResultsList = [];
  if (hasAuth("changeOrderResult")) {
    switch (result) {
      case "new_order":
        canToResultsList = ["onProduction", "hold", "inCouncil"];
        break;
      case "cm":
        canToResultsList = ["new_order"];
        break;
      case "cmReceived":
        canToResultsList = ["onProduction", "hold", "inCouncil"];
        break;
      case "onProduction":
        canToResultsList = ["ready", "hold", "inCouncil"];
        break;
      case "ready":
        canToResultsList = ["onProduction", "hold", "inCouncil"];
        break;
      case "installation":
        canToResultsList = [""];
        break;
      case "outstanding":
        canToResultsList = ["completion"];
        break;
      case "hold":
        if (lastResult && HoldCanConvertList.includes(lastResult)) {
          canToResultsList = [lastResult];
        }
        break;
      case "inCouncil":
        //if (lastResult && HoldCanConvertList.includes(lastResult)) {
        canToResultsList = ["onProduction"];
        // }
        break;
    }
  }
  resultsListNoHandle.map(resultItem => {
    if (
      canToResultsList.length &&
      canToResultsList.includes(resultItem.value)
    ) {
      resultItem.disabled = false;
    } else {
      resultItem.disabled = true;
    }
    resultsListHandle.push(resultItem);
  });
  return resultsListHandle;
}

function initFormData() {
  Object.keys(form).forEach(key => {
    switch (key) {
      default:
        form[key] = null;
        break;
    }
  });
}

// 获取当前result icon
function getCurrentResultIcon(result) {
  if (!result) {
    return;
  }
  if (typeof result === "string") {
    return allResults.find(item => item.value === result)?.icon;
  } else if (result instanceof Array) {
    return allResults.find(item => item.value === result[0])?.icon;
  }
}

// Change order result
function handleResultChange(value, row) {
  const result = value[0];
  // If the remaining balance is changed from outstanding to completion, the remaining balance must be 0
  if (result == "completion" && calculatedAmount(row) !== 0) {
    const info = transformI18n("common.resultCompletionInfo");
    ElMessage.error(info);
    handleResultClose(false);
    return;
  }
  const data = {
    id: row.id,
    type: "order",
    result: result,
    row: row
  };
  changeResultRef.value
    .show(data)
    .then(overAction => {
      if (overAction) {
        hide();
        promise.resolve();
      } else {
        changeResultValue.value = currentTagResult.value;
      }
    })
    .catch(() => {
      changeResultValue.value = currentTagResult.value;
    });
}

function getDetailGeneral() {
  const newGeneralData = detailGeneralRef.value.getNewOrderGeneralInfo();
  form.product_staff_id = newGeneralData.product_staff_id;
  form.lead.sub_category_ids = newGeneralData.lead.sub_category_ids;
  form.received_date = newGeneralData.received_date;
  form.start_date = newGeneralData.start_date;
  form.estimated_comm = newGeneralData.estimated_comm;
  if (form.received_date) {
    form.received_date = dayjs(form.received_date).format("YYYY-MM-DD");
  }
  if (form.start_date && form.start_date !== "Invalid date") {
    form.start_date = dayjs(form.start_date).format("YYYY-MM-DD");
  } else {
    form.start_date = null;
  }
}

function getPaymentNewInfo() {
  const paymentFormInfo = detailPaymentRef.value.getNewPaymentInfo();
  const keyList = [
    "finance_company",
    "finance_type",
    "finance_amount",
    "finance_approved_date",
    "finance_approved_no"
  ];
  keyList.map(key => {
    form[key] = paymentFormInfo[key];
  });
}

function updateGeneralAndPaymentNewInfo() {
  nextTick(() => {
    // if (activeName.value == "general") {
    getDetailGeneral();
    // }
    if (hasAuth("updatePayment")) {
      getPaymentNewInfo();
    }
    loading.value = true;
    const sendForm = sendFormFilter(form);
    delete sendForm.draft_leads_id;
    dialogLoading();
    orderControllerUpdate(form.id, sendForm)
      .then(() => {
        dialogPanel.value.close();
        loading.value = false;
        reloadOrderInfo();
      })
      .catch(() => {
        dialogPanel.value.close();
        loading.value = false;
      });
  });
}
async function updateOrderSpecs() {
  nextTick(() => {
    const specsInfo = detailSpecsRef.value.getNewInfo();
    loading.value = true;
    const sendForm = sendFormFilter(specsInfo);
    dialogLoading();
    orderControllerSaveSpec(sendForm)
      .then(_res => {
        dialogPanel.value.close();
        loading.value = false;
        // reloadOrderInfo();
      })
      .catch(() => {
        dialogPanel.value.close();
        loading.value = false;
      });
  });
}
async function toUpdate() {
  const specsInfo = detailSpecsRef.value.getNewInfo();
  //check comment
  let commentRequire = false;
  specsInfo.list.forEach(product => {
    if (product.spec.Extras && product.spec.Extras == "Yes" && !product.note) {
      commentRequire = true;
    }
  });
  if (commentRequire) {
    ElMessage.error("Some products need extra work, please add comments!");
    return;
  }
  if (
    ["general", "payment", "contracts", "orderSpecs"].includes(activeName.value)
  ) {
    await updateOrderSpecs();
    updateGeneralAndPaymentNewInfo();
  }
}

function hide() {
  dialogVisible.value = false;
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: orderDetailDialogRef.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function handleAction(actionName) {
  if (actionName == "cancel") {
    leadsCanceled();
  } else if (actionName == "createService") {
    emit("createService", form);
  }
}

function leadsCanceled() {
  const data = {
    id: form.id,
    type: "order",
    result: "cancelled",
    row: form
  };
  changeResultRef.value
    .show(data)
    .then(overAction => {
      if (overAction) {
        hide();
        promise.resolve();
      }
    })
    .catch(() => {
      hide();
    });
}

function initData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  subProductCategoryList.value = basicData["subProductCategoryList"];
  sourceList.value = basicData["sourceList"];
  subSourceList.value = basicData["subSourceList"];
  promotionList.value = basicData["promotionList"];
}
const changeTab = (_tab, _event) => {};
function reloadOrderInfo() {
  const _with = [
    "payments",
    "cm",
    "install",
    "productSpec",
    "productStaff",
    "lead.client.products",
    "lead.apptSetter",
    "lead.category",
    "lead.spokenTo",
    "lead.salesConsultant",
    "appointment.leads",
    "appointment.client",
    "supplierList.supplier",
    "contracts",
    "invoice",
    "productFiles",
    "trade"
  ];
  dialogLoading();
  orderControllerShow(form.id, _with)
    .then(res => {
      const { data } = res;
      if (data) {
        defaultDataHandle(data);
      }
      dialogPanel.value.close();
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}
// to reset result Cascade element
function handleResultClose(isShow) {
  if (!isShow) {
    changeResultValue.value = currentTagResult.value;
    cascaderKey.value = Math.floor(Math.random() * 100);
    showCascader.value = false;
    nextTick(() => {
      showCascader.value = true;
    });
  }
}

// down invoice file
function downInvoice() {
  InvoiceDownDialogRef.value.show(form).then(data => {
    console.log(data);
    if (data && data.type) {
      const id = form.id;
      downInvoiceLoading.value = true;
      orderControllerGeneratorPDF(id, data)
        .then(res => {
          downInvoiceLoading.value = false;
          if (res.success && res.data) {
            window.open(res.data.url, "_blank");
            ElMessage.success(res.message);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_res => {
          downInvoiceLoading.value = false;
          ElMessage.error("Failed to generate invoice!");
        });
    }
  });
}

// Download Satisfaction Report
function downSatisfaction() {
  const id = form.id;
  downSatisfactionLoading.value = true;
  orderControllerGeneratorSatisfactionPDF(id, {})
    .then(res => {
      downSatisfactionLoading.value = false;
      if (res.success && res.data) {
        window.open(res.data.url, "_blank");
        ElMessage.success(res.message);
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(_res => {
      console.log("_res", _res);
      downSatisfactionLoading.value = false;
      ElMessage.error("Failed to generate satisfaction report!");
    });
}

// Download Satisfaction Report
function downloadWarrantyCard() {
  const id = form.id;
  downloadWarrantyCardLoading.value = true;
  orderControllerGeneratorWarrantyCardPDF(id, {})
    .then(res => {
      downloadWarrantyCardLoading.value = false;
      if (res.success && res.data) {
        window.open(res.data.url, "_blank");
        ElMessage.success(res.message);
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(_res => {
      console.log(_res);
      downloadWarrantyCardLoading.value = false;
      ElMessage.error("Failed to generate satisfaction report!");
    });
}

onMounted(() => {
  initData();
});
defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="Order Details"
    :close-on-click-modal="false"
    width="90%"
    ref="orderDetailDialogRef"
    class="no-footer-dialog order-detail-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="detail-header">
        <div class="left">
          <div class="level-1">
            Order:<span class="ml-1">{{ form.lead_id }}</span>
          </div>
          <div class="sub-title mr-2">
            <span class="mr-1 iconfont just-icon-client" />
            <span v-if="form.lead && form.lead.client"
              >{{ form.lead.client.given_name }}
              {{ form.lead.client.surname }}</span
            >
          </div>
          <div class="sub-title mr-2">
            <span class="mr-1 iconfont just-icon-product" />
            <span v-if="firstProduct">{{ firstProduct || "Empty" }}</span>
          </div>
          <div class="sub-title mr-2">
            <el-tag type="danger" effect="dark" v-if="form.extras == 'Yes'"
              >Extras required</el-tag
            >
          </div>

          <div class="sub-title mr-2">
            <el-tag
              type="danger"
              effect="dark"
              v-if="
                form.electrician &&
                form.lead.category &&
                form.lead.category.name.indexOf('Blinds') !== fale
              "
              >Need Electrician</el-tag
            >
          </div>
        </div>
        <div class="action-box" v-if="form.result !== 'cancelled' && editPage">
          <el-dropdown trigger="click">
            <el-button
              type="info"
              :loading="
                downInvoiceLoading ||
                downSatisfactionLoading ||
                downloadWarrantyCardLoading
              "
              class="ml-2"
            >
              <i
                class="mr-2 cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
              />
              Download
              <i
                class="ml-2 cursor-pointer iconfont just-icon-arrow-down text-[#4F5762]"
              />
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="downInvoice">
                  Download Invoice
                </el-dropdown-item>

                <el-dropdown-item @click="downSatisfaction"
                  >Download Customer Satisfaction Slip</el-dropdown-item
                >
                <el-dropdown-item @click="downloadWarrantyCard"
                  >Download Warranty Card</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <div v-if="form.result !== 'completion'">
            <el-button
              type="primary"
              :loading="loading"
              @click="toUpdate"
              class="ml-2"
              v-if="activeName == 'general' && canEdit"
            >
              {{ transformI18n("buttons.hssave") }}</el-button
            >
            <el-button
              type="primary"
              :loading="loading"
              @click="toUpdate"
              class="ml-2"
              v-else-if="activeName == 'payment' && hasAuth('updatePayment')"
            >
              {{ transformI18n("buttons.hssave") }}</el-button
            >
            <el-button
              type="primary"
              :loading="loading"
              @click="toUpdate"
              class="ml-2"
              v-else-if="activeName == 'orderSpecs' && hasAuth('orderSpec')"
            >
              {{ transformI18n("buttons.hssave") }}</el-button
            >
          </div>

          <!-- change result button -->
          <div
            class="resultChangeChange ml-2"
            v-if="editPage && resultOptionHandled.length"
          >
            <span
              :class="
                'mr-1 iconfont just-icon-' +
                getCurrentResultIcon(changeResultValue)
              "
            />
            <el-cascader
              :options="resultOptionHandled"
              :show-all-levels="false"
              :model-value="changeResultValue"
              @change="value => handleResultChange(value, form)"
              :props="resultCascaderProps"
              :disabled="form.result === 'cancelled'"
              @visible-change="isShow => handleResultClose(isShow)"
              :key="cascaderKey"
              v-if="showCascader"
            >
              <template #default="{ data }">
                <span class="cascader-label">
                  <span
                    :class="'mr-1 iconfont just-icon-' + data.icon"
                    v-if="data.icon"
                  />
                  {{ data.label }}</span
                >
              </template>
            </el-cascader>
          </div>

          <el-dropdown
            trigger="click"
            class="ml-2"
            v-if="
              form.result !== 'cancelled' &&
              currentTableAction.length &&
              editPage
            "
          >
            <span class="iconfont just-icon-gengduo" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  @click.stop="handleAction(actionItem.name)"
                  v-for="(actionItem, actionIndex) in currentTableAction"
                  :key="actionIndex"
                >
                  {{ actionItem.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="detail-content">
        <div class="detail-info">
          <el-tabs
            v-model="activeName"
            class="order-tabs"
            @tab-click="changeTab"
          >
            <el-tab-pane label="General" name="general">
              <DetailGeneral
                ref="detailGeneralRef"
                :allStaff="allStaff"
                :orderInfo="form"
                :editPage="editPage"
              />
            </el-tab-pane>
            <el-tab-pane
              label="Payment"
              name="payment"
              v-if="hasAuth('viewPayment')"
            >
              <DetailPayment
                ref="detailPaymentRef"
                :allStaff="allStaff"
                :orderInfo="form"
                :editPage="editPage"
                @reloadOrderInfo="reloadOrderInfo"
              />
            </el-tab-pane>
            <el-tab-pane
              label="Order Specs"
              name="orderSpecs"
              v-if="hasAuth('orderSpec')"
            >
              <DetailSpecs
                ref="detailSpecsRef"
                :orderInfo="form"
                :editPage="editPage"
                @reloadOrderInfo="reloadOrderInfo"
              />
            </el-tab-pane>
            <el-tab-pane label="Contracts and installation" name="contracts">
              <DetailInstallation
                ref="detailInstallationRef"
                :orderInfo="form"
                :editPage="editPage"
                @reloadOrderInfo="reloadOrderInfo"
              />
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="notes-box">
          <LeadsNote
            ref="leadsNoteRef"
            v-if="dialogVisible"
            :id="form.id"
            :type="'order'"
            :result="form.result"
            :row="form"
            :canCreateAppointment="canCreateAppointment && editPage"
            @updateDetail="reloadOrderInfo"
          />
        </div>
      </div>
    </el-form>
    <ChangeResult ref="changeResultRef" />
    <InvoiceDownDialog ref="InvoiceDownDialogRef" />
  </el-dialog>
</template>

<style>
.order-detail-dialog {
  overflow: auto;
}

.order-detail-dialog .el-dialog__body .detail-info {
  min-width: 960px;
  max-width: 95vw;
  overflow: auto;
}
</style>
<style lang="scss" scoped>
.help-block {
  font-size: 12px;
  color: #999;
}

.form-item-with {
  width: 80%;
}

.detail-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 50px;
  padding: 0 15px;
  border-bottom: 1px solid #e9ebf0;

  .left {
    display: flex;
    align-items: center;
  }

  .sub-title {
    margin-left: 10px;
  }
}

.detail-content {
  display: flex;
  justify-content: space-between;

  .detail-info {
    flex-grow: 1;
    padding: 0 20px 30px;
    overflow: auto;
  }

  .notes-box {
    flex-shrink: 0;
    width: 450px;
    background: #fff;
    border: 1px solid #e9ebf0;
  }

  .item-block {
    padding: 0 0 20px;
    margin-top: 20px;
    border: 0;
    border-bottom: 1px solid #e9ebf0;

    &:last-child {
      border-bottom: none;
    }
  }

  .item-box {
    height: 50px;
    margin: 20px 0 0;

    .item-value {
      line-height: 34px;
    }
  }
}

.input-result-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.edit-client {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;

  .editIcon {
    display: none;
    margin-right: 10px;
  }

  &:hover {
    .editIcon {
      display: block;
    }
  }
}

.action-box {
  display: flex;
  align-items: center;
}

.noEdit {
  cursor: not-allowed;
}

@media screen and (width <= 1200px) {
  .order-detail-dialog .el-dialog__body {
    .detail-content {
      display: block;

      .detail-info {
        width: 100%;
        min-width: 100%;
      }

      .notes-box {
        width: 100%;
        min-width: 100%;
      }
    }
  }
}

@media screen and (width <= 768px) {
  .order-detail-dialog .el-dialog__body {
    .detail-header {
      display: block;
      height: auto;
      padding-bottom: 10px;

      .left {
        flex-wrap: wrap;
        margin-bottom: 10px;
      }

      .action-box {
        flex-wrap: wrap;
        justify-content: right;

        .el-button {
          margin-bottom: 5px;
        }
      }
    }
  }
}

@media screen and (width <= 480px) {
  .order-detail-dialog .el-dialog__body {
    .detail-info {
      padding: 10px 15px;
    }

    .notes-box {
      padding: 10px;
    }
  }
}

@media screen and (width <= 375px) {
  .order-detail-dialog .el-dialog__body {
    .detail-info {
      padding: 5px 10px;
    }
  }
}
</style>
