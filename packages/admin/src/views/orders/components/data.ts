import { transformI18n } from "@/plugins/i18n";

import blinds from "@/assets/image/blinds.png";
import garageDoor from "@/assets/image/garageDoor.png";
import landscaping from "@/assets/image/landscaping.png";
import roof from "@/assets/image/roof.png";
export { roof, blinds, garageDoor, landscaping };
export const indexDefaultQuery = {
  search: "",
  sort: ["-id"],
  filter: [],
  _with: [],
  withCount: []
};

export const columns: TableColumnList = [
  {
    label: "Order ID",
    width: "120",
    prop: "lead_id",
    fixed: true,
    slot: "id",
    sortable: true
  },
  {
    label: "Extras",
    prop: "extras",
    width: "120"
  },
  {
    label: "Reason",
    prop: "reason",
    width: "200"
  },
  {
    label: "Client",
    prop: "lead.client.given_name",
    slot: "client",
    minWidth: "200",
    sortable: true
  },
  {
    label: "Phone",
    prop: "lead.client.phone",
    minWidth: "160",
    slot: "phone",
    sortable: true
  },
  {
    label: "Email",
    prop: "lead.client.email",
    minWidth: "140",
    slot: "email",
    sortable: true
  },
  {
    label: "Address",
    prop: "lead.client.address",
    minWidth: "120",
    slot: "address",
    sortable: true
  },
  {
    label: "CM Received Date",
    prop: "cm_received_date",
    slot: "cm_received_date",
    minWidth: "130",
    sortable: true
  },
  {
    label: "Received Date",
    prop: "received_date",
    slot: "received_date",
    minWidth: "130",
    sortable: true
  },
  {
    label: "Category",
    prop: "lead.category.name",
    slot: "category",
    minWidth: "140",
    sortable: true
  },
  {
    label: "Product",
    prop: "product.name",
    slot: "product",
    minWidth: "120",
    sortable: true
  },
  {
    label: "Existing Product",
    slot: "existingProduct",
    minWidth: "160",
    sortable: false
  },
  {
    label: "Quantity",
    prop: "quantity",
    slot: "quantity",
    minWidth: "90"
  },
  {
    label: "Sales Manager",
    prop: "lead.sales.login_name",
    slot: "sales_manager",
    minWidth: "150",
    sortable: true
  },
  {
    label: "SalesConsultant",
    prop: "lead.sales_consultant.login_name",
    slot: "salesConsultant",
    width: "150",
    sortable: true
  },
  {
    label: "Retail",
    prop: "lead.retail",
    slot: "retail",
    minWidth: "120",
    sortable: true
  },
  {
    label: "Sold Amount",
    prop: "lead.sold",
    slot: "sold",
    minWidth: "120",
    sortable: true
  },
  {
    label: "Production Staff",
    prop: "productStaff.login_name",
    slot: "production_staff",
    minWidth: "150",
    sortable: true
  },
  {
    label: "Install Staff",
    prop: "install.login_name",
    slot: "install_staff",
    minWidth: "150",
    sortable: true
  },
  {
    label: "CMer",
    prop: "cm.name",
    slot: "cm",
    minWidth: "150",
    sortable: true
  },
  {
    label: "CM Booked Date",
    prop: "cm_booked_date",
    minWidth: "145",
    slot: "cm_booked_date",
    sortable: true
  },
  {
    label: "On Production Date",
    prop: "on_product_date",
    slot: "production_date",
    minWidth: "155",
    sortable: true
  },
  {
    label: "Ready Date",
    prop: "ready_date",
    slot: "ready_date",
    minWidth: "130",
    sortable: true
  },
  {
    label: "Installation Date",
    prop: "installation_date",
    slot: "installation_date",
    minWidth: "140",
    sortable: true
  },
  {
    label: "Hold Time",
    prop: "hold_time",
    slot: "hold_time",
    minWidth: "120",
    sortable: true
  },
  {
    label: "Council days",
    prop: "submit_date",
    slot: "submit_date",
    minWidth: "120",
    sortable: true
  },
  {
    label: "Decision",
    width: "120",
    slot: "decision",
    sortable: false
  },
  {
    label: "Completion Date",
    prop: "completion_date",
    slot: "completion_date",
    minWidth: "140",
    sortable: true
  },
  {
    label: "Supplier days",
    slot: "supplier_days",
    minWidth: "120"
  },
  {
    label: "Remaining Balance",
    prop: "remainingBalance",
    slot: "remainingBalance",
    minWidth: "130"
  },
  {
    label: "Order Result",
    prop: "result",
    slot: "result",
    minWidth: "160"
  },
  {
    label: "Followup Date",
    prop: "followup_date",
    slot: "followup_date",
    minWidth: "140"
  },
  {
    label: "From old",
    prop: "from_old",
    width: "120"
  },
  {
    label: "Need Electrician",
    slot: "electrician",
    width: "120"
  },
  {
    label: "Ready",
    prop: "ready",
    slot: "ready",
    width: "60"
  },
  {
    label: "Update At",
    prop: "updated_at",
    width: "120",
    slot: "updated_at",
    sortable: true
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    prop: "operation",
    slot: "operation",
    align: "center"
  }
];

export const leadsColumns: TableColumnList = [
  {
    label: "Leads ID",
    width: "100",
    prop: "id",
    fixed: true,
    sortable: true
  },
  {
    label: "Reason",
    prop: "reason",
    width: "120"
  },
  {
    label: "Client",
    prop: "client.given_name",
    slot: "client",
    minWidth: "140",
    sortable: true
  },
  {
    label: "Phone",
    prop: "client.phone",
    minWidth: "120",
    slot: "phone",
    sortable: true
  },
  {
    label: "Email",
    prop: "client.email",
    minWidth: "140",
    slot: "email",
    sortable: true
  },
  {
    label: "Address",
    prop: "client.address",
    minWidth: "120",
    slot: "address",
    sortable: true
  },
  {
    label: "Date of enquiry",
    prop: "enquiry_date",
    minWidth: "130",
    slot: "enquiryDate",
    sortable: true
  },
  {
    label: "Category",
    prop: "category.name",
    slot: "category",
    minWidth: "140",
    sortable: true
  },
  {
    label: "Product",
    prop: "product",
    slot: "product",
    minWidth: "110"
  },
  {
    label: "Sales Manager",
    prop: "sales.login_name",
    slot: "salesManager",
    minWidth: "150",
    sortable: true
  },
  {
    label: "SalesConsultant",
    prop: "sales_consultant.login_name",
    slot: "salesConsultant",
    width: "160",
    sortable: true
  },
  {
    label: "Retail",
    prop: "retail",
    slot: "retail",
    sortable: true
  },
  {
    label: "Quoted Amount",
    prop: "quoted",
    minWidth: "140",
    slot: "quoted",
    sortable: true
  },
  {
    label: "Sold Amount",
    prop: "sold",
    minWidth: "120",
    slot: "sold",
    sortable: true
  },
  {
    label: "Followup Time",
    prop: "followup_time",
    slot: "followup_time",
    minWidth: "140",
    sortable: true
  },
  {
    label: "Reason",
    prop: "reason",
    minWidth: "140"
  },
  {
    label: "Appt Date",
    minWidth: "120",
    prop: "appt_date",
    slot: "appt_date",
    sortable: true
  },
  {
    label: "Sold Date",
    prop: "sold_date",
    slot: "sold_date",
    width: "120",
    sortable: true
  },
  {
    label: "Leads Result",
    prop: "result",
    slot: "result",
    width: "160"
  },
  {
    label: "From old",
    prop: "from_old",
    width: "120"
  },
  {
    label: "Update At",
    prop: "updated_at",
    width: "120",
    sortable: true
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    prop: "operation",
    slot: "operation",
    align: "center"
  }
];

// the key require same with result value
export const showFields = {
  review: [
    "id",
    "reason",
    "client.given_name",
    "client.phone",
    "client.address",
    "category.name",
    "product",
    "enquiry_date",
    "sales_consultant.login_name",
    "retail",
    "sold",
    "sold_date",
    "result",
    "updated_at",
    "operation"
  ],
  all: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "productStaff.login_name",
    "received_date",
    "product.name",
    "quantity",
    "lead.sold",
    "result",
    "updated_at",
    "operation"
  ],
  cm: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "productStaff.login_name",
    "product.name",
    "quantity",
    "lead.sold",
    "cm.name",
    "cm_booked_date",
    "result",
    "updated_at",
    "operation"
  ],
  cmReceived: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "productStaff.login_name",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "cm.name",
    "cm_received_date",
    "received_date",
    "result",
    "updated_at",
    "operation"
  ],
  onProduction: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "productStaff.login_name",
    "on_product_date",
    "supplier_days",
    "result",
    "updated_at",
    "operation"
  ],
  ready: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "productStaff.login_name",
    "ready_date",
    "followup_date",
    "result",
    "updated_at",
    "operation"
  ],
  installation: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "productStaff.login_name",
    "install_staff.login_name",
    "installation_date",
    "result",
    "updated_at",
    "operation"
  ],
  extra: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "productStaff.login_name",
    "remainingBalance",
    "result",
    "ready",
    "updated_at",
    "operation"
  ],
  outstanding: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "productStaff.login_name",
    "remainingBalance",
    "result",
    "updated_at",
    "operation"
  ],
  completion: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "productStaff.login_name",
    "received_date",
    "completion_date",
    "result",
    "updated_at",
    "operation"
  ],
  hold: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "productStaff.login_name",
    "hold_time",
    "result",
    "updated_at",
    "operation"
  ],
  inCouncil: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "productStaff.login_name",
    "hold_time",
    "submit_date",
    "result",
    "updated_at",
    "operation"
  ],
  cancelled: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "productStaff.login_name",
    "hold_time",
    "result",
    "updated_at"
  ],
  default: [
    "lead_id",
    "extras",
    "reason",
    "lead.client.given_name",
    "lead.client.phone",
    "lead.client.address",
    "lead.category.name",
    "quantity",
    "lead.sold",
    "product.name",
    "productStaff.login_name",
    "lead.sales_consultant.login_name",
    "result",
    "ready",
    "updated_at",
    "operation"
  ]
};

export const tableActionList = {
  review: [
    { label: "View Details", icon: "eye", name: "viewLeads" },
    { label: "Confirm to Order", icon: "convert", name: "convertToOrder" }
  ],
  new_order: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" },
    { label: "Create Appointment", icon: "add", name: "createAppt" }
  ],
  cmReceived: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" },
    { label: "Create Appointment", icon: "add", name: "createAppt" }
  ],
  onProduction: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" },
    { label: "Create Appointment", icon: "add", name: "createAppt" }
  ],
  ready: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" },
    { label: "Create Appointment", icon: "add", name: "createAppt" }
  ],
  outstanding: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" },
    { label: "Create Appointment", icon: "add", name: "createAppt" }
  ],
  extra: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" },
    { label: "Create Appointment", icon: "add", name: "createAppt" }
  ],
  completion: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Revoke Completed Order", icon: "revoke", name: "canRevokeOrder" },
    { label: "Create Service", icon: "add", name: "createService" }
  ],
  cancelled: [],
  default: [
    { label: "Edit", icon: "pencil", name: "edit" },
    { label: "Cancel", icon: "cancelled", name: "cancel" }
  ]
};

export const detailActionList = {
  new_order: [{ label: "Cancel", icon: "cancel", name: "cancel" }],
  ready: [{ label: "Cancel", icon: "cancel", name: "cancel" }],
  outstanding: [],
  completion: [
    { label: "Create Service", icon: "pencil", name: "createService" }
  ],
  cancelled: [],
  default: [{ label: "Cancel", icon: "cancel", name: "cancel" }]
};

export const allResults = [
  {
    value: "new_order",
    label: "New Order",
    icon: "new_order",
    disabled: true
  },
  {
    value: "cm",
    label: "CM",
    icon: "cm",
    disabled: true
  },
  {
    value: "cmReceived",
    label: "CM Received",
    icon: "cmReceived",
    disabled: true
  },
  {
    value: "onProduction",
    label: "On Production",
    icon: "onProduction",
    disabled: true
  },
  {
    value: "ready",
    label: "Ready",
    icon: "ready",
    disabled: true
  },
  {
    value: "installation",
    label: "Installation",
    icon: "installation",
    disabled: true
  },
  {
    value: "extra",
    label: "Extra",
    icon: "extra",
    disabled: true
  },
  {
    value: "outstanding",
    label: "Outstanding",
    icon: "outstanding",
    disabled: true
  },
  {
    value: "completion",
    label: "Completion",
    icon: "completion",
    disabled: true
  },
  {
    value: "hold",
    label: "On Hold",
    icon: "hold",
    disabled: true
  },
  {
    value: "inCouncil",
    label: "In Council",
    icon: "inCouncil",
    disabled: true
  }
  // {
  //   value: "cancelled",
  //   label: "Cancelled",
  //   icon: "cancelled",
  //   disabled: true
  // }
];

export const typeOption = [
  "Cash",
  "Cheque",
  "Credit",
  "CreditCard",
  "Direct Tsf",
  "Finn",
  "Finn Cost",
  "Refund",
  "Unpaid"
];

export function calculatedAmount(item) {
  const payments = item.payments;
  let paid_amount = 0;
  let outstanding = -1;
  paid_amount = payments.reduce((acc, cur) => {
    let curAmount = 0;
    if (cur.type == "Refund") {
      curAmount = -cur.amount;
    } else if (cur.type != "Unpaid") {
      curAmount = cur.amount;
    }
    return parseFloat(acc.toFixed(2)) + parseFloat(curAmount.toFixed(2));
  }, 0);
  if (item.lead) {
    outstanding =
      parseFloat(item.lead.sold.toFixed(2)) -
      parseFloat(paid_amount.toFixed(2));
  }
  return outstanding ? parseFloat(outstanding.toFixed(2)) : outstanding;
}

export const decisionOption = [
  "Submitted",
  "Planning consent",
  "Building Consent",
  "Development Approval"
];
