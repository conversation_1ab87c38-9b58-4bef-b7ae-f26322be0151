<script setup lang="ts">
import { transformI18n } from "@/plugins/i18n";
import { reactive, ref, onMounted, watch, nextTick } from "vue";
import { useCommonStoreHook } from "@/store/modules/common";
import {
  columns,
  leadsColumns,
  allResults,
  showFields,
  tableActionList,
  calculatedAmount
} from "./data.ts";
import { allResults as leadsAllResults } from "@/views/leads/components/leads_data.ts";
import { leadsControllerIndex } from "@/api/admin/leads";
import { orderControllerIndex } from "@/api/admin/order";
import DataTable from "@/components/DataTable";
import CirCleText from "@/components/CircleText";
import SearchIcon from "@/assets/svg/search.svg?component";
import EmptySpan from "@/components/EmptySpan";
import dayjs from "dayjs";
import ChangeResult from "@/components/ChangeResult/ChangeResult.vue";
import CreateAppointment from "@/components/CreateAppointment/CreateAppointment.vue";
import ResultDialog from "@/components/ResultDialog";
import { ElMessageBox, ElMessage } from "element-plus";
import { hasAuth } from "@/router/utils";
import { formatTime } from "@/utils/form";
import { storageLocal } from "@pureadmin/utils";
import {
  removeElseFromObjArr,
  handleAddress,
  getSupplierReceivedDays,
  getDaysFromToday
} from "@/utils/common";
import { orderControllerRevokeOrder } from "@/api/admin/order";
const productCategoryList = ref([]);
const currentCompanyRegion = ref([]);

const resultDialogRef = ref(null);
const props = defineProps({
  args: {
    type: Object,
    default: () => ({})
  },
  loadDataFlag: {
    type: String,
    default: null
  }
});
onMounted(() => {
  initData();
  initSearchData();
});
// Listen for changes in props properties
watch(
  () => props.args,
  newValue => {
    if (newValue) {
      initData();
    }
  }
);
watch(
  () => props.loadDataFlag,
  (_newValue, _oldValue) => {
    loadData();
  }
);
const resultCascaderProps = {
  checkStrictly: true,
  emitPath: true
};
const baseForm = {
  product_category_id: null,
  date: null,
  followup_date: null,
  otherParam: {
    clientKeyWords: "",
    subCategoryIds: null,
    reasonKeyWords: "",
    staff: null
  }
};
const indexQuery = reactive({
  search: "",
  sort: "-id",
  filter: "",
  _with: "sales,source,category,spokenTo",
  withCount: ""
});
const form = reactive(baseForm);
const formRef = ref(null);
const dataTableRef = ref(null);
const currentTagResult = ref(null);
const mv = ref();
const dialogPanel = ref();
const changeResult = ref(null);
const createAppointment = ref(null);
const newColumns = ref([...columns]);
const currentShowFields = reactive(JSON.parse(JSON.stringify(showFields)));
const showTable = ref(false);
const cascaderKey = ref([]); // use to reflash current result cascader
const showCascader = ref([]);
const slotNamesList = ref([]);

const emit = defineEmits<{
  (e: "toPassNewInfo", val: Object): void;
  (e: "toDetail", val: Object): void;
  (e: "convertToOrder", val: Object): void;
  (e: "createService", val: Object): void;
  (e: "updateListSummary"): void;
}>();

function initData() {
  currentTagResult.value = props.args.tabName;
  currentCompanyRegion.value = props.args.companyRegion;

  // get list filter info
  const lastFilterParam = storageLocal().getItem(
    `orderfilter_${currentTagResult.value}`
  );
  if (lastFilterParam && lastFilterParam["form"]) {
    Object.assign(form, lastFilterParam["form"]);
  }
  if (lastFilterParam && lastFilterParam["search"]) {
    indexQuery.search = lastFilterParam["search"];
  }

  if (currentTagResult.value === "review") {
    slotNamesList.value = [
      "id",
      "client",
      "phone",
      "email",
      "address",
      "category",
      "product",
      "enquiryDate",
      "sales_manager",
      "salesConsultant",
      "retail",
      "quoted",
      "sold",
      "result",
      "sold_date",
      "appt_date",
      "followup_time",
      "operation",
      "existingProduct",
      "updated_at"
    ];
  } else {
    slotNamesList.value = [
      "id",
      "client",
      "phone",
      "email",
      "address",
      "category",
      "product",
      "sales_manager",
      "salesConsultant",
      "retail",
      "sold",
      "production_staff",
      "install_staff",
      "cm",
      "production_date",
      "ready_date",
      "installation_date",
      "cm_booked_date",
      "hold_time",
      "remainingBalance",
      "result",
      "operation",
      "supplier_days",
      "cm_received_date",
      "received_date",
      "appt_date",
      "followup_time",
      "followup_date",
      "sold_date",
      "quantity",
      "completion_date",
      "electrician",
      "submit_date",
      "ready",
      "existingProduct",
      "updated_at"
    ];
  }
  showTable.value = false;
  getTableColumns();
  showTable.value = true;
}

function getTableColumns() {
  if (currentTagResult.value === "review") {
    newColumns.value = JSON.parse(JSON.stringify(leadsColumns));
  } else {
    newColumns.value = JSON.parse(JSON.stringify(columns));
  }
  // Initialize show fields data
  const lastOrderShowFields = storageLocal().getItem("orderLeadsShowFields");
  if (lastOrderShowFields) {
    Object.assign(currentShowFields, lastOrderShowFields);
  }

  let checkShowFields = JSON.parse(JSON.stringify(showFields))["default"];
  if (currentShowFields[currentTagResult.value]) {
    checkShowFields = currentShowFields[currentTagResult.value];
  }
  newColumns.value = newColumns.value.map(column => {
    let hide = false;
    hide = !checkShowFields.includes(String(column.prop));
    return { ...column, hide };
  });
}

// table column show field change handle:storage the change
function dynamicColumnsChange(value) {
  if (value && value.dynamicColumns && currentTagResult.value) {
    const newShowFields = [];
    value.dynamicColumns.map(item => {
      if (!item.hide) {
        newShowFields.push(item.prop);
      }
    });
    currentShowFields[currentTagResult.value] = newShowFields;
    storageLocal().setItem("orderLeadsShowFields", currentShowFields);
  }
}

// Get the current row  operation todo
function getActionList(result, row) {
  let actionList = [];
  if (currentTagResult.value === "review") {
    actionList = tableActionList["review"];
  } else if (result && tableActionList[result]) {
    actionList = tableActionList[result];
  } else if (!result) {
    return [];
  } else {
    actionList = tableActionList["default"];
  }
  if (!actionList.length) {
    return [];
  }
  // Filter actions by permission
  const removeList = [];
  const canCancel = hasAuth("cancelOrder");
  const canCreateService = hasAuth("createService");
  const canConvertToOrder = hasAuth("convertToOrder");
  const canRevokeOrder = hasAuth("canRevokeOrder");
  let canAddOrderAppointment = hasAuth("addOrderAppointment");
  if (canAddOrderAppointment) {
    canAddOrderAppointment =
      row.appointment && row.appointment.length ? false : true;
  }
  if (!canCancel) {
    removeList.push("cancel");
  }
  if (!canCreateService) {
    removeList.push("createService");
  }
  if (!canAddOrderAppointment) {
    removeList.push("createAppt");
  }
  if (!canConvertToOrder) {
    removeList.push("convertToOrder");
  }
  if (!canRevokeOrder) {
    removeList.push("canRevokeOrder");
  }
  const canCompletion = hasAuth("changeOrderResult");
  if ((row.lead && row.lead.sold != row.paid_amount) || !canCompletion) {
    removeList.push("completion");
  }
  if (!canCompletion) {
    removeList.push("completion");
  }

  if (removeList.length) {
    actionList = removeElseFromObjArr(actionList, removeList);
  }
  return actionList;
}

function getListApiName() {
  if (props.args.tabName === "review") {
    return leadsControllerIndex;
  } else {
    return orderControllerIndex;
  }
}

function onSearch() {
  loadData();
}

function onReset() {
  indexQuery.search = "";
  Object.keys(form).forEach(key => {
    switch (key) {
      case "otherParam":
        form[key] = {
          clientKeyWords: "",
          subCategoryIds: null,
          reasonKeyWords: "",
          staff: null
        };
        break;
      default:
        form[key] = null;
        break;
    }
  });
  loadData();
}

function loadData() {
  // Cache filter info
  const filterParam = {
    form: form,
    search: indexQuery.search
  };
  storageLocal().setItem(`orderfilter_${currentTagResult.value}`, filterParam);

  dataTableRef.value.loadData();
}

function tableQueryParams(form) {
  const filterInfo = [];
  let subCategoryIds = null;
  if (form.otherParam && form.otherParam.subCategoryIds) {
    subCategoryIds = form.otherParam.subCategoryIds.join(",");
  }
  Object.keys(baseForm).forEach(key => {
    if (form[key]) {
      if (key == "date") {
        const startDate = dayjs(form.date[0]).format("YYYY-MM-DD HH:mm:ss");
        const endDate = dayjs(form.date[1]).format("YYYY-MM-DD 23:59:59");
        const dateName =
          props.args?.tabName != "review" ? "received_date" : "enquiry_date";
        filterInfo.push(`${dateName}:gte:${startDate}`);
        filterInfo.push(`${dateName}:lte:${endDate}`);
      } else if (key == "followup_date") {
        const startDate = dayjs(form.followup_date[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        const endDate = dayjs(form.followup_date[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
        filterInfo.push(`followup_date:gte:${startDate}`);
        filterInfo.push(`followup_date:lte:${endDate}`);
      } else if (key != "product" && key != "otherParam") {
        filterInfo.push(`${key}:eq:` + form[key]);
      }
    }
  });

  if (props.args?.tabName == "review") {
    filterInfo.push("result:eq:sold");
    filterInfo.push(`order_id:null`);
  } else if (props.args?.tabName != "all") {
    filterInfo.push(`result:eq:${props.args?.tabName}`);
  }
  if (props.args?.companyRegion?.length) {
    filterInfo.push("company_region:in:" + props.args?.companyRegion.join("|"));
  }
  if (props.args?.tabName == "review") {
    indexQuery._with =
      "sales,source,category,spokenTo,client.products,apptSetter,saleAppointment.client,saleAppointment.leads,salesConsultant,contracts";
  } else {
    indexQuery._with =
      "payments,cm,install,productSpec,productStaff,lead.client.products,lead.category,lead.apptSetter,lead.spokenTo,lead.sales, appointment.leads, appointment.client, supplierList.supplier, contracts, invoice, productFiles,lead.service,product";
  }

  const param = Object.assign(
    { ...indexQuery },
    {
      filter: filterInfo.length === 0 ? indexQuery.filter : filterInfo.join(),
      otherParam: JSON.stringify({
        clientKeyWords: form.otherParam.clientKeyWords,
        subCategoryIds: subCategoryIds,
        reasonKeyWords: form.otherParam.reasonKeyWords,
        staff: form.otherParam.staff
      })
    }
  );
  return param;
}

function initSearchData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
}

// action processing
function handleAction(row, actionName) {
  if (actionName == "viewLeads") {
    emit("toDetail", row);
  } else if (actionName == "edit") {
    emit("toDetail", row);
  } else if (actionName == "convertToOrder") {
    emit("convertToOrder", row);
  } else if (actionName == "createAppt") {
    createdAppointment(row);
  } else if (actionName == "cancel") {
    closeOrder(row);
  } else if (actionName == "createService") {
    emit("createService", row);
  } else if (actionName == "completion") {
    handleResultChange(["completion"], row);
  } else if (actionName == "canRevokeOrder") {
    revokeOrder(row);
  }
  // to do
}

function createdAppointment(row) {
  if (row.appointment.length) {
    ElMessageBox.confirm(
      `There is already an <strong>outstanding</strong> you can modify it for this order?`,
      transformI18n("buttons.hsSystemPrompts"),
      {
        confirmButtonText: transformI18n("buttons.hsConfirm"),
        cancelButtonText: transformI18n("buttons.hsCancel"),
        type: "warning",
        dangerouslyUseHTMLString: true,
        draggable: true
      }
    ).then(() => {
      createAppointment.value
        .show(row.appointment[0], "edit")
        .then(() => {
          loadData();
          emit("updateListSummary");
        })
        .catch(() => {
          dialogPanel.value.close();
        });
    });
  } else {
    createAppointment.value
      .show(row, "order")
      .then(() => {
        loadData();
        emit("updateListSummary");
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  }
}

function revokeOrder(row) {
  orderControllerRevokeOrder(row.id).then(() => {
    loadData();
    emit("updateListSummary");
  });
}
function closeOrder(row) {
  const data = {
    id: row.id,
    type: "order",
    result: "cancelled",
    row
  };
  changeResult.value
    .show(data)
    .then(() => {
      loadData();
      emit("updateListSummary");
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}

// Change current row result (todo)
function handleResultChange(value, row, index = null) {
  if (currentTagResult.value === "review") {
    handleLeadsResultChange(value, row);
  } else {
    // If the remaining balance is changed from outstanding to completion, the remaining balance must be 0
    if (value == "completion" && calculatedAmount(row) !== 0) {
      const info = transformI18n("common.resultCompletionInfo");
      ElMessage.error(info);
      // to reset result cascader element
      if (index) {
        handleResultClose(false, index);
      }
      return;
    }
    handleOrderResultChange(value, row);
  }
}

function handleLeadsResultChange(value, row) {
  const hasSonResult = ["followup", "quoted", "cancelled"];
  if (hasSonResult.includes(value[0]) && !value[1]) {
    return false;
  }
  const result = value[0];
  const subResult = value[1];
  const data = {
    id: row.id,
    type: "leads",
    result: result,
    sub_result: subResult
  };
  changeResult.value
    .show(data)
    .then(() => {
      loadData();
      emit("updateListSummary");
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}

function handleOrderResultChange(result, row) {
  const data = {
    id: row.id,
    type: "order",
    result: result[0],
    row: row
  };
  changeResult.value
    .show(data)
    .then(() => {
      loadData();
      emit("updateListSummary");
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}

function stopBubbling() {
  return;
}

//  to update table list new info (total)
function toPassNewInfo(newInfo) {
  cascaderKey.value = generateUniqueRandomArray(newInfo.pageSize);
  showCascader.value = Array(newInfo.pageSize).fill(true);
  emit("toPassNewInfo", newInfo);
}

/** row click handlde */
function onRowClick(row) {
  if (!row) {
    return;
  }
  emit("toDetail", row);
}

// get current row result icon
function getCurrentResultIcon(result) {
  if (!result) {
    return;
  }
  let resultAllList = [
    ...allResults,
    {
      value: "cancelled",
      label: "Cancelled",
      icon: "cancelled"
    }
  ];
  if (currentTagResult.value === "review") {
    resultAllList = leadsAllResults;
  }
  if (typeof result === "string") {
    return resultAllList.find(item => item.value === result)?.icon;
  } else if (result instanceof Array) {
    return resultAllList.find(item => item.value === result[0])?.icon;
  }
}

function getResultOptions(row) {
  const result = row.result;
  const lastResult = row.last_result;
  const HoldCanConvertList = [
    "new_order",
    "cmReceived",
    "onProduction",
    "ready"
  ];
  const resultsListNoHandle = JSON.parse(JSON.stringify(allResults));
  //Change the result disabled property based on the role
  const resultsListHandle = [];
  let canToResultsList = [];
  if (hasAuth("changeOrderResult")) {
    switch (result) {
      case "new_order":
        canToResultsList = ["onProduction", "hold", "inCouncil"];
        break;
      case "cm":
        canToResultsList = ["new_order", "inCouncil"];
        break;
      case "cmReceived":
        canToResultsList = ["onProduction", "hold", "inCouncil"];
        break;
      case "onProduction":
        canToResultsList = ["ready", "hold", "inCouncil"];
        break;
      case "ready":
        canToResultsList = ["onProduction", "hold", "inCouncil"];
        break;
      case "installation":
        canToResultsList = [""];
        break;
      case "outstanding":
        canToResultsList = ["completion"];
        break;
      case "hold":
        if (lastResult && HoldCanConvertList.includes(lastResult)) {
          canToResultsList = [lastResult];
        }
        break;
      case "inCouncil":
        // if (lastResult && HoldCanConvertList.includes(lastResult)) {
        canToResultsList = ["onProduction"];
        // }
        break;
    }
  }
  resultsListNoHandle.map(resultItem => {
    if (
      canToResultsList.length &&
      canToResultsList.includes(resultItem.value)
    ) {
      resultItem.disabled = false;
    } else {
      resultItem.disabled = true;
    }
    resultsListHandle.push(resultItem);
  });
  return resultsListHandle;
}

function handleResultClose(isShow, index) {
  if (!isShow) {
    cascaderKey.value[index] = Math.floor(Math.random() * 100);
    showCascader.value[index] = false;
    nextTick(() => {
      showCascader.value[index] = true;
    });
  }
}

function generateUniqueRandomArray(length: number): number[] {
  const arr: number[] = [];

  while (arr.length < length) {
    const randomNum = Math.floor(Math.random() * 100); // 生成随机数，此处范围是 0 到 99，根据需要进行调整
    if (!arr.includes(randomNum)) {
      arr.push(randomNum);
    }
  }
  return arr;
}

// table list order change
function sortChange(column) {
  const order = column.order === "ascending" ? "" : "-";
  const orderField = column.prop;
  indexQuery.sort = order + orderField;
  loadData();
}

function handleSearch(val) {
  indexQuery.search = val;
  loadData();
}

function getHandleDate(date, row) {
  if (
    currentTagResult.value === "review" ||
    row.result == "completion" ||
    row.result == "cancelled"
  ) {
    return dayjs(date).format("DD/MM/YYYY");
  }
  return getDaysFromToday(date) + "d";
}

// Roof 为Roof area， Turf 为Site area。其他为件数.
function getQuantity(row) {
  return row.qty;
  // if (!(row.lead.category && row.product_spec && row.product_spec.length)) {
  //   return 0;
  // }
  // let num = 0;
  // if (row.lead.category.name == "Roof") {
  //   const productSpec = row.product_spec[0] && row.product_spec[0].spec;
  //   num = productSpec["RoofArea(m2)"];
  // } else if (row.lead.category.name == "Landscaping") {
  //   const productSpec = row.product_spec[0] && row.product_spec[0].spec;
  //   num = productSpec["Site area(m2)"];
  // } else {
  //   num = row.product_spec.length;
  // }
  // return num;
}

// add table summary row
function getSummaries(param, summaryData) {
  const { columns } = param;
  const sums = columns.map((column, index) => {
    if (index === 0) {
      return "Total";
    }
    if (index === 1) {
      return `Quantity:
      ${summaryData.totalCount || 0}`;
    }
    if (index === 2) {
      return `Units: ${summaryData.totalUnit.toFixed(2) || 0}`;
    }
    if (index === 3) {
      return `Amount: $${
        summaryData.totalSold.toLocaleString("en-US", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }) || 0
      }`;
    }
    return ""; // For columns without specific handling
  });

  return sums;
}
defineExpose({ loadData });
</script>
<template>
  <div ref="mv" class="leads-list-page">
    <div class="flex justify-between w-full h-[50px] list-form">
      <el-form ref="formRef" :inline="true" :model="form">
        <el-form-item :label="'Product:'" prop="product" class="!w-[240px]">
          <el-select
            v-model="form.otherParam.subCategoryIds"
            placeholder="Select"
            filterable
            clearable
            multiple
            collapse-tags
            collapse-tags-tooltip
            @change="onSearch()"
          >
            <el-option-group
              v-for="group in productCategoryList"
              :key="group.name"
              :label="group.name"
            >
              <el-option
                v-for="item in group.products"
                :key="item.id"
                :label="item.name"
                :value="`${item.id}`"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item
          :label="
            currentTagResult === 'review' ? 'Enquiry Date' : 'Received on'
          "
          prop="data"
          class="!w-[320px]"
        >
          <el-date-picker
            v-model="form.date"
            type="daterange"
            start-placeholder="Start date"
            end-placeholder="End date"
            format="DD/MM/YYYY"
            date-format="YYYY/MM/DD"
            class="mr-[10px]"
          />
        </el-form-item>
        <el-form-item
          label="Followup Date"
          v-if="currentTagResult != 'review'"
          prop="data"
          class="!w-[320px]"
        >
          <el-date-picker
            v-model="form.followup_date"
            type="daterange"
            start-placeholder="Start date"
            end-placeholder="End date"
            format="DD/MM/YYYY"
            date-format="YYYY/MM/DD"
            class="mr-[10px]"
          />
        </el-form-item>
        <el-form-item :label="'Client:'" prop="otherParam.clientKeyWords">
          <el-input
            v-model="form.otherParam.clientKeyWords"
            autocomplete="off"
            @input="onSearch()"
            :placeholder="transformI18n('common.input')"
            clearable
          />
        </el-form-item>
        <el-form-item :label="'Reason:'" prop="otherParam.reasonKeyWords">
          <el-input
            v-model="form.otherParam.reasonKeyWords"
            autocomplete="off"
            @input="onSearch()"
            :placeholder="transformI18n('common.input')"
            clearable
          />
        </el-form-item>
        <el-form-item :label="'Staff:'" prop="otherParam.staff">
          <el-input
            v-model="form.otherParam.staff"
            autocomplete="off"
            @input="onSearch()"
            :placeholder="transformI18n('common.input')"
            clearable
          />
        </el-form-item>
        <el-form-item class="action-box">
          <el-button type="primary" @click="onSearch()">
            {{ transformI18n("common.filter") }}
          </el-button>
          <el-button @click="onReset()" type="info">{{
            transformI18n("common.clear")
          }}</el-button>
        </el-form-item>
      </el-form>
      <div class="search-box">
        <el-input
          ref="inputRef"
          v-model="indexQuery.search"
          size="large"
          clearable
          :placeholder="transformI18n('orders.searchOrders')"
          @input="handleSearch"
          class="table-search-input"
        >
          <template #prefix>
            <el-tooltip
              class="item"
              effect="dark"
              content="You can search by ID, Order Result"
              placement="top"
            >
              <SearchIcon class="search-icon" />
            </el-tooltip>
          </template>
        </el-input>
      </div>
    </div>
    <data-table
      v-if="showTable"
      ref="dataTableRef"
      :columns="newColumns"
      :source="getListApiName()"
      :form="form"
      :slotNames="slotNamesList"
      :query-params="tableQueryParams"
      :header-cell-style="{
        background: 'var(--el-table-row-hover-bg-color)',
        color: 'var(--el-text-color-primary)'
      }"
      :getSummariesCustom="getSummaries"
      :showSummary="currentTagResult != 'review'"
      @handleRowClick="onRowClick"
      @toPassNewInfo="toPassNewInfo"
      @sortChange="sortChange"
      @dynamicColumnsChange="dynamicColumnsChange"
    >
      <template #id="{ row }">
        <span v-if="currentTagResult === 'review'">{{ row.id }}</span>
        <span v-else>{{ row.lead_id }}</span>
      </template>
      <template #client="{ row }">
        <span class="iconfont just-icon-client mr-2" />
        <span v-if="row.lead && row.lead.client && row.lead.client.given_name"
          >{{ row.lead.client.given_name }} {{ row.lead.client.surname }}</span
        >
        <span v-else-if="row.client && row.client.given_name"
          >{{ row.client.given_name }} {{ row.client.surname }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #phone="{ row }">
        <span v-if="row.lead && row.lead.client && row.lead.client.phone">{{
          row.lead.client.phone
        }}</span>
        <span v-else-if="row.client && row.client.phone">{{
          row.client.phone
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #email="{ row }">
        <span v-if="row.lead && row.lead.client && row.lead.client.email">{{
          row.lead.client.email
        }}</span>
        <span v-else-if="row.client && row.client.email">{{
          row.client.email
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #address="{ row }">
        <span v-if="row.lead && row.lead.address">
          {{ handleAddress(row.lead.address) }}
        </span>
        <span
          v-else-if="row.lead && row.lead.client && row.lead.client.address"
        >
          {{ handleAddress(row.lead.client.address) }}
        </span>
        <span v-else-if="row.client && row.client.address">{{
          handleAddress(row.client.address)
        }}</span>
        <span v-else class="empty">Empty</span>
      </template>
      <template #category="{ row }">
        <div v-if="row.lead" class="flex">
          <CirCleText
            :text="row.lead.category.name"
            v-if="row.lead && row.lead.category"
            :customBgColor="row.lead.category.color"
            :showNum="1"
          />
          <EmptySpan
            v-if="row.lead && row.lead.category"
            :text="row.lead.category.name"
          />
        </div>
        <div v-else class="flex">
          <CirCleText
            :text="row.category.name"
            v-if="row.category"
            :customBgColor="row.category.color"
            :showNum="1"
          />
          <EmptySpan v-if="row.category" :text="row.category.name" />
        </div>
      </template>
      <template #product="{ row }">
        <EmptySpan
          v-if="currentTagResult === 'review'"
          :text="row.product || ''"
        />
        <EmptySpan v-else :text="row.product?.name || ''" />
      </template>
      <template #quantity="{ row }">
        {{ getQuantity(row) }}
      </template>
      <template #sales_manager="{ row }">
        <EmptySpan
          v-if="currentTagResult === 'review'"
          :text="row.sales && row.sales.login_name ? row.sales.login_name : ''"
        />
        <EmptySpan
          v-else
          :text="
            row.lead && row.lead.sales && row.lead.sales.login_name
              ? row.lead.sales.login_name
              : ''
          "
        />
      </template>
      <template #salesConsultant="{ row }">
        <EmptySpan
          v-if="currentTagResult === 'review'"
          :text="
            row.sales_consultant && row.sales_consultant.login_name
              ? row.sales_consultant.login_name
              : ''
          "
        />
        <EmptySpan
          v-else
          :text="
            row.lead &&
            row.lead.sales_consultant &&
            row.lead.sales_consultant.login_name
              ? row.lead.sales_consultant.login_name
              : ''
          "
        />
      </template>
      <template #updated_at="{ row }">
        <span v-if="row.updated_at">{{
          formatTime(row.updated_at, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #sold_date="{ row }">
        <span v-if="row.sold_date">{{
          formatTime(row.sold_date, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #appt_date="{ row }">
        <span v-if="row.appt_date">{{
          formatTime(row.appt_date, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #followup_time="{ row }">
        <span v-if="row.followup_time">{{
          formatTime(row.followup_time, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #followup_date="{ row }">
        <span v-if="row.followup_date">{{
          formatTime(row.followup_date, "DD/MM/YYYY")
        }}</span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #production_staff="{ row }">
        <div v-if="row.product_staff && row.product_staff.login_name">
          <!-- <CirCleText :text="row.product_staff.login_name" /> -->
          {{ row.product_staff.login_name }}
        </div>
        <span v-else>Empty</span>
      </template>

      <template #cm="{ row }">
        <div v-if="row.cm && row.cm.name">
          <!-- <CirCleText :text="row.cm.name" /> -->
          {{ row.cm.name }}
        </div>
        <span v-else>Empty</span>
      </template>
      <template #install_staff="{ row }">
        <div v-if="row.install && row.install.login_name">
          <!-- <CirCleText :text="row.install.login_name" /> -->
          {{ row.install.login_name }}
        </div>
        <span v-else>Empty</span>
      </template>
      <!--  -->
      <template #sold="{ row }">
        <EmptySpan
          v-if="currentTagResult === 'review'"
          :text="row.sold ? '$ ' + row.sold : ''"
        />
        <EmptySpan
          v-else
          :text="row.lead && row.lead.sold ? '$ ' + row.lead.sold : ''"
        />
      </template>
      <template #retail="{ row }">
        <EmptySpan
          v-if="currentTagResult === 'review'"
          :text="row.retail ? '$ ' + row.retail : ''"
        />
        <EmptySpan
          v-else
          :text="row.lead && row.lead.retail ? '$ ' + row.lead.retail : ''"
        />
      </template>
      <template #cm_received_date="{ row }">
        <span v-if="row.cm_received_date">
          {{ getHandleDate(row.cm_received_date, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #received_date="{ row }">
        <span v-if="row.received_date">
          {{ getHandleDate(row.received_date, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #cm_booked_date="{ row }">
        <span v-if="row.cm_booked_date">
          {{ getHandleDate(row.cm_booked_date, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #submit_date="{ row }">
        <span v-if="row.submit_date">
          {{
            row.submit_date > row.approved_date || !row.approved_date
              ? getHandleDate(row.submit_date, row)
              : getHandleDate(row.approved_date, row)
          }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #ready_date="{ row }">
        <span v-if="row.ready_date">
          {{ getHandleDate(row.ready_date, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #production_date="{ row }">
        <span v-if="row.on_product_date">
          {{ getHandleDate(row.on_product_date, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #installation_date="{ row }">
        <span v-if="row.installation_date">
          {{ getHandleDate(row.installation_date, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #hold_time="{ row }">
        <span v-if="row.hold_time">
          {{ getHandleDate(row.hold_time, row) }}</span
        >
        <span class="empty" v-else>Empty</span>
      </template>
      <template #completion_date="{ row }">
        <EmptySpan :text="formatTime(row.completion_date, 'DD/MM/YYYY')" />
      </template>
      <template #supplier_days="{ row }">
        <EmptySpan :text="getSupplierReceivedDays(row.supplier_list) + 'd'" />
      </template>
      <template #enquiryDate="{ row }">
        <span v-if="row.enquiry_date">
          {{ formatTime(row.enquiry_date, "DD/MM/YYYY") }}</span
        >
        <EmptySpan :text="''" v-else />
      </template>
      <template #remainingBalance="{ row }">
        {{ calculatedAmount(row) }}
      </template>
      <template #result="{ row, $index }">
        <div class="result-container" @click.stop="stopBubbling()">
          <span v-if="currentTagResult === 'review'" class="table-draft-result">
            <span class="mr-1 iconfont just-icon-file-history" />
            <span class="capitalize">Review</span>
          </span>
          <span
            v-else-if="
              row.result === 'cancelled' || row.result === 'completion'
            "
            class="table-draft-result"
          >
            <span
              :class="
                'mr-1 iconfont just-icon-' + getCurrentResultIcon(row.result)
              "
            />
            <span class="capitalize">{{ row.result }}</span>
          </span>
          <div v-else-if="row.result">
            <span
              :class="
                'mr-1 iconfont just-icon-' +
                getCurrentResultIcon(row.result) +
                ' input-result-icon'
              "
            />
            <el-cascader
              :options="getResultOptions(row)"
              :show-all-levels="false"
              :model-value="row.result"
              @change="value => handleResultChange(value, row, $index)"
              @visible-change="isShow => handleResultClose(isShow, $index)"
              :props="resultCascaderProps"
              :key="cascaderKey[$index]"
              v-if="showCascader[$index]"
            >
              <template #default="{ data }">
                <span class="cascader-label">
                  <span
                    :class="'mr-1 iconfont just-icon-' + data.icon"
                    v-if="data.icon"
                  />
                  {{ data.orderPageLabel || data.label }}</span
                >
              </template>
            </el-cascader>
          </div>
        </div>
      </template>
      <template #electrician="{ row }">
        <span v-if="row.electrician">Yes</span>
        <span v-else>No</span>
      </template>
      <template #ready="{ row }">
        <span v-if="row.is_ready">Yes</span>
        <span v-else>No</span>
      </template>
      <template #existingProduct="{ row }">
        <span v-if="row.lead.client.products.length">
          <span
            class="existing-product-item"
            v-for="(existingProduct, i) in row.lead.client.products"
            :key="i"
          >
            {{ existingProduct.existing_product_name }}
            <i v-if="i + 1 != row.lead.client.products.length">, </i>
          </span>
        </span>
        <span class="empty" v-else>Empty</span>
      </template>
      <template #operation="{ row }">
        <div
          @click.stop="stopBubbling()"
          v-if="
            row.result != 'cancelled' && getActionList(row.result, row).length
          "
        >
          <el-dropdown trigger="click">
            <span class="iconfont just-icon-gengduo" />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  @click.stop="handleAction(row, actionItem.name)"
                  v-for="(actionItem, actionIndex) in getActionList(
                    row.result,
                    row
                  )"
                  :key="actionIndex"
                >
                  <span>
                    <i
                      :class="`iconfont just-icon-${actionItem.icon} mr-2 primary-color `"
                    />
                    {{ actionItem.label }}
                  </span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
    </data-table>
    <ChangeResult ref="changeResult" />
    <ResultDialog ref="resultDialogRef" />
    <CreateAppointment ref="createAppointment" />
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: 700;
}

.result-container {
  :deep(.el-input__wrapper) {
    width: 140px;
    border: 1px solid #e9ebf0;
    border-radius: 8px;
  }
}

.table-draft-result {
  display: flex;
  align-items: center;
}

.existing-product-item {
  display: inline-block;
  margin-left: 2px;
}
</style>
./data.js
