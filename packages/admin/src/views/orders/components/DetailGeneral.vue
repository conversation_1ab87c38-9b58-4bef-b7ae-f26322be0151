<script setup lang="ts">
import { reactive, ref, onMounted, watch, computed } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { useCommonStoreHook } from "@/store/modules/common";
import CirCleText from "@/components/CircleText";
import { allResults } from "./data";
import AddIcon from "@/assets/svg/add.svg?component";
import { formatTime } from "@/utils/form";
import { hasAuth } from "@/router/utils";
import EmptySpan from "@/components/EmptySpan";

const client = ref(null);
const orderGeneralForm = reactive({
  id: null,
  lead_id: null,
  received_date: "",
  result: "new_order",
  under_sell: null,
  estimated_comm: null,
  start_date: null,
  product_staff_id: null,
  product_staff: null,
  cm_id: null,
  cm_booked_date: null,
  cm_received_date: null,
  on_product_date: null,
  ready_date: null,
  install_id: null,
  installation_date: null,
  completion_date: null,
  reason: null,
  finance_company: null,
  finance_type: null,
  finance_amount: null,
  finance_approved_date: null,
  finance_approved_no: null,
  payments: [],
  cm: null,
  install: null,
  lead: null
});
const formRef = ref(null);
const productCategoryList = ref([]);
const subProductCategoryList = ref({});
const sourceList = ref([]);
const subSourceList = ref({});
const promotionList = ref([]);
const salesList = ref([]);
const spokenToUserList = ref([]);
const apptSetterList = ref([]);
const productStaffList = ref([]);
const allStaffList = ref();
const staffName = reactive({
  saleName: { login_name: null, color: null },
  apptSetterName: { login_name: null, color: null },
  spokenTo: { login_name: null, color: null },
  productStaff: { login_name: null, color: null }
});

const props = defineProps({
  allStaff: {
    type: Array,
    default() {
      return [];
    }
  },
  orderInfo: {
    type: Object,
    default() {
      return {};
    }
  },
  editPage: {
    type: Boolean,
    default: true
  }
});

watch(
  () => props.allStaff,
  _newVal => {
    allStaffList.value = _newVal;
    updateStaff();
  },
  {
    deep: true,
    immediate: true
  }
);

watch(
  () => props.orderInfo,
  _newVal => {
    init(_newVal);
  },
  {
    deep: true,
    immediate: true
  }
);

const canEditOrderManagement = computed(() => {
  return hasAuth("editOrderManagement") && props.editPage;
});
const canEdit = computed(() => {
  if (
    orderGeneralForm.result !== "cancelled" &&
    orderGeneralForm.result !== "completion" &&
    props.editPage
  ) {
    return true;
  }
  return false;
});
function updateStaff() {
  if (allStaffList.value) {
    salesList.value = [];
    spokenToUserList.value = [];
    apptSetterList.value = [];
    productStaffList.value = [];
    allStaffList.value.map(item => {
      if (item.roles?.length) {
        item.roles.map(role => {
          if (role.name === "sale_manager") {
            salesList.value.push(item);
          } else if (role.name === "lead_taker_user") {
            spokenToUserList.value.push(item);
          } else if (role.name === "appointment_setter") {
            apptSetterList.value.push(item);
          } else if (role.name === "production_assistant") {
            productStaffList.value.push(item);
          }
        });
      }
    });
  }
}

function getResultIcon(result) {
  if (!result) {
    return;
  }
  return allResults.find(item => item.value === result)?.icon;
}

// function handChangeStaffName(id, nameType) {
//   const item = allStaffList.value.find(item => item.id === id);
//   if (item) {
//     staffName[nameType] = { login_name: item.login_name, color: item.color };
//   }
// }

function getNewOrderGeneralInfo() {
  if (orderGeneralForm.lead) {
    orderGeneralForm.lead.sub_category_ids =
      orderGeneralForm.lead.sub_category_ids_array.join(",");
  }
  return orderGeneralForm;
}

function init(data = null) {
  if (data) {
    data = JSON.parse(JSON.stringify(data));
    Object.assign(orderGeneralForm, data);
    if (data.lead) {
      client.value = data.lead.client;
    }
    if (orderGeneralForm.lead) {
      staffName.saleName = {
        login_name: orderGeneralForm.lead.sales?.login_name,
        color: orderGeneralForm.lead.sales?.color
      };
      staffName.apptSetterName = {
        login_name: orderGeneralForm.lead.appt_setter?.login_name,
        color: orderGeneralForm.lead.appt_setter?.color
      };
      staffName.spokenTo = {
        login_name: orderGeneralForm.lead.spoken_to?.login_name,
        color: orderGeneralForm.lead.spoken_to?.color
      };

      orderGeneralForm.lead.sub_category_ids_array = orderGeneralForm.lead
        .sub_category_ids
        ? orderGeneralForm.lead.sub_category_ids.split(",")
        : [];
      orderGeneralForm.lead.sub_category_ids_array =
        orderGeneralForm.lead.sub_category_ids_array.map(Number);
    }
    staffName.productStaff = {
      login_name: orderGeneralForm.product_staff?.login_name,
      color: orderGeneralForm.product_staff?.color
    };
  }
}

function initData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  subProductCategoryList.value = basicData["subProductCategoryList"];
  sourceList.value = basicData["sourceList"];
  subSourceList.value = basicData["subSourceList"];
  promotionList.value = basicData["promotionList"];
}
onMounted(() => {
  initData();
});
defineExpose({ getNewOrderGeneralInfo });
</script>

<template>
  <div>
    <el-form
      ref="formRef"
      :model="orderGeneralForm"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
      v-if="orderGeneralForm.lead"
    >
      <div class="detail-content">
        <div class="general-info">
          <div class="item-block">
            <!-- client info -->
            <div class="item-title level-1">
              {{ transformI18n("client.client") }}
            </div>
            <el-row :gutter="10" v-if="client">
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="level-2">{{ transformI18n("client.client") }}</div>
                <div class="item-value level-3 edit-client">
                  <span>
                    {{ client.title }} {{ client.surname }}
                    {{ client.given_name }}
                  </span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="transformI18n('leads.clientPhone')">
                  <div class="item-value level-3 edit-client">
                    {{ client.phone }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="level-2">
                  {{ transformI18n("leads.backupContact") }}
                </div>
                <div class="item-value level-3 edit-client">
                  <span
                    v-if="
                      client.sec_title ||
                      client.sec_surname ||
                      client.sec_given_name
                    "
                  >
                    {{ client.sec_title }} {{ client.sec_surname }}
                    {{ client.sec_given_name }}
                  </span>
                  <span v-else class="empty">{{
                    transformI18n("common.empty")
                  }}</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item
                  :label="transformI18n('leads.backupContactPhone')"
                  prop="sec_phone"
                >
                  <div class="item-value level-3 edit-client">
                    {{ client.sec_phone }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="item-box">
                <el-form-item
                  :label="transformI18n('client.address')"
                  prop="address"
                >
                  <div class="item-value level-3 edit-client">
                    <span v-if="orderGeneralForm.lead.address">
                      {{ orderGeneralForm.lead.address }}</span
                    >
                    <span v-else-if="client && client.address">
                      {{ client.address }}</span
                    >
                    <span v-else class="empty"> Empty</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="transformI18n('client.email')">
                  <div class="item-value level-3 edit-client">
                    <span v-if="client && client.email">
                      {{ client.email }}</span
                    >
                    <span v-else class="empty"> Empty</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item label="Existing Product">
                  <div class="item-value level-3 edit-client">
                    <span v-if="client && client.products">
                      <span
                        class="existing-product-item"
                        v-for="(existingProduct, i) in client.products"
                        :key="i"
                      >
                        {{ existingProduct.existing_product_name }}
                        <i v-if="i + 1 != client.products.length">, </i>
                      </span>
                    </span>
                    <span v-else class="empty"> Empty</span>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- management -->
          <div class="item-block">
            <div class="item-title level-1">
              {{ transformI18n("leads.management") }}
            </div>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item
                  :label="'Production Staff'"
                  prop="product_staff_id"
                >
                  <div class="flex items-center">
                    <CirCleText
                      :text="staffName.productStaff.login_name"
                      v-if="staffName.productStaff"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="staffName.productStaff.color"
                    />
                    <EmptySpan
                      :text="
                        staffName.productStaff &&
                        staffName.productStaff.login_name
                          ? staffName.productStaff.login_name
                          : ''
                      "
                    />

                    <!-- <el-select
                      v-model="orderGeneralForm.product_staff_id"
                      :placeholder="transformI18n('common.empty')"
                      filterable
                      v-else
                      @change="val => handChangeStaffName(val, 'productStaff')"
                    >
                      <el-option
                        v-for="item in productStaffList"
                        :key="item.id"
                        :label="item.login_name"
                        :value="item.id"
                      >
                        <div class="flex items-center">
                          <CirCleText
                            :text="item.login_name"
                            :size="20"
                            :fontSize="'10px'"
                            :customBgColor="item.color"
                          />
                          <span>{{ item.login_name }}</span>
                        </div>
                      </el-option>
                    </el-select> -->
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="transformI18n('common.result')">
                  <div class="flex items-center">
                    <span
                      :class="
                        'mr-1  input-result-icon iconfont just-icon-' +
                        getResultIcon(orderGeneralForm.result)
                      "
                    />
                    <span class="capitalize">{{
                      transformI18n(`orders.result_${orderGeneralForm.result}`)
                    }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="'Order Received Date'">
                  <div class="item-value level-3">
                    <span v-if="orderGeneralForm.received_date">{{
                      formatTime(orderGeneralForm.received_date, "DD/MM/YYYY")
                    }}</span>
                    <span v-else class="empty">Empty</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="'Estimated Comm'" prop="estimated_comm">
                  <EmptySpan
                    :text="'$ ' + orderGeneralForm.estimated_comm"
                    v-if="!canEditOrderManagement || !canEdit"
                  />
                  <el-input
                    v-else
                    v-model="orderGeneralForm.estimated_comm"
                    :placeholder="transformI18n('common.input')"
                  >
                    <template #prefix v-if="orderGeneralForm.estimated_comm"
                      ><span class="mr-2">$</span>
                    </template></el-input
                  >
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="'Under Sell'">
                  <div class="item-value level-3">
                    <span v-if="orderGeneralForm.under_sell"
                      >{{ orderGeneralForm.under_sell }} %
                    </span>
                    <span v-else class="empty">{{
                      transformI18n("common.empty")
                    }}</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="'Start Date'" prop="start_date">
                  <div
                    class="item-value level-3"
                    v-if="!canEditOrderManagement || !canEdit"
                  >
                    <span v-if="orderGeneralForm.start_date">{{
                      formatTime(orderGeneralForm.start_date, "DD/MM/YYYY")
                    }}</span>
                    <span v-else class="empty">Empty</span>
                  </div>
                  <el-date-picker
                    v-model="orderGeneralForm.start_date"
                    v-else
                    type="date"
                    :placeholder="'ASAP'"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="level-2">Related Leads</div>
                <div class="item-value level-3">
                  <span v-if="orderGeneralForm.lead">{{
                    orderGeneralForm.lead.id
                  }}</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="level-2">Sales Consultant</div>
                <div class="item-value level-3">
                  <CirCleText
                    :text="orderGeneralForm.lead.sales_consultant.login_name"
                    v-if="
                      orderGeneralForm.lead &&
                      orderGeneralForm.lead.sales_consultant
                    "
                    :size="20"
                    :fontSize="'10px'"
                    :customBgColor="
                      orderGeneralForm.lead.sales_consultant.color
                    "
                  />
                  <EmptySpan
                    :text="
                      orderGeneralForm.lead &&
                      orderGeneralForm.lead.sales_consultant
                        ? orderGeneralForm.lead.sales_consultant.login_name
                        : ''
                    "
                  />
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="level-2">Sold Date</div>
                <div class="item-value level-3">
                  <span
                    v-if="
                      orderGeneralForm.lead && orderGeneralForm.lead.sold_date
                    "
                    >{{
                      formatTime(orderGeneralForm.lead.sold_date, "DD/MM/YYYY")
                    }}</span
                  >
                </div>
              </el-col>
            </el-row>
          </div>
          <!-- Product Requirements -->
          <div class="item-block">
            <div class="item-title level-1">
              {{ transformI18n("leads.productRequirements") }}
            </div>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="transformI18n('productSpec.category')">
                  <div class="item-value level-3">
                    <span
                      v-if="
                        orderGeneralForm.lead && orderGeneralForm.lead.category
                      "
                    >
                      {{ orderGeneralForm.lead.category.name }}</span
                    >
                    <span class="empty" v-else>Empty</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12" class="item-box product-select">
                <el-form-item
                  :label="transformI18n('productSpec.product')"
                  prop="sub_category_ids_array"
                  v-if="orderGeneralForm.lead"
                >
                  <div class="item-value level-3" v-if="!canEdit">
                    <span v-if="orderGeneralForm.lead">
                      {{ orderGeneralForm.lead.product }}</span
                    >
                    <span class="empty" v-else>Empty</span>
                  </div>
                  <el-select
                    v-model="orderGeneralForm.lead.sub_category_ids_array"
                    :placeholder="transformI18n('common.empty')"
                    multiple
                    :multiple-limit="1"
                    collapse-tags
                    collapse-tags-tooltip
                    v-else
                    :suffix-icon="AddIcon"
                    filterable
                  >
                    <el-option
                      v-for="item in subProductCategoryList[
                        orderGeneralForm.lead.product_category_id
                      ]"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" class="item-box">
                <el-form-item :label="'Product Detail'" prop="lead.comment">
                  <el-input
                    v-model="orderGeneralForm.lead.comment"
                    v-if="orderGeneralForm.lead"
                    :placeholder="transformI18n('leads.commentsHoldDetail')"
                    autocomplete="off"
                    :rows="3"
                    maxlength="50"
                    type="textarea"
                    readonly
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- source -->
          <div class="item-block">
            <div class="item-title level-1">
              {{ transformI18n("basicData.source") }}
            </div>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item
                  :label="transformI18n('basicData.source')"
                  v-if="orderGeneralForm.lead"
                >
                  <el-select
                    v-model="orderGeneralForm.lead.source_id"
                    :disabled="true"
                  >
                    <el-option
                      v-for="item in sourceList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="transformI18n('basicData.subSource')">
                  <el-select
                    v-model="orderGeneralForm.lead.sub_source_id"
                    :disabled="true"
                    :placeholder="transformI18n('common.select')"
                  >
                    <el-option
                      v-for="item in subSourceList[
                        orderGeneralForm.lead.source_id
                      ]"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="transformI18n('leads.dateOfEnquiry')">
                  <el-date-picker
                    v-model="orderGeneralForm.lead.enquiry_date"
                    type="date"
                    :placeholder="transformI18n('common.select')"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    :disabled="true"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item :label="transformI18n('leads.spokenTo')">
                  <div class="noEdit">
                    <div class="flex items-center" v-if="staffName.spokenTo">
                      <CirCleText
                        :text="staffName.spokenTo.login_name"
                        :size="20"
                        :fontSize="'10px'"
                        :customBgColor="staffName.spokenTo.color"
                      />
                      {{ staffName.spokenTo.login_name }}
                    </div>
                    <div v-else class="empty">
                      {{ transformI18n("common.empty") }}
                    </div>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="12" class="item-box">
                <el-form-item :label="transformI18n('basicData.promotion')">
                  <el-select
                    v-model="orderGeneralForm.lead.promotion_id"
                    :placeholder="transformI18n('common.select')"
                    :disabled="true"
                  >
                    <el-option
                      v-for="item in promotionList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </el-form>
  </div>
</template>

<style lang="scss" scoped>
.level-2 {
  margin-bottom: 8px;
}

.detail-content {
  display: flex;
  justify-content: space-between;

  .general-info {
    flex-grow: 1;
    min-height: 350px;
    max-height: calc(100vh - 360px);
    padding: 0 10px;
    overflow: auto;
  }

  .notes-box {
    flex-shrink: 0;
    width: 450px;
    background: #fff;
    border: 1px solid #e9ebf0;
  }

  .item-block {
    padding: 0 0 20px;
    margin-top: 20px;
    border: 0;
    border-bottom: 1px solid #e9ebf0;

    &:last-child {
      border-bottom: none;
    }
  }

  .item-box {
    min-height: 50px;
    margin: 20px 0 0;

    .item-value {
      line-height: 1.5em;
    }
  }
}

.input-result-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.edit-client {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.noEdit {
  cursor: not-allowed;
}

@media screen and (width <= 480px) {
  .el-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .detail-content .item-box {
    margin-top: 10px;
  }
}
</style>
