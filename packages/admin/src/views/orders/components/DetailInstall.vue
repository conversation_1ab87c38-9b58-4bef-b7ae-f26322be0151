<script setup lang="ts">
import { ref, onMounted, watch, reactive, computed, nextTick } from "vue";
import addSupplierDialog from "@/components/AddSupplier/addSupplierDialog.vue";
import addApprovedDateDialog from "@/components/AddApprovedDate/addApprovedDateDialog.vue";
import {
  orderControllerRemoveSupplier,
  orderControllerAddSupplier
} from "@/api/admin/order";
import { ElLoading, ElMessage } from "element-plus";
import CirCleText from "@/components/CircleText";
import { UploadFile } from "@/components/Upload";
import { hasAuth } from "@/router/utils";
import { transformI18n } from "@/plugins/i18n";
import {
  uploadControllerDestroy,
  uploadControllerShow
} from "@/api/admin/basic-data";
import { appointmentTypeText } from "@/views/appointment/data";
import { accountControllerIndex } from "@/api/admin/admin-account";
import { appointmentControllerAssign } from "@/api/admin/appointment";
import { orderControllerUpdate } from "@/api/admin/order";
import { formatTime } from "@/utils/form";
import { decisionOption } from "./data";

const addApprovedDateDialogRef = ref(null);
const addSupplierDialogRef = ref(null);
const decisionDialogVisible = ref(false);
const dialogPanel = ref();
const loadingDialogRef = ref();
const assignToLoading = ref(false);
const orderForm = reactive({
  id: null,
  lead_id: null,
  received_date: "",
  result: "",
  under_sell: null,
  estimated_comm: null,
  start_date: null,
  product_staff_id: null,
  product_staff: null,
  cm_id: null,
  cm_booked_date: null,
  cm_received_date: null,
  on_product_date: null,
  ready_date: null,
  install_id: null,
  installation_date: null,
  completion_date: null,
  reason: null,
  finance_company: null,
  finance_type: null,
  finance_amount: null,
  finance_approved_date: null,
  finance_approved_no: null,
  payments: [],
  cm: null,
  install: null,
  lead: null,
  supplier_list: null,
  product_files: null,
  invoice: null,
  appointment: null,
  hold_time: null,
  followup_date: null,
  to_be_collect: null,
  submit_date: null,
  approved_date: null,
  is_ready: 0,
  company_region: null,
  trade: null,
  application_id: null,
  decision: null,
  painter_person: null,
  electrician_person: null,
  delivery: null
});
const canAssignToCm = ref(false);
const canAssignToIntall = ref(false);
const installId = ref(null);
const painterId = ref(null);
const electricianId = ref(null);
const cmId = ref(null);
const canEdit = computed(() => {
  if (
    orderForm.result !== "cancelled" &&
    orderForm.result !== "completion" &&
    props.editPage
  ) {
    return true;
  }
  return false;
});
const canUpdateDate = computed(() => {
  if (orderForm.result !== "cancelled" && props.editPage) {
    return true;
  }
  return false;
});
const props = defineProps({
  orderInfo: {
    type: Object,
    default() {
      return {};
    }
  },
  editPage: {
    type: Boolean,
    default: true
  }
});

const uploadInvoice = ref({
  type: "invoice",
  object_id: props.orderInfo.lead_id
});
const uploadCM = ref({
  type: "cm",
  object_id: props.orderInfo.lead_id
});
const uploadTrade = ref({
  type: "trade",
  object_id: props.orderInfo.lead_id
});

watch(
  () => props.orderInfo,
  _newVal => {
    orderInfoHandle(_newVal);
    uploadInvoice.value.object_id = props.orderInfo.lead_id;
    uploadCM.value.object_id = props.orderInfo.lead_id;
    uploadTrade.value.object_id = props.orderInfo.lead_id;
  },
  {
    deep: true,
    immediate: true
  }
);

const staffOptions = ref([]);
const getStaffOptionsLoading = ref(false);

const emit = defineEmits<{
  (e: "reloadOrderInfo", val: Object): void;
}>();

function dialogChange() {
  addApprovedDateDialogRef.value
    .show({
      id: orderForm.id,
      decision: orderForm.decision
    })
    .then(_res => {
      if (_res) {
        emit("reloadOrderInfo", {});
      } else {
        orderForm.decision = props.orderInfo.decision;
      }
    });
}
function toAddSupplier() {
  addSupplierDialogRef.value.show({ id: orderForm.id }).then(_res => {
    if (_res) {
      emit("reloadOrderInfo", {});
    }
  });
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: loadingDialogRef.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function removeSupplier(item) {
  const data = {
    order_supplier_id: item.id
  };
  dialogLoading();
  orderControllerRemoveSupplier(orderForm.id, data)
    .then(res => {
      dialogPanel.value.close();
      if (res.success) {
        ElMessage.success(res.message);
      } else {
        ElMessage.error(res.message);
      }
      emit("reloadOrderInfo", {});
    })
    .catch(_err => {
      dialogPanel.value.close();
    });
}
function fileChange() {
  emit("reloadOrderInfo", {});
}

function previewFile(file) {
  const fileUrl = file.url;
  if (file.storage == "s3") {
    dialogLoading();
    uploadControllerShow(file.id)
      .then(res => {
        console.log(res);
        if (res.data.s3Url) {
          window.open(res.data.s3Url, "_blank");
        } else {
          window.open(res.data.url, "_blank");
        }
        dialogPanel.value.close();
      })
      .catch(_err => {
        dialogPanel.value.close();
      });
  } else {
    window.open(fileUrl, "_blank");
  }
}

// delete attachment
function delAttachment(file) {
  uploadControllerDestroy(file.id).then(res => {
    if (res.success) {
      ElMessage.success(res.message);
      emit("reloadOrderInfo", {});
    } else {
      ElMessage.error(res.message || "File deletion failure!");
    }
  });
}

// function disabledDate(time, order_date) {
//   return time.getTime() < new Date(order_date).getTime();
// }

function supplierReceivedDateChange(value, supplier, type) {
  if (!(supplier && supplier.id)) {
    return;
  }
  let sendForm = {};
  if (type == "date") {
    sendForm = {
      order_supplier_id: supplier.id,
      supplier_received_date: value,
      note: supplier.note,
      supplier_amount: supplier.supplier_amount
    };
  } else if (type == "note") {
    sendForm = {
      order_supplier_id: supplier.id,
      note: value,
      supplier_received_date: supplier.supplier_received_date,
      supplier_amount: supplier.supplier_amount
    };
  } else {
    sendForm = {
      order_supplier_id: supplier.id,
      supplier_amount: value,
      note: supplier.note,
      supplier_received_date: supplier.supplier_received_date
    };
  }

  dialogLoading();
  orderControllerAddSupplier(orderForm.id, sendForm)
    .then(res => {
      dialogPanel.value.close();
      if (res.success) {
        ElMessage.success(res.message);
      } else {
        ElMessage.error(res.message);
      }
    })
    .catch(_err => {
      dialogPanel.value.close();
    });
}

function handChangeStaffName(type) {
  nextTick(() => {
    let user_id = null;
    if (type === "install") {
      user_id = installId.value;
    } else if (type === "cm") {
      user_id = cmId.value;
    } else if (type === "electrician") {
      user_id = electricianId.value;
    } else if (type === "painter") {
      user_id = painterId.value;
    }

    // 对于electrician和painter类型，允许清除操作（user_id为null）
    if (!user_id && (type === "install" || type === "cm")) {
      return;
    }
    if (type === "electrician" || type === "painter") {
      if (!installId.value) {
        ElMessage.error("Please assign installer first.");
        return;
      }
      const sendForm = {
        electrician_id: electricianId.value,
        painter_id: painterId.value
      };
      assignToLoading.value = true;
      orderControllerUpdate(orderForm.id, sendForm)
        .then(res => {
          if (res.success) {
            ElMessage.success(res.message);
          } else {
            ElMessage.error(res.message);
          }
          assignToLoading.value = false;
          emit("reloadOrderInfo", {});
        })
        .catch(() => {
          ElMessage.error("Operation failed.");
        });
      return;
    }
    assignToLoading.value = true;
    if (!(orderForm.appointment && orderForm.appointment.length)) {
      ElMessage.error(
        "No appointment is in progress. Please create an appointment."
      );
      return;
    }
    const appointmentId = orderForm.appointment[0].id;
    appointmentControllerAssign(appointmentId, { user_id: user_id }).then(
      res => {
        assignToLoading.value = false;
        if (res?.success) {
          ElMessage.success(res.message);
          const theOne = staffOptions.value.find(item => item.id === user_id);
          orderForm[type] = theOne;
          emit("reloadOrderInfo", {});
        } else {
          ElMessage.error(res.message);
        }
      }
    );
  });
}

function getStaffOptions(query = "") {
  let filter = "";
  if (appointmentTypeText["install"]) {
    const roleNameListStr =
      appointmentTypeText["install"].filterRoleName.join("|");
    filter += "roles.name:in:" + roleNameListStr;
  }
  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  const size = 99;
  getStaffOptionsLoading.value = true;
  const search = query ? "%" + query + "%" : "";
  accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    orderForm.company_region
  )
    .then(res => {
      const { data } = res;
      staffOptions.value = data || [];
      getStaffOptionsLoading.value = false;
    })
    .catch(_error => {
      getStaffOptionsLoading.value = false;
    });
}

function orderInfoHandle(data) {
  canAssignToCm.value = false;
  canAssignToIntall.value = false;
  if (data) {
    data = JSON.parse(JSON.stringify(data));
    Object.assign(orderForm, data);
    installId.value = orderForm.install?.id;
    painterId.value = orderForm.painter_person?.id;
    electricianId.value = orderForm.electrician_person?.id;
    cmId.value = orderForm.cm?.id;

    if (props.editPage && data.appointment && data.appointment.length) {
      if (data.appointment[0].type === "cm") {
        canAssignToCm.value = hasAuth("assignAppointment");
      } else if (data.appointment[0].type === "install") {
        canAssignToIntall.value = hasAuth("assignAppointment");
      }
    }
  }
}

//update form data
function formChange(isToBeCollect = false) {
  let sendForm = null;
  if (isToBeCollect) {
    sendForm = {
      to_be_collect: orderForm.to_be_collect
    };
  } else {
    sendForm = {
      cm_booked_date: orderForm.cm_booked_date,
      cm_received_date: orderForm.cm_received_date,
      on_product_date: orderForm.on_product_date,
      hold_time: orderForm.hold_time,
      followup_date: orderForm.followup_date,
      ready_date: orderForm.ready_date,
      completion_date: orderForm.completion_date,
      reason: orderForm.reason,
      is_ready: orderForm.is_ready,
      application_id: orderForm.application_id,
      delivery: orderForm.delivery
    };
  }
  orderControllerUpdate(orderForm.id, sendForm)
    .then(res => {
      if (res.success) {
        ElMessage.success(res.message);
      } else {
        ElMessage.error(res.message);
      }
      emit("reloadOrderInfo", {});
    })
    .catch(() => {
      ElMessage.error("Operation failed.");
    });
}

onMounted(() => {
  // staffOptions.value = [];
  getStaffOptions();
});
</script>

<template>
  <div class="mb-[20px]">
    <div
      ref="loadingDialogRef"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="detail-content">
        <div class="general-info">
          <!-- Install -->
          <div class="item-title level-1">Installation</div>
          <div class="base-item border-b border-[#E9EBF0] mt-2.5 mb-2.5">
            <p class="text-[#2A2E34] text-sm font-medium mb-2.5">
              Check Measurer
            </p>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item label="Check Measurer Name" prop="orderForm.cm">
                  <div
                    class="item-value level-3"
                    v-if="!canAssignToCm || !canEdit"
                  >
                    <span v-if="orderForm && orderForm.cm">
                      <CirCleText
                        :text="orderForm.cm.login_name"
                        :size="20"
                        :fontSize="'10px'"
                        :customBgColor="orderForm.cm.color"
                      />
                      {{ orderForm.cm.login_name }}
                    </span>
                    <span class="text-[#9DA7B8]" v-else>Not yet</span>
                  </div>
                  <div class="flex items-center" v-else>
                    <CirCleText
                      :text="orderForm.cm.login_name"
                      v-if="orderForm.cm"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="orderForm.cm.color"
                    />
                    <el-select
                      v-model="cmId"
                      :placeholder="transformI18n('common.empty')"
                      filterable
                      clearable
                      class="w-[240px] mr-[10px]"
                      :loading="getStaffOptionsLoading"
                      :remote-method="getStaffOptions"
                      remote
                      @change="handChangeStaffName('cm')"
                      :disabled="assignToLoading"
                    >
                      <el-option
                        v-for="item in staffOptions"
                        :key="item.id"
                        :label="item.login_name"
                        :value="item.id"
                      >
                        <div class="flex items-center">
                          <CirCleText
                            :text="item.login_name"
                            :size="20"
                            :fontSize="'10px'"
                            :customBgColor="item.color"
                          />
                          <span>{{ item.login_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Check Measurer Date
                </div>
                <div class="item-value level-3" v-if="!canEdit">
                  <span v-if="orderForm.cm_booked_date">
                    {{ formatTime(orderForm.cm_booked_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="orderForm.cm_booked_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  CM Receive Date
                </div>
                <div class="item-value level-3" v-if="!canEdit">
                  <span v-if="orderForm.cm_received_date">
                    {{ formatTime(orderForm.cm_received_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="orderForm.cm_received_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  OnProduction Date
                </div>
                <div class="item-value level-3" v-if="!canEdit">
                  <span v-if="orderForm.on_product_date">
                    {{ formatTime(orderForm.on_product_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="orderForm.on_product_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Hold Time
                </div>
                <div class="item-value level-3" v-if="!canEdit">
                  <span v-if="orderForm.hold_time">
                    {{ orderForm.hold_time }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="orderForm.hold_time"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Followup Date
                </div>
                <div class="item-value level-3" v-if="!canEdit">
                  <span v-if="orderForm.followup_date">
                    {{ formatTime(orderForm.followup_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="orderForm.followup_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>

              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Ready Date
                </div>
                <div class="item-value level-3" v-if="!canEdit">
                  <span v-if="orderForm.ready_date">
                    {{ formatTime(orderForm.ready_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="orderForm.ready_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  To Be Collect
                </div>
                <el-form-item :label="''" prop="to_be_collect">
                  <el-input
                    v-model="orderForm.to_be_collect"
                    :placeholder="transformI18n('common.input')"
                    @change="val => formChange(true)"
                  >
                    <template #prefix v-if="orderForm.to_be_collect"
                      ><span class="mr-2">$</span>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Submit Date
                </div>
                <div class="item-value level-3">
                  <span v-if="orderForm.submit_date">
                    {{ formatTime(orderForm.submit_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Approved/Updated Date
                </div>
                <div class="item-value level-3">
                  <span v-if="orderForm.approved_date">
                    {{ formatTime(orderForm.approved_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Application ID
                </div>
                <div class="item-value level-3">
                  <el-input
                    v-model="orderForm.application_id"
                    :maxlength="10"
                    :minlength="8"
                    :controls="false"
                    clearable
                    :placeholder="transformI18n('common.input')"
                    class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
                    @change="val => formChange()"
                  />
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Decision
                </div>
                <div class="item-value level-3">
                  <el-select
                    v-model="orderForm.decision"
                    @change="dialogChange()"
                    :placeholder="transformI18n('common.select')"
                  >
                    <el-option
                      v-for="item in decisionOption"
                      :label="item"
                      :value="item"
                      :key="item"
                      :disabled="
                        item == 'Submitted' || item == 'Development Approval'
                      "
                    />
                  </el-select>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Reason
                </div>
                <el-form-item :label="''" prop="">
                  <el-input
                    v-model="orderForm.reason"
                    type="textarea"
                    :rows="3"
                    :disabled="!canEdit"
                    :placeholder="transformI18n('common.input')"
                    @change="val => formChange()"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Ready
                </div>
                <el-select
                  v-model="orderForm.is_ready"
                  @change="formChange()"
                  :placeholder="transformI18n('common.select')"
                  :disabled="!canEdit"
                >
                  <el-option label="Yes" :value="1" />
                  <el-option label="No" :value="0" />
                </el-select>
              </el-col>
            </el-row>
          </div>
          <div class="install-item border-b border-[#E9EBF0] mt-2.5 mb-2.5">
            <p class="text-[#2A2E34] text-sm font-medium mb-2.5">Install</p>
            <el-row :gutter="10">
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item label="Installer" prop="installId">
                  <div class="item-value level-3" v-if="!canAssignToIntall">
                    <span v-if="orderForm && orderForm.install">
                      <CirCleText
                        :text="orderForm.install.login_name"
                        :size="20"
                        :fontSize="'10px'"
                        :customBgColor="orderForm.install.color"
                      />
                      {{ orderForm.install.login_name }}
                    </span>
                    <span class="text-[#9DA7B8]" v-else>Not yet</span>
                  </div>
                  <div class="flex items-center" v-else>
                    <CirCleText
                      :text="orderForm.install.login_name"
                      v-if="orderForm.install"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="orderForm.install.color"
                    />
                    <el-select
                      v-model="installId"
                      :placeholder="transformI18n('common.empty')"
                      filterable
                      clearable
                      class="w-[240px] mr-[10px]"
                      :loading="getStaffOptionsLoading"
                      :remote-method="getStaffOptions"
                      remote
                      @change="handChangeStaffName('install')"
                      :disabled="assignToLoading"
                    >
                      <el-option
                        v-for="item in staffOptions"
                        :key="item.id"
                        :label="item.login_name"
                        :value="item.id"
                      >
                        <div class="flex items-center">
                          <CirCleText
                            :text="item.login_name"
                            :size="20"
                            :fontSize="'10px'"
                            :customBgColor="item.color"
                          />
                          <span>{{ item.login_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item label="Painter" prop="painterId">
                  <div class="item-value level-3" v-if="!canAssignToIntall">
                    <span v-if="orderForm && orderForm.painter_person">
                      <CirCleText
                        :text="orderForm.painter_person.login_name"
                        :size="20"
                        :fontSize="'10px'"
                        :customBgColor="orderForm.painter_person.color"
                      />
                      {{ orderForm.painter_person.login_name }}
                    </span>
                    <span class="text-[#9DA7B8]" v-else>Not yet</span>
                  </div>
                  <div class="flex items-center" v-else>
                    <CirCleText
                      :text="orderForm.painter_person.login_name"
                      v-if="orderForm.painter_person"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="orderForm.painter_person.color"
                    />
                    <el-select
                      v-model="painterId"
                      :placeholder="transformI18n('common.empty')"
                      filterable
                      clearable
                      class="w-[240px] mr-[10px]"
                      :loading="getStaffOptionsLoading"
                      :remote-method="getStaffOptions"
                      remote
                      @change="handChangeStaffName('painter')"
                      :disabled="assignToLoading || !installId"
                    >
                      <el-option
                        v-for="item in staffOptions"
                        :key="item.id"
                        :label="item.login_name"
                        :value="item.id"
                      >
                        <div class="flex items-center">
                          <CirCleText
                            :text="item.login_name"
                            :size="20"
                            :fontSize="'10px'"
                            :customBgColor="item.color"
                          />
                          <span>{{ item.login_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <el-form-item label="Electrician" prop="electricianId">
                  <div class="item-value level-3" v-if="!canAssignToIntall">
                    <span v-if="orderForm && orderForm.electrician_person">
                      <CirCleText
                        :text="orderForm.electrician_person.login_name"
                        :size="20"
                        :fontSize="'10px'"
                        :customBgColor="orderForm.electrician_person.color"
                      />
                      {{ orderForm.electrician_person.login_name }}
                    </span>
                    <span class="text-[#9DA7B8]" v-else>Not yet</span>
                  </div>
                  <div class="flex items-center" v-else>
                    <CirCleText
                      :text="orderForm.electrician_person.login_name"
                      v-if="orderForm.electrician_person"
                      :size="20"
                      :fontSize="'10px'"
                      :customBgColor="orderForm.electrician_person.color"
                    />
                    <el-select
                      v-model="electricianId"
                      :placeholder="transformI18n('common.empty')"
                      filterable
                      clearable
                      class="w-[240px] mr-[10px]"
                      :loading="getStaffOptionsLoading"
                      :remote-method="getStaffOptions"
                      remote
                      @change="handChangeStaffName('electrician')"
                      :disabled="assignToLoading || !installId"
                    >
                      <el-option
                        v-for="item in staffOptions"
                        :key="item.id"
                        :label="item.login_name"
                        :value="item.id"
                      >
                        <div class="flex items-center">
                          <CirCleText
                            :text="item.login_name"
                            :size="20"
                            :fontSize="'10px'"
                            :customBgColor="item.color"
                          />
                          <span>{{ item.login_name }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box" />
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Delivery
                </div>
                <div class="item-value level-3" v-if="!canUpdateDate">
                  <span v-if="orderForm.delivery">
                    {{ formatTime(orderForm.delivery, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="orderForm.delivery"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Install Booked
                </div>
                <div class="item-value level-3">
                  <span v-if="orderForm.installation_date">
                    {{
                      formatTime(
                        orderForm.installation_date,
                        "DD/MM/YYYY HH:mm"
                      )
                    }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Date Installed
                </div>
                <div class="item-value level-3">
                  <span v-if="orderForm.installed_date">
                    {{ formatTime(orderForm.installed_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="item-box">
                <div class="text-[#656F7D] text-xs font-normal mb-2.5">
                  Completion Date
                </div>
                <div class="item-value level-3" v-if="!canUpdateDate">
                  <span v-if="orderForm.completion_date">
                    {{ formatTime(orderForm.completion_date, "DD/MM/YYYY") }}
                  </span>
                  <span class="text-[#9DA7B8]" v-else>Not yet</span>
                </div>
                <el-date-picker
                  v-model="orderForm.completion_date"
                  type="date"
                  class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                  :placeholder="transformI18n('common.select')"
                  format="DD/MM/YYYY"
                  value-format="YYYY/MM/DD"
                  @change="val => formChange()"
                  v-else
                />
              </el-col>
            </el-row>
          </div>
          <div class="supplier-list border-b border-[#E9EBF0] mt-2.5 mb-2.5">
            <p
              class="w-full flex justify-between items-center text-[#2A2E34] text-sm font-medium mb-2.5"
            >
              <span>Supplier</span>
              <span
                v-if="hasAuth('addSupplierForOrder')"
                @click.stop="toAddSupplier"
                class="flex items-center pt-2 pb-2 pl-2.5 pr-2.5 rounded-md text-[#9B3CE5] bg-[#ffffff] border border-[#E9EBF0]"
              >
                <span class="iconfont just-icon-circle-add mr-[5px]" /> Add New
              </span>
            </p>
            <div class="pc-item">
              <el-row :gutter="10">
                <el-col :span="2" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">ID</div>
                </el-col>
                <el-col :span="5" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">
                    Supplier Name
                  </div>
                </el-col>
                <el-col :span="3" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">
                    Order Date
                  </div>
                </el-col>
                <el-col :span="4" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">
                    Supplier Ready Date
                  </div>
                </el-col>
                <el-col :span="4" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">Amount</div>
                </el-col>
                <el-col :span="5" class="item-box">
                  <div class="text-[#656F7D] text-xs font-normal">Note</div>
                </el-col>
              </el-row>
              <div
                v-if="
                  orderForm &&
                  orderForm.supplier_list &&
                  orderForm.supplier_list.length
                "
              >
                <el-row
                  v-for="(supplier, index) in orderForm.supplier_list"
                  :key="index"
                  :gutter="10"
                >
                  <el-col :span="2" class="item-box">
                    <div class="item-value level-3">
                      {{ supplier.id }}
                    </div>
                  </el-col>
                  <el-col :span="5" class="item-box" v-if="supplier.supplier">
                    <div class="item-value level-3">
                      {{ supplier.supplier.name }}
                    </div>
                  </el-col>
                  <el-col :span="3" class="item-box">
                    <div class="item-value level-3">
                      {{ formatTime(supplier.order_date, "DD/MM/YYYY") }}
                    </div>
                  </el-col>
                  <el-col :span="4" class="item-box">
                    <el-date-picker
                      v-model="supplier.supplier_received_date"
                      type="date"
                      class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                      :placeholder="transformI18n('common.select')"
                      format="DD/MM/YYYY"
                      value-format="YYYY/MM/DD"
                      :disabled="!hasAuth('addSupplierForOrder')"
                      @change="
                        val => supplierReceivedDateChange(val, supplier, 'date')
                      "
                    />
                  </el-col>
                  <el-col :span="4" class="item-box">
                    <el-input-number
                      v-model="supplier.supplier_amount"
                      :min="0"
                      :controls="false"
                      :disabled="!hasAuth('addSupplierForOrder')"
                      :placeholder="transformI18n('common.input')"
                      class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
                      @change="
                        val =>
                          supplierReceivedDateChange(
                            val,
                            supplier,
                            'supplier_amount'
                          )
                      "
                    />
                  </el-col>
                  <el-col :span="5" class="item-box">
                    <el-input
                      v-model="supplier.note"
                      :maxlength="30"
                      :controls="false"
                      clearable
                      :disabled="!hasAuth('addSupplierForOrder')"
                      :placeholder="transformI18n('common.input')"
                      class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
                      @change="
                        val => supplierReceivedDateChange(val, supplier, 'note')
                      "
                    />
                  </el-col>
                  <el-col v-if="hasAuth('removeSupplierForOrder')" :span="1">
                    <i
                      @click="removeSupplier(supplier)"
                      class="cursor-pointer text-[#9B3CE5] iconfont just-icon-cancelled mr-[5px] ml-[10px]"
                    />
                  </el-col>
                </el-row>
              </div>
              <div v-else>
                <el-row>
                  <el-col :span="2" class="item-box">
                    <div class="item-value level-3">
                      <span class="text-[#9DA7B8]">Not yet</span>
                    </div>
                  </el-col>
                  <el-col :span="5" class="item-box">
                    <div class="item-value level-3">
                      <span class="text-[#9DA7B8]">Not yet</span>
                    </div>
                  </el-col>
                  <el-col :span="4" class="item-box">
                    <div class="item-value level-3">
                      <span class="text-[#9DA7B8]">Not yet</span>
                    </div>
                  </el-col>
                  <el-col :span="5" class="item-box">
                    <div class="item-value level-3">
                      <span class="text-[#9DA7B8]">Not yet</span>
                    </div>
                  </el-col>
                  <el-col :span="5" class="item-box">
                    <div class="item-value level-3">
                      <span class="text-[#9DA7B8]">Not yet</span>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
            <div class="mobile-item">
              <div
                class="supplier-item-block"
                v-for="(supplier, index) in orderForm.supplier_list"
                :key="index"
              >
                <div class="supplier-name flex justify-between">
                  <span>{{ supplier.id }}</span>
                  <span
                    v-if="hasAuth('removeSupplierForOrder')"
                    class="iconfont just-icon-delete ml-2 primary-color"
                    @click="removeSupplier(supplier)"
                  />
                </div>
                <el-row :gutter="15" class="px-[15px]">
                  <el-col :xs="12" :sm="6" class="supplier-item">
                    <div class="level-2">Supplier Name</div>
                    <div class="item-value level-3">
                      {{ supplier.supplier.name }}
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="6" class="supplier-item">
                    <div class="level-2">Order Date</div>

                    <div class="item-value level-3">
                      {{ formatTime(supplier.order_date, "DD/MM/YYYY") }}
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="6" class="supplier-item">
                    <div class="level-2">Supplier Received Date</div>
                    <div
                      class="item-value level-3 text-over"
                      v-if="orderForm.result == 'cancelled'"
                    >
                      {{
                        formatTime(
                          supplier.supplier_received_date,
                          "DD/MM/YYYY"
                        ) || "-"
                      }}
                    </div>
                    <el-date-picker
                      v-model="supplier.supplier_received_date"
                      v-else
                      type="date"
                      :disabled="!hasAuth('addSupplierForOrder')"
                      class="border border-[#E9EBF0] rounded-[6px] p-[5px] max-w-[100%]"
                      :placeholder="transformI18n('common.select')"
                      format="DD/MM/YYYY"
                      value-format="YYYY/MM/DD"
                      @change="
                        val => supplierReceivedDateChange(val, supplier, 'date')
                      "
                    />
                  </el-col>
                  <el-col :xs="12" :sm="6" class="supplier-item">
                    <div class="level-2">Amount</div>
                    <div class="item-value level-3">
                      <el-input-number
                        v-model="supplier.supplier_amount"
                        :min="0"
                        :controls="false"
                        :disabled="!hasAuth('addSupplierForOrder')"
                        :placeholder="transformI18n('common.input')"
                        class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
                        @change="
                          val =>
                            supplierReceivedDateChange(
                              val,
                              supplier,
                              'supplier_amount'
                            )
                        "
                      />
                    </div>
                  </el-col>
                  <el-col :xs="12" :sm="6" class="supplier-item">
                    <div class="level-2">Note</div>
                    <div
                      class="item-value level-3"
                      v-if="orderForm.result == 'cancelled'"
                    >
                      {{ supplier.note || "-" }}
                    </div>
                    <el-input
                      v-model="supplier.note"
                      v-else
                      :maxlength="30"
                      :controls="false"
                      clearable
                      :disabled="!hasAuth('addSupplierForOrder')"
                      :placeholder="transformI18n('common.input')"
                      class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
                      @change="
                        val => supplierReceivedDateChange(val, supplier, 'note')
                      "
                    />
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>

          <!-- sales file -->
          <div>
            <div
              class="item-title level-1 w-full mt-5 flex justify-between items-center"
            >
              <span>{{ transformI18n("appointment.leadsAttachment") }}</span>
            </div>
            <div>
              <div
                v-if="
                  !(
                    orderForm &&
                    orderForm.contracts &&
                    orderForm.contracts.length
                  )
                "
                class="empty mt-[20px]"
              >
                Not Yet
              </div>
              <el-row
                class="flex justify-between items-center border text-base pl-2 pr-2 pb-1.5 pt-1.5 mt-5 border-[#E9EBF0] rounded-lg"
                v-for="(contract, index) in orderForm.contracts"
                :key="index"
              >
                <span
                  ><i class="text-[#9B3CE5] iconfont just-icon-upload" />{{
                    contract.name
                  }}</span
                >
                <i
                  @click="previewFile(contract)"
                  class="cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
                />
              </el-row>
            </div>
          </div>
          <div>
            <div
              class="item-title level-1 w-full mt-5 flex justify-between items-center"
            >
              <span>{{ transformI18n("appointment.cmAttachment") }}</span>
              <UploadFile
                v-if="hasAuth('uploadFileForOrder') && props.editPage"
                ref="uploadFileRef"
                :otherParam="uploadCM"
                :btnTxt="'Upload'"
                :btnIcon="'just-icon-upload-line'"
                @fileChange="fileChange"
                :btnClass="'uploadBtn'"
              />
            </div>
            <div>
              <div
                v-if="
                  !(
                    orderForm &&
                    orderForm.product_files &&
                    orderForm.product_files.length
                  )
                "
                class="empty mt-[20px]"
              >
                Not Yet
              </div>
              <el-row
                class="flex justify-between items-center border text-base pl-2 pr-2 pb-1.5 pt-1.5 mt-5 border-[#E9EBF0] rounded-lg"
                v-for="(productFile, index) in orderForm.product_files"
                :key="index"
              >
                <span
                  ><i class="text-[#9B3CE5] iconfont just-icon-upload" />{{
                    productFile.name
                  }}</span
                >

                <div>
                  <i
                    @click="previewFile(productFile)"
                    class="cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
                  />
                  <span
                    v-if="hasAuth('delOrderUploadFile') && props.editPage"
                    class="iconfont just-icon-delete ml-2 primary-color"
                    @click="delAttachment(productFile)"
                  />
                </div>
              </el-row>
            </div>
          </div>

          <div
            class="w-full mt-5 item-title level-1 flex justify-between items-center"
          >
            <span>Invoice Attachments</span>
            <UploadFile
              v-if="hasAuth('uploadFileForOrder') && props.editPage"
              ref="uploadFileRef"
              :otherParam="uploadInvoice"
              :btnTxt="'Upload'"
              :btnIcon="'just-icon-upload-line'"
              @fileChange="fileChange"
              :btnClass="'uploadBtn'"
            />
          </div>

          <div>
            <div
              class="empty mt-[20px]"
              v-if="!(orderForm.invoice && orderForm.invoice.length)"
            >
              Not yet
            </div>
            <el-row
              class="flex justify-between items-center border text-base pl-2 pr-2 pb-1.5 pt-1.5 mt-5 border-[#E9EBF0] rounded-lg"
              v-for="(item, index) in orderForm.invoice"
              :key="index"
            >
              <span
                ><i class="text-[#9B3CE5] iconfont just-icon-upload" />{{
                  item.name
                }}</span
              >
              <div>
                <i
                  @click="previewFile(item)"
                  class="cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
                />
                <span
                  v-if="hasAuth('delOrderUploadFile') && props.editPage"
                  class="iconfont just-icon-delete ml-2 primary-color"
                  @click="delAttachment(item)"
                />
              </div>
            </el-row>
          </div>
          <div
            v-if="orderInfo?.lead && orderInfo.lead.product_category_id == 5"
            class="w-full mt-5 item-title level-1 flex justify-between items-center"
          >
            <span>Trade Attachments</span>
            <UploadFile
              v-if="hasAuth('uploadFileForOrder') && props.editPage"
              ref="uploadFileRef"
              :otherParam="uploadTrade"
              :btnTxt="'Upload'"
              :btnIcon="'just-icon-upload-line'"
              @fileChange="fileChange"
              :btnClass="'uploadBtn'"
            />
          </div>

          <div
            v-if="orderInfo?.lead && orderInfo.lead.product_category_id == 5"
          >
            <div
              class="empty mt-[20px]"
              v-if="!(orderForm.trade && orderForm.trade.length)"
            >
              Not yet
            </div>
            <el-row
              class="flex justify-between items-center border text-base pl-2 pr-2 pb-1.5 pt-1.5 mt-5 border-[#E9EBF0] rounded-lg"
              v-for="(item, index) in orderForm.trade"
              :key="index"
            >
              <span
                ><i class="text-[#9B3CE5] iconfont just-icon-upload" />{{
                  item.name
                }}</span
              >
              <div>
                <i
                  @click="previewFile(item)"
                  class="cursor-pointer iconfont just-icon-download-line text-[#4F5762]"
                />
                <span
                  v-if="hasAuth('delOrderUploadFile') && props.editPage"
                  class="iconfont just-icon-delete ml-2 primary-color"
                  @click="delAttachment(item)"
                />
              </div>
            </el-row>
          </div>
        </div>
      </div>
    </div>
    <addSupplierDialog ref="addSupplierDialogRef" />
    <addApprovedDateDialog ref="addApprovedDateDialogRef" />

    <el-dialog
      v-model="decisionDialogVisible"
      :title="'Update Date'"
      align-center
      :width="'550px'"
    />
  </div>
</template>

<style lang="scss" scoped>
.detail-content {
  display: flex;
  justify-content: space-between;

  .general-info {
    flex-grow: 1;
    min-height: 350px;
    max-height: calc(100vh - 360px);
    padding: 0 10px;
    overflow: auto;
  }

  .item-value {
    line-height: 32px;
  }
}

.pc-item {
  display: block;
}

.mobile-item {
  display: none;
}

@media screen and (width <= 768px) {
  .pc-item {
    display: none;
  }

  .mobile-item {
    display: block;

    .supplier-item-block {
      margin-top: 20px;
      border: 1px solid #e9ebf0;
      border-radius: 6px;
    }

    .supplier-item {
      padding: 5px 15px;
      margin: 0;
      border-radius: 6px;

      :deep(.el-date-editor.el-input) {
        width: 100%;
      }

      :deep(.el-input-number) {
        width: 100%;
      }
    }

    .supplier-name {
      padding: 5px 15px;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      background: #f7f8f9;
    }

    .level-2 {
      margin-bottom: 8px;
    }
  }
}

@media screen and (width <= 480px) {
  .base-item,
  .install-info,
  .supplier-list {
    .el-col {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }

  :deep(.el-date-editor.el-input) {
    width: 100%;
  }
}
</style>
