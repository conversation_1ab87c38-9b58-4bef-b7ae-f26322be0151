<script setup lang="ts">
import { reactive, ref, onMounted, watch, computed } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { formatTime } from "@/utils/form";
import addPaymentDialog from "@/components/Addpayment/addPaymentDialog.vue";
import { typeOption } from "./data.ts";
import { ElLoading, ElMessage } from "element-plus";
import {
  orderControllerDelPayment,
  orderControllerAddPayment
} from "@/api/admin/order";
import { hasAuth } from "@/router/utils";

const client = ref(null);
const form = reactive({
  id: null,
  lead_id: null,
  received_date: "",
  result: "new_order",
  under_sell: null,
  estimated_comm: null,
  start_date: null,
  product_staff_id: null,
  product_staff: null,
  cm_id: null,
  cm_booked_date: null,
  cm_received_date: null,
  on_product_date: null,
  ready_date: null,
  install_id: null,
  installation_date: null,
  completion_date: null,
  reason: null,
  finance_company: null,
  finance_type: null,
  finance_amount: null,
  finance_approved_date: null,
  finance_approved_no: null,
  payments: [],
  cm: null,
  install: null,
  lead: null
});
const dialogPanel = ref();
const formRef = ref(null);
const addPaymentDialogRef = ref(null);
const paid_amount = ref(0);
const outstanding = ref(0);
const payments = ref([]);
const props = defineProps({
  allStaff: {
    type: Array,
    default() {
      return [];
    }
  },
  orderInfo: {
    type: Object,
    default() {
      return {};
    }
  },
  editPage: {
    type: Boolean,
    default: true
  }
});
const emit = defineEmits<{
  (e: "reloadOrderInfo", val: Object): void;
}>();
watch(
  () => props.orderInfo,
  _newVal => {
    init(_newVal);
  },
  {
    deep: true,
    immediate: true
  }
);
watch(
  () => payments,
  _newVal => {
    calculatedAmount();
  },
  {
    deep: true,
    immediate: true
  }
);
const canEdit = computed(() => {
  if (
    form.result !== "cancelled" &&
    form.result !== "completion" &&
    props.editPage
  ) {
    return true;
  }
  return false;
});
function getNewPaymentInfo() {
  return form;
}

function calculatedAmount() {
  paid_amount.value = payments.value.reduce((acc, cur) => {
    let curAmount = 0;
    if (cur.type == "Refund") {
      curAmount = -cur.amount;
    } else if (cur.type != "Unpaid") {
      curAmount = cur.amount;
    }
    return parseFloat(acc.toFixed(2)) + parseFloat(curAmount.toFixed(2));
  }, 0);
  if (form.lead) {
    outstanding.value =
      parseFloat(form.lead.sold.toFixed(2)) -
      parseFloat(paid_amount.value.toFixed(2));
  }
}

function init(data = null) {
  if (data) {
    data = JSON.parse(JSON.stringify(data));
    Object.assign(form, data);
    if (data.lead) {
      client.value = data.lead.client;
    }
    if (data.payments) {
      payments.value = data.payments;
    }
  }
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: formRef.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function paymentChange(value, payment) {
  if (!(payment && payment.id)) {
    return;
  }
  const sendForm = {
    order_payment_id: payment.id,
    detail: payment.detail,
    type: payment.type,
    amount: payment.amount,
    received_date: payment.received_date,
    name: payment.name,
    payment_type: "order"
  };

  dialogLoading();
  orderControllerAddPayment(form.id, sendForm)
    .then(res => {
      dialogPanel.value.close();
      console.log(res.success);
      if (res.success) {
        ElMessage.success(res.message);
      } else {
        console.log("=======");
        ElMessage.error(res.message);
      }
      emit("reloadOrderInfo", {});
    })
    .catch(_err => {
      dialogPanel.value.close();
    });
}

function toAddPayment() {
  addPaymentDialogRef.value.show({ id: form.id }).then(_res => {
    if (_res) {
      emit("reloadOrderInfo", {});
    }
  });
}

function delPayment(payment) {
  orderControllerDelPayment(form.id, {
    payment_id: payment.id,
    type: "order"
  }).then(res => {
    if (res.success) {
      emit("reloadOrderInfo", {});
      ElMessage.success(res.message);
    } else {
      ElMessage.error(res.message);
    }
  });
}

onMounted(() => {});
defineExpose({ getNewPaymentInfo });
</script>

<template>
  <div class="mb-[20px]">
    <el-form
      ref="formRef"
      :model="form"
      label-width="120"
      class="w-[99/100]"
      label-position="top"
    >
      <div class="detail-content">
        <div class="general-info">
          <div class="item-block">
            <!-- client info -->
            <div class="item-title level-1">Summary</div>
            <el-row :gutter="10" class="summary-box">
              <el-col :xs="12" :sm="6" class="summary-item active">
                <span class="mr-1 iconfont primary-color just-icon-file-text" />
                <div class="summary-right">
                  <div class="level-2">Client</div>
                  <div class="item-value level-3">
                    <span
                      >{{ client.title }} {{ client.surname }}
                      {{ client.given_name }}</span
                    >
                  </div>
                  <div class="level-2 mt-[5px]">Contract Number</div>
                  <div class="item-value level-3">
                    <span
                      class="mr-1 iconfont primary-color just-icon-upload text-[14px]"
                    />
                    <span>{{ client.phone }}</span>
                  </div>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="summary-item">
                <span class="title">Sold Amount</span>
                <span class="desc">${{ form.lead ? form.lead.sold : 0 }}</span>
              </el-col>
              <el-col :xs="12" :sm="6" class="summary-item">
                <span class="title">Paid Amount</span>
                <span class="desc">${{ paid_amount.toFixed(2) }}</span>
              </el-col>
              <el-col :xs="12" :sm="6" class="summary-item">
                <span class="title">Outstanding</span>
                <span class="desc red">${{ outstanding.toFixed(2) || 0 }}</span>
              </el-col>
            </el-row>
          </div>

          <!-- management -->
          <div class="item-title level-1">
            <span>Payment Progress</span>
          </div>
          <div
            class="payment-item-block"
            v-for="(payItem, index) in payments"
            :key="index"
          >
            <div class="payment-name flex justify-between">
              <span>{{ payItem.name }}</span>
              <span
                v-if="canEdit && hasAuth('updatePayment')"
                class="iconfont just-icon-delete ml-2 primary-color"
                @click="delPayment(payItem)"
              />
            </div>
            <el-row :gutter="15" class="px-[15px]">
              <el-col :xs="12" :sm="6" class="payment-item">
                <div class="level-2">Date Received</div>
                <div class="item-value level-3" v-if="hasAuth('updatePayment')">
                  <el-date-picker
                    v-model="payItem.received_date"
                    type="date"
                    class="border border-[#E9EBF0] rounded-[6px] p-[5px] grow"
                    :placeholder="transformI18n('common.select')"
                    format="YYYY/MM/DD"
                    value-format="YYYY/MM/DD"
                    @change="val => paymentChange(val, payItem)"
                  />
                </div>
                <div v-else class="item-value level-3">
                  <span v-if="payItem.received_date">{{
                    formatTime(payItem.received_date, "DD/MM/YYYY")
                  }}</span>
                  <span v-else class="empty">Empty</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="payment-item">
                <div class="level-2">Amount Received</div>

                <div class="item-value level-3" v-if="hasAuth('updatePayment')">
                  <el-input-number
                    v-model="payItem.amount"
                    :min="1"
                    :precision="2"
                    :placeholder="transformI18n('common.input')"
                    controls-position="right"
                    @change="val => paymentChange(val, payItem)"
                    class="border border-[#E9EBF0] rounded-[6px] grow"
                  />
                </div>
                <div v-else class="item-value level-3">
                  <span>$ {{ payItem.amount || 0 }}</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="payment-item">
                <div class="level-2">Receipt Details</div>
                <div
                  class="item-value level-3 text-over"
                  v-if="hasAuth('updatePayment')"
                >
                  <el-input
                    v-model="payItem.detail"
                    type="textarea"
                    size="small"
                    :rows="1"
                    :placeholder="transformI18n('common.input')"
                    autocomplete="off"
                    class="border border-[#E9EBF0] rounded-[6px] p-[5px]"
                    @change="val => paymentChange(val, payItem)"
                  />
                </div>
                <div v-else>
                  <div
                    class="item-value level-3 text-over"
                    v-if="payItem.detail"
                  >
                    {{ payItem.detail }}
                  </div>
                  <span v-else class="empty">Empty</span>
                </div>
              </el-col>
              <el-col :xs="12" :sm="6" class="payment-item">
                <div class="level-2">Type</div>
                <div class="item-value level-3" v-if="hasAuth('updatePayment')">
                  <el-select
                    v-model="payItem.type"
                    :placeholder="transformI18n('common.select')"
                    size="small"
                    @change="val => paymentChange(val, payItem)"
                    class="border border-[#E9EBF0] rounded-[6px] p-[5px] w-[100%]"
                  >
                    <el-option
                      v-for="item in typeOption"
                      :label="item"
                      :value="item"
                      :key="item"
                    />
                  </el-select>
                </div>
                <div v-else class="item-value level-3">{{ payItem.type }}</div>
              </el-col>
            </el-row>
          </div>
          <el-row class="total-item" v-if="payments.length">
            <el-col :xs="12" :sm="6"> Total: </el-col>
            <el-col :xs="12" :sm="6"> ${{ paid_amount.toFixed(2) }} </el-col>
          </el-row>
          <div
            v-if="canEdit && hasAuth('updatePayment')"
            class="add-box flex items-center justify-center primary-color"
            @click="toAddPayment"
          >
            <span class="iconfont just-icon-circle-add mr-[10px]" />
            <span class="text-[14px]">Add New Payment</span>
          </div>

          <div class="item-title level-1 mt-[20px]">Finance</div>
          <el-row :gutter="10">
            <el-col :span="8" class="item-box">
              <el-form-item :label="'Company'" prop="finance_company">
                <el-input
                  v-model="form.finance_company"
                  :disabled="!canEdit"
                  :placeholder="transformI18n('common.input')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" class="item-box">
              <el-form-item :label="'Type'" prop="finance_type">
                <el-select
                  v-model="form.finance_type"
                  :disabled="!canEdit"
                  clearable
                  :placeholder="transformI18n('common.select')"
                >
                  <el-option
                    v-for="item in typeOption"
                    :label="item"
                    :value="item"
                    :key="item"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="item-box">
              <el-form-item :label="'Amount'" prop="finance_amount">
                <el-input
                  v-model="form.finance_amount"
                  :disabled="!canEdit"
                  :placeholder="transformI18n('common.input')"
                  ><template #prefix v-if="form.finance_amount"
                    ><span class="mr-2">$</span>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8" class="item-box">
              <el-form-item
                :label="'Date Approved'"
                prop="finance_approved_date"
              >
                <el-date-picker
                  v-model="form.finance_approved_date"
                  type="date"
                  class="w-full"
                  :placeholder="transformI18n('common.select')"
                  format="YYYY/MM/DD"
                  value-format="YYYY/MM/DD"
                  :disabled="!canEdit"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8" class="item-box">
              <el-form-item :label="'Approved No.'" prop="finance_approved_no">
                <el-input
                  v-model="form.finance_approved_no"
                  :disabled="!canEdit"
                  :placeholder="transformI18n('common.input')"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-form>
    <addPaymentDialog ref="addPaymentDialogRef" />
  </div>
</template>

<style lang="scss" scoped>
.level-2 {
  margin-bottom: 8px;
}

.detail-content {
  display: flex;
  justify-content: space-between;

  .general-info {
    flex-grow: 1;
    min-height: 350px;
    max-height: calc(100vh - 360px);
    padding: 0 10px;
    overflow: auto;
  }

  .notes-box {
    flex-shrink: 0;
    width: 450px;
    background: #fff;
    border: 1px solid #e9ebf0;
  }

  .item-block {
    padding: 0 0 20px;
    margin-top: 20px;
    border: 0;
  }

  .payment-item-block {
    margin-top: 20px;
    border: 1px solid #e9ebf0;
    border-radius: 6px;
  }

  .payment-item {
    padding: 5px 15px;
    margin: 0;
    border-radius: 6px;

    :deep(.el-date-editor.el-input) {
      width: 100%;
    }

    :deep(.el-input-number) {
      width: 100%;
    }
  }

  .item-box {
    min-height: 50px;
    margin: 20px 0 0;
  }

  .item-value {
    line-height: 1.5em;
  }
}

.payment-name {
  padding: 5px 15px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  background: #f7f8f9;
}

.input-result-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.edit-client {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.noEdit {
  cursor: not-allowed;
}

.just-icon-file-text {
  flex-grow: 0;
  flex-shrink: 0;
  padding: 0;
  margin-right: 15px;
  font-size: 40px;
  line-height: 1em;
}

.summary-box {
  padding: 10px;
  background: #f9f5ff;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 110px;
  padding: 10px 15px;
  margin: 0;
  border-radius: 20px;

  &.active {
    flex-direction: row;
    align-items: flex-start;
    justify-content: flex-start;
    background: #fff;
    box-shadow: 0 2px 10px 0 rgb(0 0 0 / 10%);
  }

  .desc {
    margin-top: 15px;
    font-size: 20px;
    font-weight: 700;
  }
}

.summary-right {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  .item-value {
    font-size: 14px;
    font-weight: 500;
  }

  .level-2 {
    margin-bottom: 2px;
  }
}

.red {
  color: red;
}

.add-box {
  height: 60px;
  margin: 20px 0;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid #e9ebf0;
  border-radius: 8px;
}

@media screen and (width <= 480px) {
  .el-col {
    flex: 0 0 100%;
    max-width: 100%;
  }

  .detail-content .item-box {
    margin-top: 10px;
  }

  .summary-item {
    height: auto;

    .desc {
      margin-top: 0;
    }
  }
}

.total-item {
  padding: 10px 20px;
  margin-top: 10px;
  font-size: 18px;
  font-weight: 700;
  background: #f7f8f9;
  border: 1px solid #e9ebf0;

  .el-col {
    margin: 0;
  }
}
</style>
