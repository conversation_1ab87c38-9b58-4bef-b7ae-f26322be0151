<script setup lang="ts">
import { ref, onMounted, nextTick, reactive } from "vue";
import { transformI18n } from "@/plugins/i18n";
import ListTable from "./components/listTable.vue";
import OrderDetail from "./detail.vue";
import LeadsDetail from "@/views/leads/leads-detail.vue";
import ResultDialog from "@/components/ResultDialog";
import dayjs from "dayjs";
import { accountControllerIndex } from "@/api/admin/admin-account";
import ConvertToOrderDialog from "@/components/ConvertToOrderDialog";
import { orderControllerGetSummaryInfo } from "@/api/admin/order";
import CreateServiceDialog from "@/components/CreateServiceDialog";
import MapView from "@/views/leads/components/LeadsMapView.vue";
import { ElMessage } from "element-plus";
import { storageLocal } from "@pureadmin/utils";
import ReportDownDialog from "@/components/ReportDownDialog";
import { orderControllerGeneratorReportPDF } from "@/api/admin/order";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const companyRegion = ref([]);
const mapCompanyRegion = ref([]);

const activeName = ref(null);
// current tab List total num and show beside the tab label
const listCount = ref({
  review: 0,
  all: 0,
  new_order: 0,
  cm: 0,
  cmReceived: 0,
  onProduction: 0,
  ready: 0,
  installation: 0,
  outstanding: 0,
  hold: 0,
  completion: 0,
  cancelled: 0
});
const convertToOrderDialogRef = ref(null);
const createServiceDialogRef = ref(null);
const resultDialogRef = ref(null);
const allStaffList = ref();
const LeadsDetailRef = ref(null);
const orderDetailRef = ref(null);
const MapMarksNum = ref(0);
const reportDownDialogRef = ref(null);
const downloadReportLoading = ref(false);
const roundNumber = ref(0);
// The value of tabName must be consistent with the order result except for 'all' and 'review'
const tabsList = [
  {
    label: transformI18n("orders.review"),
    name: "review",
    component: ListTable,
    props: { tabName: "review", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.all"),
    name: "all",
    component: ListTable,
    props: { tabName: "all", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("common.new"),
    name: "new_order",
    component: ListTable,
    props: { tabName: "new_order", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_cm"),
    name: "cm",
    component: ListTable,
    props: { tabName: "cm", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_cmReceived"),
    name: "cmReceived",
    component: ListTable,
    props: { tabName: "cmReceived", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_onProduction"),
    name: "onProduction",
    component: ListTable,
    props: { tabName: "onProduction", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_ready"),
    name: "ready",
    component: ListTable,
    props: { tabName: "ready", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_installation"),
    name: "installation",
    component: ListTable,
    props: { tabName: "installation", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_extra"),
    name: "extra",
    component: ListTable,
    props: { tabName: "extra", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_outstanding"),
    name: "outstanding",
    component: ListTable,
    props: { tabName: "outstanding", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("orders.result_hold"),
    name: "hold",
    component: ListTable,
    props: { tabName: "hold", companyRegion: companyRegion.value }
  },
  {
    label: "In Council",
    name: "inCouncil",
    component: ListTable,
    props: { tabName: "inCouncil", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("common.completion"),
    name: "completion",
    component: ListTable,
    props: { tabName: "completion", companyRegion: companyRegion.value }
  },
  {
    label: transformI18n("common.cancelled"),
    name: "cancelled",
    component: ListTable,
    props: { tabName: "cancelled", companyRegion: companyRegion.value }
  }
];

const reportForm = reactive({
  subCategoryIds: []
});

// Dynamic components refresh the data
const loadDataFlag = reactive({
  review: "",
  all: "",
  new_order: "",
  cm: "",
  cmReceived: "",
  onProduction: "",
  ready: "",
  installation: "",
  outstanding: "",
  hold: "",
  completion: "",
  cancelled: ""
});

const mapViewRef = ref(null);
const changeTab = (tab, event) => {
  nextTick(() => {
    if (activeName.value === "mapView" && mapViewRef.value) {
      mapViewRef.value.initViewMap();
    } else {
      tabsList.forEach(item => {
        if ("tab_" + item.name === tab.props.name) {
          loadDataFlag[item.name] = event._vts + "";
        }
      });
    }
    storageLocal().setItem(`orderActiveName`, activeName.value);
  });
};
// Updates the total number of lists for the specified listTable
function toPassNewInfo(data: any, type = "draft") {
  listCount.value[type] = data.total;
}

// view details page
function toDetail(row, type = "review") {
  if (type === "review") {
    // open leads detail
    toLeadsDetail(row);
  } else {
    orderDetailRef.value.show(row);
  }
}

function convertToOrder(row, type = "review") {
  if (type == "review") {
    const data = {
      lead_id: row.id,
      stagnation_order_desposit: row.stagnation_order_desposit
    };
    convertToOrderDialogRef.value.show(data).then(_res => {
      reLoadTableData();
    });
  }
}

function createService(row) {
  if (row.result == "completion") {
    if (row.lead && row.lead.service) {
      ElMessage.error("Unfinished services exist, unable to create new.");
      return;
    }
    const data = {
      order_id: row.id
    };
    createServiceDialogRef.value.show(data).then(_res => {
      reLoadTableData();
    });
  }
}
// view details page
function toLeadsDetail(row) {
  row.enquiry_date = dayjs(row.enquiry_date).format("YYYY-MM-DD");
  const canEdit = false;
  LeadsDetailRef.value.show(row, "leads", canEdit);
}

function beforeResultClose(done) {
  activeName.value = "tab_review";
  loadDataFlag["review"] =
    "" + (Math.floor(Math.random() * (1000 - 10 + 1)) + 10);
  done();
}

function reLoadTableData() {
  tabsList.forEach(item => {
    if ("tab_" + item.name === activeName.value) {
      loadDataFlag[item.name] =
        "" + (Math.floor(Math.random() * (1000 - 10 + 1)) + 10);
    }
    item.props.companyRegion = companyRegion.value;
  });
  console.log(tabsList);
  getSummaryInfo();
}

// get system all account
function getAccount() {
  // staff info
  const search = "";
  let filter = "";
  const sort = "";
  const _with = "roles";
  const withCount = "";
  const page = 1;
  const size = 666;
  const filterInfo = [];
  let companyRegionStr = null;
  if (companyRegion.value.length) {
    companyRegionStr = companyRegion.value.join(",");
  }
  filter = filterInfo.length === 0 ? "" : filterInfo.join();
  accountControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size,
    "",
    companyRegionStr
  ).then(res => {
    const { data } = res;
    allStaffList.value = data || [];
  });
}

// get every type order list total number
function getSummaryInfo() {
  let companyRegionStr = "";
  if (companyRegion.value.length) {
    companyRegionStr = companyRegion.value.join(",");
  }
  orderControllerGetSummaryInfo(companyRegionStr).then(res => {
    const { data } = res;
    if (data) {
      for (const key in data) {
        listCount.value[key] = data[key];
      }
    }
  });
}
function initData() {
  const lastTabActiveName = storageLocal().getItem(`orderActiveName`);
  activeName.value = lastTabActiveName || "tab_review";
  getAccount();
  getSummaryInfo();
}

function changeCompany() {
  getAccount();
  reLoadTableData();
  mapCompanyRegion.value = companyRegion.value;
}

function changeMapMarksNum(total) {
  MapMarksNum.value = total;
}

function toDetailFromMap(data) {
  orderDetailRef.value.show(data).then(_res => {
    roundNumber.value += 1;
  });
}

// Download Satisfaction Report
function downReportAction() {
  reportDownDialogRef.value.show(reportForm).then(data => {
    console.log(data);
    if (data) {
      let subCategoryIds = null;
      if (data.subCategoryIds) {
        subCategoryIds = data.subCategoryIds.join(",");
      }
      let companyRegionStr = "";
      if (companyRegion.value.length) {
        companyRegionStr = companyRegion.value.join(",");
      }
      downloadReportLoading.value = true;
      orderControllerGeneratorReportPDF(
        {
          subCategoryIds: subCategoryIds
        },
        companyRegionStr
      )
        .then(res => {
          downloadReportLoading.value = false;
          if (res.success && res.data) {
            window.open(res.data.url, "_blank");
            ElMessage.success(res.message);
          } else {
            ElMessage.error(res.message);
          }
        })
        .catch(_res => {
          downloadReportLoading.value = false;
          ElMessage.error("Failed to generate report!");
        });
    }
  });
}
onMounted(() => {
  initData();
});
</script>

<template>
  <el-card shadow="never" class="order-list">
    <div class="font-medium page-header">
      {{ transformI18n("menus.hsOrders") }}
    </div>
    <div class="page-action-box">
      <el-select
        v-if="userBaseInfo.isPlatformUser"
        class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
        v-model="companyRegion"
        @change="changeCompany"
        multiple
        collapse-tags
        collapse-tags-tooltip
        :placeholder="'Company'"
      >
        <el-option
          v-for="item in userBaseInfo.companyRegion"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>
    <el-tabs
      v-model="activeName"
      :class="
        userBaseInfo.isPlatformUser ? 'demo-tabs platform-tabs' : 'demo-tabs'
      "
      @tab-click="changeTab"
    >
      <el-tab-pane
        label="tabItem.label"
        :name="'tab_' + tabItem.name"
        v-for="(tabItem, index) in tabsList"
        :key="index"
      >
        <template #label>
          <span class="custom-tabs-label">
            <span>{{ tabItem.label }}</span>
            <el-tag
              type="info"
              effect="light"
              round
              v-if="listCount[tabItem.name] > 0"
            >
              {{ listCount[tabItem.name] }}
            </el-tag>
          </span>
        </template>
        <component
          :is="tabItem.component"
          v-if="activeName == 'tab_' + tabItem.name"
          :ref="`${tabItem.name}Ref`"
          @updateListSummary="getSummaryInfo"
          @toPassNewInfo="data => toPassNewInfo(data, tabItem.name)"
          @toDetail="row => toDetail(row, tabItem.name)"
          @convertToOrder="row => convertToOrder(row, tabItem.name)"
          @createService="row => createService(row)"
          :args="tabItem.props"
          :loadDataFlag="loadDataFlag[tabItem.name]"
          :allStaffList="allStaffList"
        />
      </el-tab-pane>
      <el-tab-pane label="Map View" name="mapView">
        <template #label>
          <span class="custom-tabs-label">
            <span>Map View</span>
            <el-tag type="info" effect="light" round v-if="MapMarksNum > 0">
              {{ MapMarksNum }}
            </el-tag>
          </span>
        </template>
        <mapView
          ref="mapViewRef"
          :dataType="'order'"
          :roundNumber="roundNumber"
          :companyRegion="mapCompanyRegion"
          @updateMapMarksNum="changeMapMarksNum"
          @toDetail="toDetailFromMap"
        />
      </el-tab-pane>
    </el-tabs>
    <el-button
      v-if="activeName != 'mapView'"
      type="primary"
      :loading="downloadReportLoading"
      @click="downReportAction"
      class="report-btn"
      >Generate and download Report</el-button
    >
    <leads-detail ref="LeadsDetailRef" :allStaff="allStaffList" />
    <OrderDetail
      ref="orderDetailRef"
      @closed="reLoadTableData"
      :allStaff="allStaffList"
      @createService="createService"
    />
    <ConvertToOrderDialog ref="convertToOrderDialogRef" />
    <ResultDialog ref="resultDialogRef" :before-close="beforeResultClose" />
    <CreateServiceDialog ref="createServiceDialogRef" />
    <ReportDownDialog ref="reportDownDialogRef" />
  </el-card>
</template>
<style lang="scss" scoped>
.demo-tabs {
  :deep(.el-tabs__header) {
    padding: 0 0 0 200px;
    -ms-overflow-style: none;
    scrollbar-width: none;

    ::-webkit-scrollbar {
      display: none;
    }
  }
}

.order-list {
  position: relative;
}

.report-btn {
  position: absolute;
  right: 16px;
  bottom: 22px;
}

.platform-tabs {
  // height: calc(100% - 50px);
  margin-top: 50px;
}

::v-deep(.platform-tabs.el-tabs .el-tabs__header) {
  padding: 0 10px;
}
</style>
