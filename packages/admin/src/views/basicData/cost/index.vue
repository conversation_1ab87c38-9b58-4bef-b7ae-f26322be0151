<script setup lang="ts">
import {
  costController<PERSON>ndex,
  costController<PERSON><PERSON>roy
} from "@/api/admin/basic-data";
import { columns, platformColumns } from "./cost_data";
import CostForm from "./form.vue";
import { reactive, ref, onMounted, watch } from "vue";
import DataTable from "@/components/DataTable";
import { transformI18n } from "@/plugins/i18n";
import RiEdit2Line from "@iconify-icons/ri/edit-2-line";
import { ElMessageBox, ElLoading } from "element-plus";
import searchLine from "@iconify-icons/ri/search-line";
import { useCommonStoreHook } from "@/store/modules/common";
import dayjs from "dayjs";
import { costControllerUpdate } from "@/api/admin/basic-data";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();
const companyRegion = ref([]);

const productCategoryList = ref([]);
const sourceList = ref([]);
const form = reactive({
  date: null,
  endDate: null,
  productIds: null,
  source_id: null,
  type: null,
  sub_source_id: null
});
const dataTableRef = ref(null);
const dataFormRef = ref(null);
const mv = ref();
const dialogPanel = ref();
const indexQuery = reactive({
  search: "",
  sort: "-id",
  filter: "",
  _with: "products,source,subsource,supplier",
  withCount: "",
  otherParam: "",
  size: 20
});
const props = defineProps({
  loadDataFlag: {
    type: String,
    default: null
  }
});
const typeList = ref([
  {
    id: 1,
    name: "Marketing"
  },
  {
    id: 2,
    name: "Product"
  }
]);
// Listen for changes in props properties
watch(
  () => props.loadDataFlag,
  (_newValue, _oldValue) => {
    loadData();
  }
);

function onEdit(row) {
  dataFormRef.value.show(row).then(() => {
    loadData();
  });
}

function onDelete(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>delete</strong> the <strong style='color:var(--el-color-primary)'>${row.type}</strong> cost?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    costControllerDestroy(row.id)
      .then(() => {
        dialogPanel.value.close();
        loadData();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

function loadData() {
  dataTableRef.value.loadData();
}

function tableQueryParams(form) {
  const filterInfo = [];
  const search = form.search || "";
  let subCategoryIds = null;
  if (form.productIds) {
    subCategoryIds = form.productIds.join(",");
  }
  if (companyRegion.value.length) {
    filterInfo.push("company_region:in:" + companyRegion.value.join("|"));
  }
  Object.keys(form).forEach(key => {
    console.log(form[key]);
    if (form[key]) {
      if (key == "date") {
        const startDate = dayjs(form.date[0]).format("YYYY-MM-DD HH:mm:ss");
        const endDate = dayjs(form.date[1]).format("YYYY-MM-DD 23:59:59");
        const dateName = "enquiry_date";
        filterInfo.push(`${dateName}:gte:${startDate}`);
        filterInfo.push(`${dateName}:lte:${endDate}`);
      } else if (key == "endDate") {
        const startDate = dayjs(form.endDate[0]).format("YYYY-MM-DD HH:mm:ss");
        const endDate = dayjs(form.endDate[1]).format("YYYY-MM-DD 23:59:59");
        const dateName = "end_date";
        filterInfo.push(`${dateName}:gte:${startDate}`);
        filterInfo.push(`${dateName}:lte:${endDate}`);
      } else if (key == "source_id") {
        if (form[key].startsWith("other-")) {
          filterInfo.push(
            `other_source:eq:${form[key].slice("other-".length)}`
          );
        } else if (form[key].startsWith("supplier-")) {
          filterInfo.push(
            `supplier_id:eq:${form[key].slice("supplier-".length)}`
          );
        } else {
          filterInfo.push(`source_id:eq:${form[key].slice("source-".length)}`);
        }
      } else if (key == "type") {
        filterInfo.push(`type:eq:${form[key]}`);
      } else if (key == "sub_source_id") {
        filterInfo.push(`sub_source_id:eq:${form[key]}`);
      }
    }
  });

  return Object.assign(
    { ...indexQuery },
    {
      filter: filterInfo.length === 0 ? indexQuery.filter : filterInfo.join()
    },
    { search: search },
    {
      otherParam: JSON.stringify({
        subCategoryIds: subCategoryIds
      })
    }
  );
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function onSearch() {
  loadData();
}

function initSearchData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  sourceList.value = basicData["costSource"];
}

function onReset() {
  indexQuery.search = "";
  Object.keys(form).forEach(key => {
    form[key] = null;
  });
  companyRegion.value = [];
  loadData();
}
function getSummaries(param, summaryData) {
  const { columns } = param;
  const sums = columns.map((column, index) => {
    if (index === 0) {
      return "Total Cost";
    }
    if (index === 1) {
      return `$${
        summaryData.totalCost.toLocaleString("en-US", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        }) || 0
      }`;
    }
    return ""; // For columns without specific handling
  });

  return sums;
}

function changeCost(row, changeDate = false) {
  const sendForm = {
    type: row.type,
    amount: row.amount,
    product_id: row.product_id,
    source_id: row.source_id == "Management" ? null : row.source_id,
    sub_source_id: row.sub_source_id,
    supplier_id: row.supplier_id,
    detail: row.detail,
    company_region: row.company_region,
    enquiry_date: changeDate
      ? dayjs(row.enquiry_date).format("YYYY-MM-DD")
      : row.enquiry_date,
    end_date:
      changeDate && row.end_date
        ? dayjs(row.end_date).format("YYYY-MM-DD")
        : row.end_date,
    other_source:
      row.source_id == "Management" ? "Management" : row.other_source
  };
  dialogLoading();
  costControllerUpdate(row.id, sendForm)
    .then(() => {
      dialogPanel.value.close();
      loadData();
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}
onMounted(() => {
  initSearchData();
});
</script>

<template>
  <el-card ref="mv" shadow="never">
    <div class="mt-2 px-2 pb-2 bg-bg_color">
      <div class="flex justify-between w-full h-[50px] list-form">
        <el-form ref="formRef" :inline="true" :model="form">
          <el-form-item
            v-if="userBaseInfo.isPlatformUser"
            class="company-region-select"
            :label="'Company:'"
            prop="companyRegion"
          >
            <el-select
              v-model="companyRegion"
              @change="loadData"
              multiple
              collapse-tags
              collapse-tags-tooltip
              class="w-[230px]"
              :placeholder="'Company'"
            >
              <el-option
                v-for="item in userBaseInfo.companyRegion"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="'Type:'" prop="type">
            <el-select
              v-model="form.type"
              placeholder="Select"
              clearable
              class="w-[120px]"
            >
              <el-option
                v-for="item in typeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            :label="'Product:'"
            prop="product"
            class="!w-[240px] mr-5"
          >
            <el-select
              v-model="form.productIds"
              placeholder="Select"
              filterable
              clearable
              multiple
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option-group
                v-for="group in productCategoryList"
                :key="group.name"
                :label="group.name"
              >
                <el-option
                  v-for="item in group.products"
                  :key="item.id"
                  :label="item.name"
                  :value="`${item.id}`"
                />
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item :label="'Source:'" prop="source_id">
            <el-select
              v-model="form.source_id"
              placeholder="Select"
              clearable
              class="w-[120px]"
            >
              <el-option
                v-for="item in sourceList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            :label="'Sub Source:'"
            prop="product"
            class="!w-[240px] mr-5"
          >
            <el-select
              v-model="form.sub_source_id"
              placeholder="Select"
              filterable
              clearable
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option-group
                v-for="group in sourceList"
                :key="group.name"
                :label="group.name"
              >
                <el-option
                  v-for="item in group.sub_source"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-option-group>
            </el-select>
          </el-form-item>
          <el-form-item :label="'Enquiry Date'" prop="data" class="!w-[320px]">
            <el-date-picker
              v-model="form.date"
              type="daterange"
              start-placeholder="Start date"
              end-placeholder="End date"
              format="DD/MM/YYYY"
              date-format="YYYY/MM/DD"
              class="mr-[10px]"
            />
          </el-form-item>
          <el-form-item :label="'End Date'" prop="data" class="!w-[320px]">
            <el-date-picker
              v-model="form.endDate"
              type="daterange"
              start-placeholder="Start date"
              end-placeholder="End date"
              format="DD/MM/YYYY"
              date-format="YYYY/MM/DD"
              class="mr-[10px]"
            />
          </el-form-item>
          <el-form-item class="action-box">
            <el-button type="primary" @click="onSearch()">
              {{ transformI18n("common.filter") }}
            </el-button>
            <el-button @click="onReset()" type="info">{{
              transformI18n("common.clear")
            }}</el-button>
          </el-form-item>
        </el-form>
        <div class="search-box">
          <el-input
            ref="inputRef"
            v-model="form.search"
            size="large"
            clearable
            :placeholder="transformI18n('leads.keywordPlaceHolder')"
            @input="loadData()"
            class="table-search-input"
          >
            <template #prefix>
              <el-tooltip
                class="item"
                effect="dark"
                content="You can search by Name,Contact People,Contact Phone or Note"
                placement="top"
              >
                <IconifyIconOffline
                  class="mr-[5px]"
                  width="16px"
                  height="16px"
                  color="#656F7D"
                  :icon="searchLine"
                />
              </el-tooltip>
            </template>
          </el-input>
        </div>
      </div>
      <data-table
        ref="dataTableRef"
        :columns="userBaseInfo.isPlatformUser ? platformColumns : columns"
        :source="costControllerIndex"
        :form="form"
        :slotNames="[
          'operation',
          'enquiry_date',
          'end_date',
          'type',
          'source',
          'amount',
          'product'
        ]"
        :query-params="tableQueryParams"
        :getSummariesCustom="getSummaries"
        :showSummary="true"
        :header-cell-style="{
          background: 'var(--el-table-row-hover-bg-color)',
          color: 'var(--el-text-color-primary)'
        }"
      >
        <template #enquiry_date="{ row }">
          <el-date-picker
            v-model="row.enquiry_date"
            type="date"
            @change="changeCost(row, true)"
            format="DD/MM/YYYY"
            date-format="YYYY/MM/DD"
            class="mr-[10px]"
          />
        </template>
        <template #end_date="{ row }">
          <el-date-picker
            v-model="row.end_date"
            type="date"
            @change="changeCost(row, true)"
            format="DD/MM/YYYY"
            date-format="YYYY/MM/DD"
            class="mr-[10px]"
          />
        </template>
        <template #type="{ row }">
          <!-- <span v-if="row.type == 1">Marketing</span>
          <span v-else-if="row.type == 2">Product</span> -->
          <el-select v-model="row.type" @change="changeCost(row)">
            <el-option
              v-for="item in typeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </template>
        <template #source="{ row }">
          <span v-if="row.source_id">
            {{ row.source.name }}
          </span>
          <span v-else-if="row.supplier_id">
            {{ row.supplier.name }}
          </span>
          <span v-else>
            {{ row.other_source }}
          </span>
        </template>
        <template #product="{ row }">
          <el-select
            v-model="row.product_id"
            placeholder="Select"
            filterable
            collapse-tags
            @change="changeCost(row)"
            collapse-tags-tooltip
          >
            <el-option-group
              v-for="group in productCategoryList"
              :key="group.name"
              :label="group.name"
            >
              <el-option
                v-for="item in group.products"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-option-group>
          </el-select>
        </template>
        <template #amount="{ row }">
          <el-input
            v-model="row.amount"
            @change="changeCost(row)"
            type="number"
            step="0.01"
            min="0.01"
            :placeholder="transformI18n('basicData.costAmount')"
            autocomplete="off"
          />
        </template>
        <template #operation="{ row }">
          <div>
            <el-dropdown trigger="click">
              <span class="iconfont just-icon-gengduo" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    class="text-[#2A2E34] text-base"
                    @click.stop="onEdit(row)"
                  >
                    <IconifyIconOffline
                      class="mr-[5px]"
                      width="20px"
                      height="20px"
                      color="#9B3CE5"
                      :icon="RiEdit2Line"
                    />
                    {{ transformI18n("buttons.hseditor") }}
                  </el-dropdown-item>

                  <el-dropdown-item
                    class="text-[#2A2E34] text-base"
                    @click.stop="onDelete(row)"
                  >
                    <i
                      class="!text-xl text-[#9B3CE5] iconfont just-icon-cancelled"
                    />
                    {{ transformI18n("buttons.hsdelete") }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </data-table>
      <cost-form ref="dataFormRef" />
    </div>
  </el-card>
</template>
<style lang="scss" scoped>
:deep(.company-region-select) {
  width: 280px !important;
}
</style>
