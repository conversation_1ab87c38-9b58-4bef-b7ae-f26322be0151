import { requiredField } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
export const indexDefaultQuery = {
  search: "",
  sort: ["-id"],
  filter: [],
  _with: [],
  withCount: []
};

export const columns: TableColumnList = [
  {
    label: "ID",
    width: "100",
    prop: "id",
    fixed: true
  },
  {
    label: transformI18n("basicData.costType"),
    prop: "type",
    slot: "type"
  },
  {
    label: transformI18n("basicData.costQueryDate"),
    prop: "enquiry_date",
    slot: "enquiry_date"
  },
  {
    label: transformI18n("basicData.costEndDate"),
    prop: "end_date",
    slot: "end_date"
  },
  {
    label: transformI18n("basicData.costSource"),
    prop: "source.name",
    slot: "source"
  },
  {
    label: transformI18n("basicData.costSubSource"),
    prop: "subsource.name"
  },
  {
    label: transformI18n("basicData.costSubProduct"),
    prop: "products.name",
    slot: "product"
  },
  {
    label: transformI18n("basicData.costAmount"),
    prop: "amount",
    slot: "amount"
  },
  {
    label: "Detail",
    prop: "detail"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];

export const platformColumns: TableColumnList = [
  {
    label: "ID",
    width: "100",
    prop: "id",
    fixed: true
  },
  {
    label: transformI18n("basicData.costType"),
    prop: "type",
    slot: "type"
  },
  {
    label: transformI18n("basicData.costQueryDate"),
    prop: "enquiry_date",
    slot: "enquiry_date"
  },
  {
    label: transformI18n("basicData.costEndDate"),
    prop: "end_date",
    slot: "end_date"
  },
  {
    label: transformI18n("basicData.costSource"),
    prop: "source.name",
    slot: "source"
  },
  {
    label: transformI18n("basicData.costSubSource"),
    prop: "subsource.name"
  },
  {
    label: transformI18n("basicData.costSubProduct"),
    prop: "products.name",
    slot: "product"
  },
  {
    label: transformI18n("basicData.costAmount"),
    prop: "amount",
    slot: "amount"
  },
  {
    label: "Detail",
    prop: "detail"
  },
  {
    label: "Company",
    prop: "company_region"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];

export const rules = {
  type: [requiredField(transformI18n("basicData.costType"))],
  enquiry_date: [requiredField(transformI18n("basicData.costQueryDate"))],
  // product_id: [requiredField(transformI18n("basicData.costSubProduct"))],
  amount: [requiredField(transformI18n("basicData.costAmount"))],
  source_id: [requiredField(transformI18n("basicData.costSubProduct"))],
  company_region: [requiredField("company")]
};
