<script setup lang="ts">
import { reactive, ref } from "vue";
import { rules } from "./cost_data";
import {
  costControllerUpdate,
  costControllerStore
} from "@/api/admin/basic-data";
import { sendFormFilter } from "@/utils/form";

import { transformI18n } from "@/plugins/i18n";
import { useCommonStoreHook } from "@/store/modules/common";
import { useNav } from "@/layout/hooks/useNav";
import dayjs from "dayjs";

const { userBaseInfo } = useNav();
const productIds = ref([]);
const ratioList = ref([]);
const errorFlag = ref(false);
const form = reactive({
  id: null,
  type: 1,
  source_id: null,
  product_id: null,
  amount: 0,
  enquiry_date: null,
  supplier_id: null,
  detail: null,
  sub_source_id: null,
  other_source: null,
  company_region: null,
  ratio_list: null
});
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const isEdit = ref(false);
const productCategoryList = ref([]);
const productNames = ref([]);
const sourceList = ref([]);
const subSource = ref([]);
const typeList = ref([
  {
    id: 1,
    name: "Marketing"
  },
  {
    id: 2,
    name: "Product"
  }
]);
function show(data = null) {
  isEdit.value = !!data;
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
  sourceList.value = basicData["costSource"];
  productCategoryList.value.forEach(c => {
    if (c.products.length) {
      c.products.forEach(p => {
        productNames.value.push({
          id: p.id,
          name: p.name
        });
      });
    }
  });
  if (isEdit.value) {
    Object.assign(form, data);
    if (form.source_id) {
      form.source_id = "source-" + form.source_id;
    } else if (form.other_source) {
      form.source_id = "other-" + form.other_source;
    } else if (form.supplier_id) {
      form.source_id = "supplier-" + form.supplier_id;
    }
    sourceList.value.forEach(item => {
      if (item.id == form.source_id) {
        subSource.value = item.sub_source;
      }
    });
  } else {
    Object.keys(form).forEach(key => {
      form[key] = null;
      if (key == "company_region") {
        form[key] = userBaseInfo.value.isPlatformUser
          ? null
          : userBaseInfo.value.accountCompanyRegion;
      }
    });
    subSource.value = [];
    productIds.value = [];
    ratioList.value = [];
  }
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}
function initSubSource() {
  sourceList.value.forEach(item => {
    if (item.id == form.source_id) {
      subSource.value = item.sub_source;
    }
  });
  if (!form.source_id.startsWith("source-")) {
    form.sub_source_id = null;
  }
}
function hide() {
  dialogVisible.value = false;
}

function ratioChange() {
  ratioList.value = [];
  errorFlag.value = false;
  console.log(productIds.value.length);
  if (productIds.value.length > 1) {
    let productName = "";
    productIds.value.forEach((item, index) => {
      productNames.value.forEach(p => {
        if (p.id == item) {
          productName = p.name;
        }
      });
      if (index + 1 < productIds.value.length) {
        ratioList.value.push({
          id: item,
          value: Math.round(100 / productIds.value.length),
          name: productName
        });
      }
    });
    productNames.value.forEach(p => {
      if (p.id == productIds.value[productIds.value.length - 1]) {
        productName = p.name;
      }
    });
    ratioList.value.push({
      id: productIds.value[productIds.value.length - 1],
      value:
        100 -
        Math.round(100 / productIds.value.length) *
          (productIds.value.length - 1),
      name: productName
    });
    console.log(ratioList.value);
    console.log("-pppppp");
  }
}
async function onSave() {
  await formRef.value.validate(valid => {
    let total = 0;
    ratioList.value.forEach(item => {
      total += item.value;
    });
    errorFlag.value = false;
    if (productIds.value.length > 1 && total != 100) {
      errorFlag.value = true;
      return;
    }
    if (!productIds.value.length && !form.id) {
      errorFlag.value = true;
      return;
    }
    if (valid) {
      loading.value = true;
      if (form.source_id.startsWith("source-")) {
        form.other_source = "";
        form.supplier_id = null;
        form.source_id = form.source_id.slice("source-".length);
      } else if (form.source_id.startsWith("other-")) {
        form.other_source = form.source_id.slice("other-".length);
        form.source_id = null;
        form.supplier_id = null;
      } else if (form.source_id.startsWith("supplier-")) {
        form.supplier_id = form.source_id.slice("supplier-".length);
        form.source_id = null;
        form.other_source = "";
      }
      if (productIds.value.length) {
        form.product_id = productIds.value.join(",");
        form.ratio_list = [];
        ratioList.value.forEach(item => {
          form.ratio_list[item.id] = item.value;
        });
      }
      form.enquiry_date = dayjs(form.enquiry_date).format("YYYY-MM-DD");
      console.log(form);
      const sendForm = sendFormFilter(form);
      if (isEdit.value) {
        costControllerUpdate(form.id, sendForm)
          .then(() => {
            loading.value = false;
            hide();
            promise.resolve();
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        costControllerStore(sendForm)
          .then(() => {
            loading.value = false;
            hide();
            promise.resolve();
          })
          .catch(() => {
            loading.value = false;
          });
      }
    }
  });
}

defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="
      isEdit
        ? transformI18n('basicData.editCost')
        : transformI18n('basicData.newCost')
    "
    width="38.75rem"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      class="w-[99/100] ml-7 mr-7"
    >
      <el-col
        v-if="userBaseInfo.isPlatformUser"
        class="!mb-0 mt-5 text-sm text-[#2A2E34]"
        :span="24"
      >
        Company
      </el-col>
      <el-col
        class="text-sm text-[#2A2E34]"
        v-if="userBaseInfo.isPlatformUser"
        :span="24"
      >
        <el-form-item prop="company_region">
          <el-select
            v-model="form.company_region"
            clearable
            :placeholder="'Company'"
          >
            <el-option
              v-for="item in userBaseInfo.companyRegion"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-row>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("basicData.costType")
        }}</el-col>
        <el-col :span="24">
          <el-form-item
            :label="transformI18n('basicData.costType') + ':'"
            prop="type"
            class="!mr-2"
          >
            <el-select
              v-model="form.type"
              placeholder="Select"
              filterable
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option
                v-for="item in typeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 mt-5 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("basicData.costQueryDate")
        }}</el-col>
        <el-col :span="24">
          <el-form-item :label="'Enquiry Date'" prop="enquiry_date">
            <el-date-picker
              v-model="form.enquiry_date"
              type="date"
              format="DD/MM/YYYY"
              date-format="YYYY/MM/DD"
              class="mr-[10px]"
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 mt-5 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("basicData.costSubProduct")
        }}</el-col>
        <el-col :span="24">
          <el-form-item :label="'Sub Product'" prop="product_id">
            <el-select
              v-if="form.id"
              v-model="form.product_id"
              placeholder="Select"
              filterable
              collapse-tags
              collapse-tags-tooltip
            >
              <el-option-group
                v-for="group in productCategoryList"
                :key="group.name"
                :label="group.name"
              >
                <el-option
                  v-for="item in group.products"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-option-group>
            </el-select>
            <el-select
              v-else
              v-model="productIds"
              placeholder="Select"
              filterable
              collapse-tags
              collapse-tags-tooltip
              @change="ratioChange"
              multiple
            >
              <el-option-group
                v-for="group in productCategoryList"
                :key="group.name"
                :label="group.name"
              >
                <el-option
                  v-for="item in group.products"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-option-group>
            </el-select>
          </el-form-item>
          <p v-if="errorFlag" class="el-form-item__error error-message">
            <span v-if="ratioList.length"
              >Please ensure that the total proportion of the products is
              100%</span
            >
            <span v-else>Please select the products</span>
          </p>
        </el-col>

        <el-row v-if="!form.id">
          <el-col :span="8" v-for="item in ratioList" :key="'ratio' + item.id">
            <div class="flex justify-between items-center pr-2 ratio-item">
              <span>{{ item.name }}</span>
              <el-input-number v-model="item.value" controls-position="right">
                <template #suffix>
                  <span>%</span>
                </template>
              </el-input-number>
            </div>
          </el-col>
        </el-row>
        <el-col class="!mb-0 mt-5 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("basicData.costSource")
        }}</el-col>
        <el-col :span="24">
          <el-form-item :label="'Source'" prop="source_id">
            <el-select
              v-model="form.source_id"
              placeholder="Select"
              filterable
              collapse-tags
              collapse-tags-tooltip
              @change="initSubSource()"
            >
              <el-option
                v-for="item in sourceList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 mt-5 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("basicData.costSubSource")
        }}</el-col>
        <el-col :span="24">
          <el-form-item
            :label="transformI18n('basicData.costSubSource') + ':'"
            prop="sub_source_id"
            class="!mr-2"
          >
            <el-select
              v-model="form.sub_source_id"
              placeholder="Select"
              filterable
              collapse-tags
              clearable
              collapse-tags-tooltip
            >
              <el-option
                v-for="item in subSource"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 mt-5 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("basicData.costAmount")
        }}</el-col>
        <el-col :span="24">
          <el-form-item
            :label="transformI18n('basicData.costAmount') + ':'"
            prop="amount"
            class="!mr-2"
          >
            <el-input
              v-model="form.amount"
              type="number"
              min="0.01"
              :placeholder="transformI18n('basicData.costAmount')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 mt-5 text-sm text-[#2A2E34]" :span="24"
          >Detail</el-col
        >
        <el-col :span="24">
          <el-form-item :label="'Detail:'" prop="detail" class="!mr-2">
            <el-input
              v-model="form.detail"
              type="textarea"
              :rows="3"
              :placeholder="transformI18n('common.input')"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide">{{
          transformI18n("buttons.hsCancel")
        }}</el-button>
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :disabled="loading"
          :loading="loading"
          @click="onSave"
          >{{ transformI18n("buttons.hssave") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
<style lang="scss">
.ratio-item {
  padding: 0 5px;
  margin-right: 5px;
  border: 1px solid #e9ebf0;
  border-radius: 6px;
}

.error-message {
  position: initial;
}
</style>
