<script setup lang="ts">
import { promotionControllerIndex } from "@/api/admin/basic-data";
import { columns } from "./promotion_data";
import { reactive, ref, onMounted } from "vue";
import DataTable from "@/components/DataTable";

import { transformI18n } from "@/plugins/i18n";
import searchLine from "@iconify-icons/ri/search-line";

defineOptions({
  name: "promotion"
});

const form = reactive({});
const dataTableRef = ref(null);
const indexQuery = reactive({
  search: "",
  sort: "id",
  filter: "",
  _with: "",
  withCount: ""
});

function loadData() {
  dataTableRef.value.loadData();
}

function tableQueryParams(form) {
  const filterInfo = [];
  let search = "";
  if (form.search) {
    search = `%${form.search}%`;
  }

  return Object.assign(
    { ...indexQuery },
    { filter: filterInfo.length === 0 ? indexQuery.filter : filterInfo.join() },
    { search: search }
  );
}

onMounted(() => {});
</script>

<template>
  <el-card shadow="never">
    <div class="mt-2 px-2 pb-2 bg-bg_color">
      <div class="flex justify-end w-full h-[50px] list-form">
        <div class="search-box">
          <el-input
            ref="inputRef"
            v-model="form.search"
            size="large"
            clearable
            :placeholder="transformI18n('leads.keywordPlaceHolder')"
            @input="loadData()"
            class="table-search-input"
          >
            <template #prefix>
              <el-tooltip
                class="item"
                effect="dark"
                content="You can search by Name"
                placement="top"
              >
                <IconifyIconOffline
                  class="mr-[5px]"
                  width="16px"
                  height="16px"
                  color="#656F7D"
                  :icon="searchLine"
                />
              </el-tooltip>
            </template>
          </el-input>
        </div>
      </div>
      <data-table
        ref="dataTableRef"
        :columns="columns"
        :source="promotionControllerIndex"
        :form="form"
        :query-params="tableQueryParams"
        :header-cell-style="{
          background: 'var(--el-table-row-hover-bg-color)',
          color: 'var(--el-text-color-primary)'
        }"
      />
    </div>
  </el-card>
</template>
./clients_data ./promotion_data
