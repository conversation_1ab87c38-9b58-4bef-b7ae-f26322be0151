<script setup lang="ts">
import {
  targetControllerIndex,
  targetControllerDestroy,
  targetControllerUpdate,
  targetControllerStore,
  targetControllerCopyTarget,
  targetControllerUpdateDate
} from "@/api/admin/basic-data";
import { reactive, ref, onMounted, watch } from "vue";
import { transformI18n } from "@/plugins/i18n";
import { ElMessageBox, ElLoading } from "element-plus";
import { useCommonStoreHook } from "@/store/modules/common";
import dayjs from "dayjs";
import { useNav } from "@/layout/hooks/useNav";
const saleValues = ref([]);
const dateList = ref(null);
const monthStr = ref(null);
const installValues = ref([]);
const { userBaseInfo } = useNav();
const targetList = ref([]);
const month = ref(null);
const productCategoryList = ref([]);
const monthList = ref([
  {
    name: "Jan",
    value: "01"
  },
  {
    name: "Feb",
    value: "02"
  },
  {
    name: "Mar",
    value: "03"
  },
  {
    name: "Apr",
    value: "04"
  },
  {
    name: "May",
    value: "05"
  },
  {
    name: "Jun",
    value: "06"
  },
  {
    name: "Jul",
    value: "07"
  },
  {
    name: "Aug",
    value: "08"
  },
  {
    name: "Sep",
    value: "09"
  },
  {
    name: "Oct",
    value: "10"
  },
  {
    name: "Nov",
    value: "11"
  },
  {
    name: "Dec",
    value: "12"
  }
]);
const mv = ref();
const dialogPanel = ref();
const indexQuery = reactive({
  search: "",
  sort: "-id",
  filter: "",
  _with: "",
  withCount: "",
  size: 999,
  page: 1
});

const props = defineProps({
  loadDataFlag: {
    type: String,
    default: null
  }
});

// Listen for changes in props properties
watch(
  () => props.loadDataFlag,
  (_newValue, _oldValue) => {
    loadData();
  }
);

watch(
  () => targetList.value,
  list => {
    saleValues.value = list.map(item => formatNumber(item.sale_target));
    installValues.value = list.map(item => formatNumber(item.install_target));
  },
  { immediate: true }
);

function formatNumber(value) {
  if (value === null || value === undefined) return "";
  const num = parseFloat(value);
  if (isNaN(num)) return "";
  return num.toLocaleString("en-US", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

function handleInput(event, index, key) {
  console.log(event);
  const rawValue = event.replace(/,/g, "");
  const num = parseFloat(rawValue);
  if (!isNaN(num)) {
    key == "sale"
      ? (targetList.value[index].sale_target = num)
      : (targetList.value[index].install_target = num);
  }
  console.log(targetList.value);
}

function handleBlur(index, key) {
  saleValues.value[index] = formatNumber(
    key == "sale"
      ? targetList.value[index].sale_target
      : targetList.value[index].install_target
  );
}

function onDelete(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>delete</strong> the <strong style='color:var(--el-color-primary)'>target</strong>?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    targetControllerDestroy(row.id)
      .then(() => {
        dialogPanel.value.close();
        loadData();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

function changeMonth() {
  const year = dayjs(new Date()).format("YYYY");
  month.value = dayjs(year + "-" + monthStr.value).format("YYYY-MM");
  dateList.value = [
    dayjs(month.value).startOf("month").format("YYYY-MM-DD"),
    dayjs(month.value).endOf("month").format("YYYY-MM-DD")
  ];
  loadData();
}

function changeDate() {
  const startDate = dayjs(dateList.value[0]).format("YYYY-MM-DD 00:00:00");
  const endDate = dayjs(dateList.value[1]).format("YYYY-MM-DD 23:59:59");
  const year = dayjs(new Date()).format("YYYY");

  dialogLoading();
  targetControllerUpdateDate({
    month: year + "-" + monthStr.value,
    start_date: startDate,
    end_date: endDate
  })
    .then(() => {
      dialogPanel.value.close();
      loadData();
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}

function copyTarget() {
  let monthLabel = "";
  monthList.value.forEach(item => {
    if (item.value == monthStr.value) {
      monthLabel = item.name;
    }
  });
  const year = dayjs(new Date()).format("YYYY");
  ElMessageBox.confirm(
    `Are you sure you want to <strong>copy</strong> the <strong style='color:var(--el-color-primary)'>${monthLabel}</strong> month target to next month, this will cover the existing data?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    const nextMonth = dayjs(year + "-" + monthStr.value)
      .add(1, "month")
      .format("YYYY-MM");
    console.log(nextMonth);
    targetControllerCopyTarget({
      month: year + "-" + monthStr.value,
      next_month: nextMonth
    })
      .then(() => {
        dialogPanel.value.close();
        loadData();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

function loadData() {
  dialogLoading();
  if (!month.value) {
    month.value = dayjs(new Date()).format("YYYY-MM");
  }

  const filter = "month:eq:" + month.value;
  const { search, sort, _with, withCount, page, size } = indexQuery;
  targetControllerIndex(
    search,
    filter,
    sort,
    _with,
    withCount,
    page,
    size
  ).then(res => {
    targetList.value = [];
    if (!res.meta.total) {
      addNewTarget();
    } else {
      res.data.forEach(item => {
        targetList.value.push({
          id: item.id,
          name: item.name,
          enquiry: item.enquiry,
          month: item.month,
          company_region: item.company_region,
          sub_products: item.sub_products ? item.sub_products.split(",") : [],
          start_date: dayjs(item.start_date).format("YYYY-MM-DD"),
          end_date: dayjs(item.end_date).format("YYYY-MM-DD"),
          sale_target: item.sale_target,
          install_target: item.install_target,
          sort: item.sort
        });
      });
      dateList.value = [
        targetList.value[0].start_date,
        targetList.value[0].end_date
      ];
    }
    dialogPanel.value.close();
  });
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

function initSearchData() {
  const basicData = useCommonStoreHook().getAllHandleData;
  productCategoryList.value = basicData["productCategoryList"];
}
function addNewTarget() {
  const row = {
    name: "Target",
    month: month.value,
    company_region: null,
    sub_products: [],
    enquiry: 0,
    start_date: dayjs(month.value)
      .startOf("month")
      .format("YYYY-MM-DD 00:00:00"),
    end_date: dayjs(month.value).endOf("month").format("YYYY-MM-DD 23:59:59"),
    sale_target: 0,
    install_target: 0,
    sort: 0
  };
  changeTarget(row);
}

function changeTarget(row) {
  console.log(targetList.value);
  const sendForm = {
    name: row.name,
    month: row.month,
    enquiry: row.enquiry,
    company_region: row.company_region,
    sub_products: row.sub_products.length ? row.sub_products.join(",") : "",
    start_date: row.start_date,
    end_date: row.end_date,
    sale_target: row.sale_target,
    install_target: row.install_target,
    sort: row.sort
  };
  dialogLoading();
  (row.id
    ? targetControllerUpdate(row.id, sendForm)
    : targetControllerStore(sendForm)
  )
    .then(() => {
      dialogPanel.value.close();
      loadData();
    })
    .catch(() => {
      dialogPanel.value.close();
    });
}

onMounted(() => {
  console.log("*********");
  initSearchData();
  month.value = dayjs(new Date()).format("YYYY-MM");
  monthStr.value = dayjs(new Date()).format("MM");
  dateList.value = [
    dayjs(new Date()).startOf("month").format("YYYY-MM-DD"),
    dayjs(new Date()).endOf("month").format("YYYY-MM-DD")
  ];
  console.log(monthStr);
});
</script>

<template>
  <el-card ref="mv" shadow="never">
    <div class="p-5 bg-bg_color">
      <el-row class="mb-5 header-item">
        <div class="date-item">
          Month
          <el-select
            class="border border-[#E9EBF0] rounded-[6px] pl-2 ml-2 w-[80px]"
            v-model="monthStr"
            @change="changeMonth()"
            collapse-tags
            collapse-tags-tooltip
            :placeholder="'Month'"
          >
            <el-option
              v-for="item in monthList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
          <el-date-picker
            v-model="dateList"
            type="daterange"
            start-placeholder="Start"
            end-placeholder="End"
            format="DD/MM/YYYY"
            date-format="YYYY/MM/DD"
            @change="changeDate()"
            class="border-0 ml-2"
          />
        </div>
        <div>
          <el-button :icon="Plus" type="primary" @click="copyTarget"
            >Copy To Next Month</el-button
          >
          <el-button :icon="Plus" type="primary" @click="addNewTarget"
            >Add New Traget</el-button
          >
        </div>
      </el-row>
      <el-row class="table-header" :gutter="10">
        <el-col :span="3" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">Target</div>
        </el-col>
        <el-col :span="4" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">SubProduct</div>
        </el-col>
        <el-col :span="3" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">Company</div>
        </el-col>
        <el-col :span="3" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">Enquiry</div>
        </el-col>
        <el-col :span="4" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">Gross Sales</div>
        </el-col>
        <el-col :span="4" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">Turnover</div>
        </el-col>
        <el-col :span="2" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">Sort</div>
        </el-col>
        <el-col :span="1" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">Action</div>
        </el-col>
      </el-row>
      <el-row
        class="pt-2 pb-2 border-b border-l border-r border-[#e9ebf0] target-item"
        :gutter="10"
        v-for="(item, index) in targetList"
        :key="index"
      >
        <el-col :span="3" class="item-box">
          <el-input
            v-model="item.name"
            :placeholder="'Target'"
            autocomplete="off"
            @change="val => changeTarget(item)"
          />
        </el-col>
        <el-col :span="4" class="item-box">
          <el-select
            v-model="item.sub_products"
            collapse-tags
            collapse-tags-tooltip
            @change="changeTarget(item)"
            multiple
            placeholder="Select"
          >
            <el-option-group
              v-for="group in productCategoryList"
              :key="group.name"
              :label="group.name"
            >
              <el-option
                v-for="item in group.products"
                :key="item.id"
                :label="item.name"
                :value="`${item.id}`"
              />
            </el-option-group>
          </el-select>
        </el-col>
        <el-col :span="3">
          <el-select
            v-model="item.company_region"
            @change="changeTarget(item)"
            collapse-tags
            collapse-tags-tooltip
            :placeholder="'Company'"
          >
            <el-option
              v-for="item in userBaseInfo.companyRegion"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-col>
        <el-col :span="3" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">
            <el-input-number
              v-model="item.enquiry"
              :min="0"
              :controls="false"
              :placeholder="transformI18n('common.input')"
              class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
              @change="val => changeTarget(item)"
            />
          </div>
        </el-col>
        <el-col :span="4" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">
            <el-input
              v-model="saleValues[index]"
              :min="0"
              :controls="false"
              :placeholder="transformI18n('common.input')"
              class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
              @change="val => changeTarget(item)"
              @input="handleInput($event, index, 'sale')"
              @blur="handleBlur(index, 'sale')"
            />
          </div>
        </el-col>
        <el-col :span="4" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">
            <el-input
              v-model="installValues[index]"
              :min="0"
              :controls="false"
              :placeholder="transformI18n('common.input')"
              class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
              @change="val => changeTarget(item)"
              @input="handleInput($event, index, 'install')"
              @blur="handleBlur(index, 'install')"
            />
          </div>
        </el-col>
        <el-col :span="2" class="item-box">
          <div class="text-[#656F7D] text-xs font-normal">
            <el-input-number
              v-model="item.sort"
              :min="0"
              :controls="false"
              :placeholder="transformI18n('common.input')"
              class="border border-[#E9EBF0] rounded-[6px] p-x[5px] m-x[5px] max-w-[100%]"
              @change="val => changeTarget(item)"
            />
          </div>
        </el-col>
        <el-col :span="1" class="item-box">
          <i
            @click="onDelete(item)"
            class="cursor-pointer text-[#9B3CE5] iconfont just-icon-cancelled mr-[5px] ml-[10px]"
          />
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>
<style lang="scss" scoped>
:deep(.company-region-select) {
  width: 280px !important;
}

.header-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 20px;
  font-weight: bold;
  color: #000;

  :deep(.el-date-editor) {
    padding: 2px;
    margin-left: 10px;
    font-size: 20px;
    color: #000;
    border: 1px solid #e9ebf0;
    border-radius: 5px;
  }

  .date-item {
    display: flex;
    align-items: center;
  }
}

.table-header {
  padding: 5px;
  margin: 5px;
  margin-bottom: 0;
  color: #909399;
  background: #ebeef5;
  border-radius: 5px 5px 0 0;
}

.target-item {
  :deep(.el-input__inner) {
    text-align: center;
  }
}
</style>
