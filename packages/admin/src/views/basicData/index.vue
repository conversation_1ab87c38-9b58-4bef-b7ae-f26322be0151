<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { transformI18n } from "@/plugins/i18n";
import Source from "./source/index.vue";
import Promotion from "./promotion/index.vue";
import Supplier from "./supplier/index.vue";
import Cost from "./cost/index.vue";
import Target from "./target/index.vue";
import { Plus } from "@element-plus/icons-vue";
import NewSupplierForm from "./supplier/form.vue";
import NewCostForm from "./cost/form.vue";
import { hasAuth } from "@/router/utils";
import { costControllerSyncReport } from "@/api/admin/basic-data";
defineOptions({
  name: "Basic Data Setting"
});
const loading = ref(false);
const activeName = ref("tab_promotion");
const loadDataFlag = ref("");
const defaultTabsList = [
  {
    label: transformI18n("basicData.promotion"),
    name: "promotion",
    component: Promotion,
    permissionCode: "viewAnyPromotion",
    loadDataFlag: ""
  },
  {
    label: transformI18n("basicData.source"),
    name: "source",
    component: Source,
    permissionCode: "viewAnySource",
    loadDataFlag: ""
  },
  {
    label: transformI18n("basicData.supplier"),
    name: "supplier",
    component: Supplier,
    permissionCode: "viewAnySupplier",
    loadDataFlag: ""
  },
  {
    label: transformI18n("basicData.cost"),
    name: "cost",
    component: Cost,
    permissionCode: "viewAnyCost",
    loadDataFlag: ""
  },
  {
    label: transformI18n("basicData.target"),
    name: "target",
    component: Target,
    permissionCode: "viewAnyTarget",
    loadDataFlag: ""
  }
];

const NewSupplierFormRef = ref(null);
const NewCostFormRef = ref(null);
const tabsList = ref([]);

// to Create new supplier
function addNewSupplier() {
  NewSupplierFormRef.value.show().then(() => {
    tabsList.value.forEach(item => {
      if ("tab_" + item.name == "tab_supplier") {
        item.loadDataFlag =
          "" + (Math.floor(Math.random() * (1000 - 10 + 1)) + 10);
      }
    });
  });
}

// to Create new supplier
function addNewCost() {
  NewCostFormRef.value.show().then(() => {
    tabsList.value.forEach(item => {
      if ("tab_" + item.name == "tab_cost") {
        item.loadDataFlag =
          "" + (Math.floor(Math.random() * (1000 - 10 + 1)) + 10);
      }
    });
  });
}

function changeTab(tab, event) {
  nextTick(() => {
    tabsList.value.forEach(item => {
      if ("tab_" + item.name == activeName.value) {
        item.loadDataFlag = event._vts + "";
      }
    });
  });
}

function syncCostReport() {
  loading.value = true;
  costControllerSyncReport()
    .then(() => {
      loading.value = false;
      loadDataFlag.value =
        "" + (Math.floor(Math.random() * (1000 - 10 + 1)) + 10);
    })
    .catch(() => {
      loading.value = false;
      loadDataFlag.value =
        "" + (Math.floor(Math.random() * (1000 - 10 + 1)) + 10);
    });
}
onMounted(() => {
  defaultTabsList.forEach(item => {
    if (hasAuth(item.permissionCode)) {
      tabsList.value.push(item);
    }
  });
  activeName.value = `tab_${tabsList.value[0].name}`;
});
</script>

<template>
  <el-card shadow="never">
    <div class="font-medium page-header">
      {{ transformI18n("menus.hsbasic") }}
    </div>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="changeTab">
      <el-tab-pane
        :label="tabItem.label"
        :name="'tab_' + tabItem.name"
        v-for="(tabItem, index) in tabsList"
        :key="index"
      >
        <component
          :is="tabItem.component"
          :ref="`${tabItem.name}Ref`"
          :loadDataFlag="tabItem.loadDataFlag"
        />
      </el-tab-pane>
    </el-tabs>
    <div
      v-auth="'addSupplier'"
      v-if="activeName === 'tab_supplier'"
      class="page-action-box"
    >
      <el-button :icon="Plus" type="primary" @click="addNewSupplier">{{
        transformI18n("basicData.newSupplier")
      }}</el-button>
    </div>
    <div
      v-auth="'viewAnyCost'"
      v-if="activeName === 'tab_cost'"
      class="page-action-box"
    >
      <el-button :loading="loading" type="primary" @click="syncCostReport"
        >Sync Cost Report</el-button
      >
      <el-button :icon="Plus" type="primary" @click="addNewCost">{{
        transformI18n("basicData.newCost")
      }}</el-button>
    </div>
    <new-supplier-form ref="NewSupplierFormRef" />
    <new-cost-form ref="NewCostFormRef" />
  </el-card>
</template>
<style lang="scss" scoped>
.page-header {
  position: absolute;
  top: 10px;
  left: 15px;
  width: 182px;
  height: 27px;
  padding: 0;
  font-size: 18px;
  border-right: 1px solid #e9ebf0;
}

:deep(.el-tabs) {
  --el-tabs-header-height: 50px;
}

.demo-tabs {
  :deep(.el-tabs__header) {
    padding: 0 220px;
    margin: 0;
    border-bottom: 1px solid #e9ebf0;

    .el-tabs__nav-wrap::after {
      background-color: transparent;
    }

    .el-tabs__item {
      font-weight: normal;
    }

    .el-tabs__item.is-active {
      font-weight: 500;
      color: #000;
    }

    .el-tag {
      width: max-content;
      min-width: 15px;
      height: 15px;
      padding: 1px;
      margin-left: 5px;
      font-size: 11px;
      font-weight: 500;
      color: #656f7d;
      background-color: #f0f4f7;
      border: 0;
    }
  }

  .custom-tabs-label {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
