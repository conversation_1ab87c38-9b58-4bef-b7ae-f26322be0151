<script setup lang="ts">
import { reactive, ref } from "vue";
import { rules } from "./supplier_data";
import {
  supplierControllerUpdate,
  supplierControllerStore
} from "@/api/admin/basic-data";
import { sendFormFilter } from "@/utils/form";

import { transformI18n } from "@/plugins/i18n";
import { useNav } from "@/layout/hooks/useNav";

const { userBaseInfo } = useNav();

const form = reactive({
  id: null,
  name: null,
  contact_people: null,
  contact_phone: null,
  company_region: null
});
const formRef = ref(null);
const dialogVisible = ref(false);
const promise: any = {};
const loading = ref(false);
const isEdit = ref(false);

function show(data = null) {
  isEdit.value = !!data;
  if (isEdit.value) {
    Object.assign(form, data);
  } else {
    Object.keys(form).forEach(key => {
      form[key] = null;
      if (key == "company_region") {
        form[key] = userBaseInfo.value.isPlatformUser
          ? null
          : userBaseInfo.value.accountCompanyRegion;
      }
    });
  }
  dialogVisible.value = true;
  return new Promise((resolve, reject) => {
    promise.resolve = resolve;
    promise.reject = reject;
  });
}

function hide() {
  dialogVisible.value = false;
}

async function onSave() {
  await formRef.value.validate(valid => {
    if (valid) {
      loading.value = true;
      const sendForm = sendFormFilter(form);
      if (isEdit.value) {
        supplierControllerUpdate(form.id, sendForm)
          .then(() => {
            loading.value = false;
            hide();
            promise.resolve();
          })
          .catch(() => {
            loading.value = false;
          });
      } else {
        supplierControllerStore(sendForm)
          .then(() => {
            loading.value = false;
            hide();
            promise.resolve();
          })
          .catch(() => {
            loading.value = false;
          });
      }
    }
  });
}

defineExpose({ show });
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="
      isEdit
        ? transformI18n('basicData.editSupplier')
        : transformI18n('basicData.newSupplier')
    "
    width="38.75rem"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      class="w-[99/100] ml-7 mr-7"
    >
      <el-row>
        <el-col class="!mb-0 mt-5 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("basicData.name")
        }}</el-col>
        <el-col :span="24">
          <el-form-item
            :label="transformI18n('basicData.name') + ':'"
            prop="name"
            class="!mr-2"
          >
            <el-input
              v-model="form.name"
              :placeholder="transformI18n('basicData.name')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("basicData.supplierContact")
        }}</el-col>
        <el-col :span="12">
          <el-form-item
            :label="transformI18n('basicData.supplierContactPeople') + ':'"
            prop="contact_people"
            class="!mr-2"
          >
            <el-input
              v-model="form.contact_people"
              :placeholder="transformI18n('basicData.supplierContactPeople')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="transformI18n('basicData.supplierContactPhone') + ':'"
            prop="contact_phone"
            class="!mr-2"
          >
            <el-input
              v-model="form.contact_phone"
              :placeholder="transformI18n('basicData.supplierContactPhone')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>

        <el-col
          v-if="userBaseInfo.isPlatformUser"
          class="!mb-0 text-sm text-[#2A2E34]"
          :span="24"
        >
          Company
        </el-col>
        <el-col
          class="text-sm text-[#2A2E34]"
          v-if="userBaseInfo.isPlatformUser"
          :span="24"
        >
          <el-form-item prop="company_region">
            <el-select
              v-model="form.company_region"
              clearable
              :placeholder="'Company'"
            >
              <el-option
                v-for="item in userBaseInfo.companyRegion"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col class="!mb-0 text-sm text-[#2A2E34]" :span="24">{{
          transformI18n("basicData.supplierNote")
        }}</el-col>
        <el-col :span="24">
          <el-form-item prop="note" class="!mr-2">
            <el-input
              v-model="form.note"
              type="textarea"
              :rows="3"
              :placeholder="transformI18n('basicData.supplierNote')"
              autocomplete="off"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button class="w-52 !text-sm h-10 !bg-[#E9EBF0]" @click="hide">{{
          transformI18n("buttons.hsCancel")
        }}</el-button>
        <el-button
          class="w-52 !text-sm h-10"
          type="primary"
          :disabled="loading"
          :loading="loading"
          @click="onSave"
          >{{ transformI18n("buttons.hssave") }}</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>
