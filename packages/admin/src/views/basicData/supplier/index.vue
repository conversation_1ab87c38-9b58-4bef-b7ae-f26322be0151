<script setup lang="ts">
import {
  supplierControllerIndex,
  supplierControllerDestroy
} from "@/api/admin/basic-data";
import { columns, platformColumns } from "./supplier_data";
import SupplierForm from "./form.vue";
import { reactive, ref, onMounted, watch } from "vue";
import DataTable from "@/components/DataTable";
import { transformI18n } from "@/plugins/i18n";
import RiEdit2Line from "@iconify-icons/ri/edit-2-line";
import { ElMessageBox, ElLoading } from "element-plus";
import searchLine from "@iconify-icons/ri/search-line";
import { useNav } from "@/layout/hooks/useNav";

defineOptions({
  name: "Clients"
});

const { userBaseInfo } = useNav();
const companyRegion = ref([]);

const form = reactive({});
const dataTableRef = ref(null);
const dataFormRef = ref(null);
const mv = ref();
const dialogPanel = ref();
const indexQuery = reactive({
  search: "",
  sort: "-id",
  filter: "",
  _with: "",
  withCount: "",
  size: 15
});
const props = defineProps({
  loadDataFlag: {
    type: String,
    default: null
  }
});

// Listen for changes in props properties
watch(
  () => props.loadDataFlag,
  (_newValue, _oldValue) => {
    loadData();
  }
);

function onEdit(row) {
  dataFormRef.value.show(row).then(() => {
    loadData();
  });
}

function onDelete(row) {
  ElMessageBox.confirm(
    `Are you sure you want to <strong>delete</strong> the <strong style='color:var(--el-color-primary)'>${row.name}</strong> supplier?`,
    transformI18n("buttons.hsSystemPrompts"),
    {
      confirmButtonText: transformI18n("buttons.hsConfirm"),
      cancelButtonText: transformI18n("buttons.hsCancel"),
      type: "warning",
      dangerouslyUseHTMLString: true,
      draggable: true
    }
  ).then(() => {
    dialogLoading();
    supplierControllerDestroy(row.id)
      .then(() => {
        dialogPanel.value.close();
        loadData();
      })
      .catch(() => {
        dialogPanel.value.close();
      });
  });
}

function loadData() {
  dataTableRef.value.loadData();
}

function tableQueryParams(form) {
  const filterInfo = [];
  const search = form.search || "";
  if (companyRegion.value.length) {
    filterInfo.push("company_region:in:" + companyRegion.value.join("|"));
  }
  return Object.assign(
    { ...indexQuery },
    { filter: filterInfo.length === 0 ? indexQuery.filter : filterInfo.join() },
    { search: search }
  );
}

function dialogLoading() {
  dialogPanel.value = ElLoading.service({
    target: mv.value.dialogRef,
    text: "Loading...",
    background: "rgba(0,0,0,0.7)"
  });
}

onMounted(() => {});
</script>

<template>
  <el-card ref="mv" shadow="never">
    <div class="mt-2 px-2 pb-2 bg-bg_color">
      <div class="flex justify-end w-full h-[50px] list-form">
        <el-select
          v-if="userBaseInfo.isPlatformUser"
          class="rounded-md border border-[#e9ebf0] pr-2 pl-2 mr-2"
          v-model="companyRegion"
          @change="loadData"
          multiple
          collapse-tags
          collapse-tags-tooltip
          :placeholder="'Company'"
        >
          <el-option
            v-for="item in userBaseInfo.companyRegion"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="search-box">
          <el-input
            ref="inputRef"
            v-model="form.search"
            size="large"
            clearable
            :placeholder="transformI18n('leads.keywordPlaceHolder')"
            @input="loadData()"
            class="table-search-input"
          >
            <template #prefix>
              <el-tooltip
                class="item"
                effect="dark"
                content="You can search by Name,Contact People,Contact Phone or Note"
                placement="top"
              >
                <IconifyIconOffline
                  class="mr-[5px]"
                  width="16px"
                  height="16px"
                  color="#656F7D"
                  :icon="searchLine"
                />
              </el-tooltip>
            </template>
          </el-input>
        </div>
      </div>
      <data-table
        ref="dataTableRef"
        :columns="userBaseInfo.isPlatformUser ? platformColumns : columns"
        :source="supplierControllerIndex"
        :form="form"
        :slotNames="['operation', 'user_status']"
        :query-params="tableQueryParams"
        :header-cell-style="{
          background: 'var(--el-table-row-hover-bg-color)',
          color: 'var(--el-text-color-primary)'
        }"
      >
        <template #operation="{ row }">
          <div>
            <el-dropdown trigger="click">
              <span class="iconfont just-icon-gengduo" />
              <template #dropdown>
                <el-dropdown-menu>
                  <Auth value="editSupplier">
                    <el-dropdown-item
                      class="text-[#2A2E34] text-base"
                      @click.stop="onEdit(row)"
                    >
                      <IconifyIconOffline
                        class="mr-[5px]"
                        width="20px"
                        height="20px"
                        color="#9B3CE5"
                        :icon="RiEdit2Line"
                      />
                      {{ transformI18n("buttons.hseditor") }}
                    </el-dropdown-item>
                  </Auth>
                  <Auth value="delSupplier">
                    <el-dropdown-item
                      class="text-[#2A2E34] text-base"
                      @click.stop="onDelete(row)"
                    >
                      <i
                        class="!text-xl text-[#9B3CE5] iconfont just-icon-cancelled"
                      />
                      {{ transformI18n("buttons.hsdelete") }}
                    </el-dropdown-item>
                  </Auth>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </template>
      </data-table>
      <supplier-form ref="dataFormRef" />
    </div>
  </el-card>
</template>
<style lang="sass" scoped>
:deep(.el-form--inline .el-form-item)
  margin-right: 0
</style>
