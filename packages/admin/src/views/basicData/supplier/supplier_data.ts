import { requiredField } from "@/utils/form";
import { transformI18n } from "@/plugins/i18n";
export const indexDefaultQuery = {
  search: "",
  sort: ["-id"],
  filter: [],
  _with: [],
  withCount: []
};

export const columns: TableColumnList = [
  {
    label: "ID",
    width: "50",
    prop: "id",
    fixed: true
  },
  {
    label: transformI18n("basicData.supplierName"),
    prop: "name"
  },
  {
    label: transformI18n("basicData.supplierContactPeople"),
    prop: "contact_people"
  },
  {
    label: transformI18n("basicData.supplierContactPhone"),
    prop: "contact_phone"
  },
  {
    label: transformI18n("basicData.supplierNote"),
    prop: "note"
  },
  {
    label: transformI18n("buttons.hsCreatedAt"),
    prop: "created_at"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];
export const platformColumns: TableColumnList = [
  {
    label: "ID",
    width: "50",
    prop: "id",
    fixed: true
  },
  {
    label: transformI18n("basicData.supplierName"),
    prop: "name"
  },
  {
    label: transformI18n("basicData.supplierContactPeople"),
    prop: "contact_people"
  },
  {
    label: transformI18n("basicData.supplierContactPhone"),
    prop: "contact_phone"
  },
  {
    label: transformI18n("basicData.supplierNote"),
    prop: "note"
  },
  {
    label: "Company",
    prop: "company_region"
  },
  {
    label: transformI18n("buttons.hsCreatedAt"),
    prop: "created_at"
  },
  {
    label: transformI18n("buttons.hsaction"),
    width: "60",
    fixed: "right",
    slot: "operation"
  }
];
export const REGEXP_PHONE = /^((04|02|03|07|08)\d{8}|(000|111)\d{7})$/;

export const rules = {
  name: [requiredField(transformI18n("basicData.supplierName"))],
  contact_people: [
    requiredField(transformI18n("basicData.supplierContactPeople"))
  ],
  contact_phone: [
    {
      validator: (rule, value, callback) => {
        if (!value) {
          return false;
        } else if (!REGEXP_PHONE.test(value)) {
          callback(new Error(transformI18n("client.phoneRuleReg")));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ],
  company_region: [requiredField("Please select company")]
};
