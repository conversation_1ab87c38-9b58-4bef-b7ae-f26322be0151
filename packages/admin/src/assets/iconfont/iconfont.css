@font-face {
  font-family: "iconfont"; /* Project id 4463488 */
  src: url("iconfont.woff2?t=1744976406109") format("woff2"),
    url("iconfont.woff?t=1744976406109") format("woff"),
    url("iconfont.ttf?t=1744976406109") format("truetype"),
    url("iconfont.svg?t=1744976406109#iconfont") format("svg");
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.just-icon-revoke:before {
  content: "\e623";
}

.just-icon-extra:before {
  content: "\e67f";
}

.just-icon-inCouncil:before {
  content: "\e622";
}

.just-icon-export-import:before {
  content: "\e6d9";
}

.just-icon-appt-confirm:before {
  content: "\e602";
}

.just-icon-t-manger:before {
  content: "\e601";
}

.just-icon-excel:before {
  content: "\e67e";
}

.just-icon-delete-bin:before {
  content: "\e621";
}

.just-icon-download-line:before {
  content: "\e620";
}

.just-icon-upload-line:before {
  content: "\e61f";
}

.just-icon-convert:before {
  content: "\e67d";
}

.just-icon-circle-add:before {
  content: "\e67c";
}

.just-icon-Vector:before {
  content: "\e67b";
}

.just-icon-hold:before {
  content: "\e65d";
}

.just-icon-cm:before {
  content: "\e679";
}

.just-icon-cmReceived:before {
  content: "\e67a";
}

.just-icon-upload:before {
  content: "\e677";
}

.just-icon-calendar-todo:before {
  content: "\e678";
}

.just-icon-ready:before {
  content: "\e65e";
}

.just-icon-arrow-down:before {
  content: "\e65f";
}

.just-icon-arrow-left:before {
  content: "\e660";
}

.just-icon-arrow-left-up:before {
  content: "\e661";
}

.just-icon-arrow-right:before {
  content: "\e662";
}

.just-icon-delete:before {
  content: "\e663";
}

.just-icon-check:before {
  content: "\e664";
}

.just-icon-close:before {
  content: "\e665";
}

.just-icon-calendar-schedule:before {
  content: "\e666";
}

.just-icon-completion:before {
  content: "\e667";
}

.just-icon-file-history:before {
  content: "\e668";
}

.just-icon-new_order:before {
  content: "\e669";
}

.just-icon-filter:before {
  content: "\e66a";
}

.just-icon-eye:before {
  content: "\e66b";
}

.just-icon-expand-up-down:before {
  content: "\e66c";
}

.just-icon-file-text:before {
  content: "\e66d";
}

.just-icon-onProduction:before {
  content: "\e66e";
}

.just-icon-link-details:before {
  content: "\e66f";
}

.just-icon-lock:before {
  content: "\e670";
}

.just-icon-mail:before {
  content: "\e671";
}

.just-icon-notification:before {
  content: "\e672";
}

.just-icon-pencil:before {
  content: "\e673";
}

.just-icon-user-smile:before {
  content: "\e674";
}

.just-icon-outstanding:before {
  content: "\e675";
}

.just-icon-settings:before {
  content: "\e676";
}

.just-icon-installation:before {
  content: "\e65c";
}

.just-icon-location:before {
  content: "\e790";
}

.just-icon-gengduo:before {
  content: "\e600";
}

.just-icon-appointed:before {
  content: "\e65a";
}

.just-icon-add:before {
  content: "\e65b";
}

.just-icon-followup:before {
  content: "\e791";
}

.just-icon-service:before {
  content: "\e64d";
}

.just-icon-quoted:before {
  content: "\e64e";
}

.just-icon-new:before {
  content: "\e64b";
}

.just-icon-product:before {
  content: "\e650";
}

.just-icon-sold:before {
  content: "\e651";
}

.just-icon-report:before {
  content: "\e652";
}

.just-icon-order:before {
  content: "\e64c";
}

.just-icon-permission:before {
  content: "\e653";
}

.just-icon-lead:before {
  content: "\e654";
}

.just-icon-draft:before {
  content: "\e655";
}

.just-icon-data:before {
  content: "\e64a";
}

.just-icon-dashboard:before {
  content: "\e656";
}

.just-icon-cancelled:before {
  content: "\e649";
}

.just-icon-account:before {
  content: "\e657";
}

.just-icon-client:before {
  content: "\e658";
}

.just-icon-activity:before {
  content: "\e659";
}
