buttons:
  hsLoginOut: LoginOut
  hsfullscreen: FullScreen
  hsexitfullscreen: ExitFullscreen
  fullScreenPage: Full-screen page layout
  fullScreenContent: Full-screen content area
  hsrefreshRoute: RefreshRoute
  hslogin: Login
  hsadd: Add
  hsmark: Mark/Cancel
  hssave: Save
  hssearch: Filter
  hsReset: Clear
  hsexpendAll: Expand All
  hscollapseAll: Collapse All
  hssystemSet: Open ProjectConfig
  hsdelete: Delete
  hsreload: Reload
  hseditor: Edit
  hsaction: Action
  hsCreatedAt: Creation Time
  hsStatus: Status
  hscloseCurrentTab: Close CurrentTab
  hscloseLeftTabs: Close LeftTabs
  hscloseRightTabs: Close RightTabs
  hscloseOtherTabs: Close OtherTabs
  hscloseAllTabs: Close AllTabs
  hswholeFullScreen: FullScreen
  hswholeExitFullScreen: ExitFull
  hscontentFullScreen: Content FullScreen
  hscontentExitFullScreen: Content ExitFullScreen
  hsActive: Active
  hsInactive: Inactive
  hsConfirm: Confirm
  hsCancel: Cancel
  hsSystemPrompts: System Prompts
menus:
  hshome: Dashboard
  hslogin: Login
  hsempty: Empty Page
  hssysManagement: System Manage
  hsClients: Client Records
  hsbasic: Basic Data Setting
  hsUser: System Accounts
  hsRole: Roles & Permissions
  hsProduct: Product Spec
  hsSystemMenu: Menu Manage
  hsDept: Dept Manage
  hseditor: Editor
  hsabnormal: Abnormal Page
  hsfourZeroFour: "404"
  hsfourZeroOne: "403"
  hsFive: "500"
  hscomponents: Components
  hsdialog: Dialog
  hsmessage: Message Tips
  hsvideo: Video
  hssegmented: Segmented
  hswaterfall: Waterfall
  hsmap: Map
  hsdraggable: Draggable
  hssplitPane: Split Pane
  hsText: Text Ellipsis
  hsElButton: Button
  hsbutton: Button Animation
  hsCheckButton: Check Button
  hscropping: Picture Cropping
  hsanimatecss: AnimateCss Selector
  hscountTo: Digital Animation
  hsselector: Scope Selector
  hsflowChart: Flow Chart
  hsseamless: Seamless Scroll
  hscontextmenu: Context Menu
  hstypeit: Typeit
  hsjsoneditor: JSON Editor
  hsColorPicker: Color Picker
  hsDatePicker: Date Picker
  hsDateTimePicker: DateTimePicker
  hsTimePicker: TimePicker
  hsTag: Tag
  hsStatistic: Statistic
  hsCollapse: Collapse
  hsProgress: Progress
  hsUpload: File Upload
  hsCheckCard: CheckCard
  hsmenus: MultiLevel Menu
  hsmenu1: Menu1
  hsmenu1-1: Menu1-1
  hsmenu1-2: Menu1-2
  hsmenu1-2-1: Menu1-2-1
  hsmenu1-2-2: Menu1-2-2
  hsmenu1-3: Menu1-3
  hsmenu2: Menu2
  permission: Permission Manage
  permissionPage: Page Permission
  permissionButton: Button Permission
  hstabs: Tabs Operate
  hsguide: Guide
  hsAble: Able
  hsMenuTree: Menu Tree
  hsVideoFrame: Video Frame Capture
  hsWavesurfer: Audio Visualization
  hsOptimize: Debounce、Throttle、Copy、Longpress Directives
  hsWatermark: Water Mark
  hsPrint: Print
  hsDownload: Download
  hsExternalPage: External Page
  hsExternalDoc: Docs External
  hsEmbeddedDoc: Docs Embedded
  externalLink: Vue-Pure-Admin
  pureutilsLink: Pure-Admin-Utils
  hsEpDocument: Element-Plus
  hsTailwindcssDocument: Tailwindcss
  hsVueDocument: Vue3
  hsViteDocument: Vite
  hsPiniaDocument: Pinia
  hsRouterDocument: Vue-Router
  hsAbout: About
  hsResult: Result Page
  hsSuccess: Success Page
  hsFail: Fail Page
  hsIconSelect: Icon Select
  hsTimeline: Time Line
  hsLineTree: LineTree
  hsList: List Page
  hsListCard: Card List Page
  hsDebounce: Debounce & Throttle
  hsFormDesign: Form Design
  hsBarcode: Barcode
  hsQrcode: Qrcode
  hsCascader: Area Cascader
  hsSwiper: Swiper Plugin
  hsVirtualList: Virtual List
  hsPdf: PDF Preview
  hsExcel: Export Excel
  hsInfiniteScroll: Table Infinite Scroll
  hsSensitive: Sensitive Filter
  hsPinyin: PinYin
  hsdanmaku: Danmaku
  hsPureTableBase: Base Usage
  hsPureTableHigh: High Usage
  hsboard: Paint Board
  hsMenuoverflow: Menu Overflow Show Tooltip Text
  hsChildMenuoverflow: Child Menu Overflow Show Tooltip Text,
  hsLeads: Leads
  hsAppointment: Appointments
  hsOrders: Production Orders
  hsService: Order Service
  hasReportManage: Report Manager
  hasReport: Report
  hasDataSynchronizationManage: Data Synchronization
status:
  hsLoad: Loading...
login:
  title: Log in to your account
  desc: Welcome back! Please enter your details.
  username: Username
  password: Password
  verifyCode: VerifyCode
  remember: Remember
  rememberInfo: After checking and logging in, will automatically log in to the system without entering your username and password within the specified number of days.
  sure: Sure Password
  forget: Forget Password?
  login: Login
  thirdLogin: Third Login
  phoneLogin: Phone Login
  qRCodeLogin: QRCode Login
  register: Register
  weChatLogin: WeChat Login
  alipayLogin: Alipay Login
  qqLogin: QQ Login
  weiboLogin: Weibo Login
  phone: Phone
  smsVerifyCode: SMS VerifyCode
  back: Back
  test: Mock Test
  tip: After scanning the code, click "Confirm" to complete the login
  definite: Definite
  loginSuccess: Login Success
  registerSuccess: Regist Success
  tickPrivacy: Please tick Privacy Policy
  readAccept: I have read it carefully and accept
  privacyPolicy: Privacy Policy
  getVerifyCode: Get VerifyCode
  info: Seconds
  usernameReg: Please enter username
  passwordReg: Please enter password
  verifyCodeReg: Please enter verify code
  verifyCodeCorrectReg: Please enter correct verify code
  verifyCodeSixReg: Please enter a 6-digit verify code
  phoneReg: Please enter the phone
  phoneCorrectReg: Please enter the correct phone number format
  passwordRuleReg: A string of 10-18 characters, including uppercase and lowercase letters, digits
  passwordSureReg: Please enter confirm password
  passwordDifferentReg: The two passwords do not match!
  passwordUpdateReg: Password has been updated
  resetPassword: Password reset
  emailReg: Please enter email
  firstLoginReminder: This is your first login after reset  password . For your account security, we recommend you to set a new password immediately.
tips:
  deleteConfirm: Are you sure you want to delete it?
  searchFormReg: Please enter keywords
fields:
  login_name: Username
common:
  keyword: Keyword
  clear: Clear
  filter: Filter
  reset: Reset
  columnSetting: Column Setting
  refresh: Refresh
  all: All
  columns: Columns
  cancle: Cancel
  input: Input
  select: Select
  create: Create
  search: Search
  comments: Comments
  welcomeBack: Welcome back,
  dashboardDesc: Track, manage and forecast your client and orders.
  changePwd: Change password
  viewProfile: View profile
  createDate: Create Date
  updateDate: Update Date
  empty: Empty
  noData: No Data
  notYet: Not yet
  changeResult: Change Result
  changeComments: Please enter the comments
  resultInfoFirst: The current
  resultInfoSecond: will be updated to the ‘
  resultInfoLast: ’ result. Please fill in the following information.
  resultCancelInfo: Please note that this action will cancel all related appointment.
  resultCompletionInfo: Please make sure to verify that the payment has been successfully completed before confirming this action.
  result: Result
  followupDate: Followup Date
  soldAmount: Sold Amount
  quotedAmount: Quoted Amount
  retailAmount: Retail Amount
  subresult: Subresult
  quoted: Quoted
  sold: Sold
  relatedOrder: Related Order
  productionRequirements: Production Requirements
  changePassword: Change Password
  oldPasswordRequire: Please input the old password
  newPasswordRequire: Please input the new password
  confirmPassword: Confirm Password
  sameAsOladPassword: Please enter a different password than the old one
  oldPassword: Old Password
  newPassword: New Password
  success: Success
  completed: Completed
  cancelled: Cancelled
  completion: Completion
  cm: CM
  ready: Ready
  new: New
  changeApptDuration: Please enter the meeting duration
  viewHistory: View History
  received: Plan Received
role:
  name: Role Name
  permissionCode: Permission Code
  newRole: New Role
  editRole: Edit Role
  menuPermission: Menu Permission
client:
  new: New Client
  edit: Edit Client
  client: Client
  title: Title
  givenName: Given Name
  surname: Surname
  phone: Phone
  secTitle: Second Title
  secGivenName: Second Given Name
  secSurname: Second Surname
  secPhone: Second Phone
  email: Email
  address: Address
  suburb: Suburb
  newClient: New Client
  emailRuleReg: Please enter the correct email format
  phoneRuleReg: Please enter the correct phone number starting with 0
productSpec:
  category: Category
  product: Product
  productCategory: Product Category
  importAndExportBColor: Import & Export BColor
  importAndExportBMesh: Import & Export BMesh
basicData:
  name: Name
  promotion: Promotion
  source: Source
  subSource: Sub source
  supplier: Supplier
  supplierName: Suppler Name
  supplierContactPeople: Contact People
  supplierContactPhone: Contact Phone
  supplierNote: Note
  editSupplier: Edit Supplier
  newSupplier: New Supplier
  supplierContact: Contact
  campaign: Campaign
  newCost: New Cost
  editCost: Edit Cost
  cost: Cost
  costType: Type
  costQueryDate: Enquiry Date
  costEndDate: End Date
  costSubProduct: Sub Product
  costAmount: Cost
  costSource: Cost Source
  costSubSource: Sub Source
  target: Target
permission:
  module: Module
  item: Item
  setting: Permissions
  viewDashBoard: View Dashboard
  viewAnyAppointment: View Appointment
  editAppointment: Edit Appointment
  addAppointment: Create Appointment
  addLeadsAppointment: Create Appointment
  addOrderAppointment: Create Appointment
  cancelAppointment: Cancel Appointment
  assignAppointment: Assign Appointment
  completeAppointment: Complete Appointment
  viewAnyLeads: View Leads
  addLeads: Create Leads
  editManagement: Edit Management info of Leads
  cancelLeads: Cancel Leads
  changeResult: Change Result
  changeLeadsResult: Change Result
  changeOrderResult: Change Result
  confirmLeads: Confirm Draft Leads
  followupLeads: Followup Leads
  viewAnyOrders: View Order
  orderSpec: Spec Management
  viewPayment: Payment View
  updatePayment: Payment Update
  viewServicePayment: Payment View
  updateServicePayment: Payment Update
  convertToOrder: Convert To Order
  cancelOrder: Cancel Order
  canRevokeOrder: Revoke Complete Order
  addSupplierForOrder: Add/Edit Supplier
  removeSupplierForOrder: Delete Supplier
  addSupplierForService: Add/Edit Supplier
  removeSupplierForService: Delete Supplier
  createService: Create Service
  editOrderManagement: Edit Management
  uploadFileForOrder: Upload File
  viewAnyClient: Client List
  viewClient: Client Detail
  addClient: Add Client
  editClient: Edit Client
  delClient: Delete Client
  viewAnySource: View Source
  viewAnyPromotion: View Promotion
  viewAnySupplier: View Supplier
  addSupplier: Add Supplier
  editSupplier: Edit Supplier
  delSupplier: Delete Supplier
  viewAnyAccount: Account List
  viewAccount: Account Detail
  addAccount: Add Account
  editAccount: Edit Account
  delAccount: Delete Account
  viewAnyRole: View Any Role
  viewRole: View Role
  addRole: Add Role
  editRole: Edit Role
  delRole: Delete Role
  viewAnyProduct: View Products
  resetPassword: Reset Password
  viewAnyService: View Service Order
  editServiceManagement: Edit Management
  addServicePayment: Payment Management
  serviceSpec: Spec Management
  uploadFileForService: Upload File
  cancelService: Cancel Service
  addServiceAppointment: Create Appointment
  changeServiceResult: Change Result
  viewAnyReportManager: Report Dashboards List
  addReportManager: Add Report Dashboard
  editReportManager: Edit Report Dashboard
  delReportManager: Delete Report Dashboard
  delServiceUploadFile: Delete Service Upload File
  delLeadsUploadFile: Delete Lead Upload File
  delOrderUploadFile: Delete Order Upload File
  uploadFileForLeads: Upload File For Lead
  removeFromBlacklist: Remove From Blacklist
  moveToBlacklist: Move To Blacklist
  viewAnyCost: View Cost
  changeSource: Change Lead Source,
  revokeLeads: Revoke Canceled Leads
  revokeAppointment: Revoke Appointment
  changeLeadsCompany: Company Leads Company
  viewAnyTarget: View Target
leads:
  newDraftLeads: New Draft Leads
  draft: Draft
  all: All Leads
  new: New
  followup: Followup
  appointed: Appointed
  quoted: Quoted
  sold: Sold
  cancelled: Cancelled
  confirm: Confirm
  changeResult: Change Result
  edit: Edit
  cancel: Cancel
  createAppt: Create Appt
  convertToOrder: Convert to Order
  keywordPlaceHolder: Search ...
  createNewLeads: Create New Draft Leads
  createWithSameClient: Create New Draft Leads with Same Client
  productInterestedIn: Product Interested In
  clientInformation: Client Information
  leadsSourceInformation: Leads Source Information
  leadsNote: Leads Note
  telephoneOrmobilePhone: Telephone or mobile Phone
  addressOfTheClient: Address of the client
  emailOfTheClient: Email of the client
  productSelectNote: Need multiple products? You can quickly create another leads for the same customer after the lead is created.
  commentsHold: Comments about the products interested in...
  leadsNoteHold: Input leads note please...
  leadsSource: Leads Source
  dateOfEnquiry: Date of Enquiry
  existingClientBtn: Existing Client
  backupContact: Secondary Contact
  clientPhone: Client Phone
  backupContactPhone: Secondary Contact Phone
  management: Management
  salesManager: Sales Manager
  retail: Retail
  spokenTo: Spoken To
  relatedOrder: Related Order
  soldDate: Sold Date
  productRequirements: Product Requirements
  commentsHoldDetail: Write detailsed product requirements...
  campaignHoldDetail: Campaign
  addToBlacklist: Move to blacklist
  revoke: Revoke Canceled Leads
summary:
  newLeads: New leads
  newOrders: New orders
  installOrders: Installation Appointment
  newAppointment: Sales Appointment
  saleAmount: Sales Amount
  totalLeads: Total Leads
  salesAppointment: Sales Appointment
  soldLeads: Sold Leads
  depositAmount: Deposit Amount
  cmAppointment: CM Appointment
  installAppointment: Install Appointment
  serviceAppointment: Service Appointment
  installAmount: Install Amount
  completionAmount: Completion Amount
account:
  name: Name
  username: Account Name
  position: Position
  productCategory: Product Category
  role: Role
  mobile: Mobile
  email: Email
  newAccount: New Account
  editAccount: Edit Account
  userType: User Type
appointment:
  edit: Edit Appointment
  new: Create Appointment
  createAppointments: Create Appointments
  objectId: Related Object ID
  date: Date
  time: Time
  type: Appointment Type
  cm: Check Measurer
  install: Install
  service: Service
  labelsale: Sales
  labelinstall: Installer
  labelcm: CM
  relatedObject: Related Object ID
  clientPhone: Client Phone
  sendToMyCalender: Sync Calendar
  notifyClient: Notify Client
  notifyAssign: Notify
  uploadAttachments: Upload Attachments
  completeAppointment: Complete Appointment
  checkMeasurerDate: Check Measurer Date
  checkMeasurerName: Check Measurer Name
  assignTo: Assign to
  cmAttachment: Production Attachment
  leadsAttachment: Sales Attachment
  serviceAttachment: Service Attachment
  hadOutstandingAppNote: There is already an outstanding appointment, you can modify this appointment for this object.
orders:
  review: Review
  all: All Orders
  new: New
  onProduction: On Production
  readyToInstall: Ready to Install
  searchOrders: Search Orders
  result_new_order: New Order
  result_cm: CM
  result_cmReceived: CM Received
  result_onProduction: On Production
  result_ready: Ready
  result_installation: Installation
  result_service: Service
  result_outstanding: Outstanding
  result_completion: Completion
  result_hold: On Hold
  result_new: New Order
  result_cancelled: Cancelled
  result_extra: Extra
  result_received: Plan Received
  on_product_date: Product Date
  ready_date: Ready Date
  note: Note
  toBeCollect: To Be Collect
report:
  name: Name
  url: Url
  urlRuleReg: Please enter the correct url format
  editReport: Edit Report
  addReport: Add Report
  newReport: Create Report
  isShow: If Show
  welcome: Our analytics
  reportDesc: MetaBase Report Dashboards
dataSynchronizationType:
  export_blinds_contacts: Export Blinds contacts File
  export_blinds_sales: Export Blinds Sales File
  export_blinds_contProd: Export Blinds ContProd File
  export_blinds_salestran: Export Blinds SalesTran File
  export_gdoor_contacts: Export GDoor contacts File
  export_gdoor_sales: Export GDoor Sales File
  export_gdoor_contProd: Export GDoor ContProd File
  export_gdoor_salestran: Export GDoor SalesTran File
  export_lawn_contacts: Export Lawn contacts File
  export_lawn_sales: Export Lawn Sales File
  export_lawn_contProd: Export Lawn ContProd File
  export_lawn_salestran: Export Lawn SalesTran File
  export_roof_contacts: Export Roof contacts File
  export_roof_sales: Export Roof Sales File
  export_roof_contProd: Export Roof ContProd File
  export_roof_salestran: Export Roof SalesTran File
  blinds_contacts: Import Blinds contacts File
  blinds_sales: Import Blinds Sales File
  blinds_contProd: Import Blinds ContProd File
  blinds_salestran: Import Blinds SalesTran File
  gdoor_contacts: Import GDoor contacts File
  gdoor_sales: Import GDoor Sales File
  gdoor_contProd: Import GDoor ContProd File
  gdoor_salestran: Import GDoor SalesTran File
  lawn_contacts: Import Lawn contacts File
  lawn_sales: Import Lawn Sales File
  lawn_contProd: Import Lawn ContProd File
  lawn_salestran: Import Lawn SalesTran File
  roof_contacts: Import Roof contacts File
  roof_sales: Import Roof Sales File
  roof_contProd: Import Roof ContProd File
  roof_salestran: Import Roof SalesTran File
