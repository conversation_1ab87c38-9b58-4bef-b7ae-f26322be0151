buttons:
  hsLoginOut: 退出系统
  hsfullscreen: 全屏
  hsexitfullscreen: 退出全屏
  fullScreenPage: 整体页面全屏
  fullScreenContent: 内容区全屏
  hsrefreshRoute: 刷新路由
  hslogin: 登录
  hsadd: 新增
  hsmark: 标记/取消
  hssave: 保存
  hssearch: 搜索
  hsexpendAll: 全部展开
  hscollapseAll: 全部折叠
  hssystemSet: 打开项目配置
  hsdelete: 删除
  hsreload: 重新加载
  hscloseCurrentTab: 关闭当前标签页
  hscloseLeftTabs: 关闭左侧标签页
  hscloseRightTabs: 关闭右侧标签页
  hscloseOtherTabs: 关闭其他标签页
  hscloseAllTabs: 关闭全部标签页
  hswholeFullScreen: 全屏
  hswholeExitFullScreen: 退出全屏
  hscontentFullScreen: 内容区全屏
  hscontentExitFullScreen: 内容区退出全屏
menus:
  hshome: 首页
  hslogin: 登录
  hsempty: 无Layout页
  hssysManagement: 系统管理
  hsClients: Client Records
  hsUser: 用户管理
  hsRole: 角色管理
  hsProduct: Product Spec
  hsbasic: Basic Data Setting
  hsSystemMenu: 菜单管理
  hsDept: 部门管理
  hseditor: 编辑器
  hsabnormal: 异常页面
  hsfourZeroFour: "404"
  hsfourZeroOne: "403"
  hsFive: "500"
  hscomponents: 组件
  hsdialog: 函数式弹框
  hsmessage: 消息提示
  hsvideo: 视频
  hssegmented: 分段控制器
  hswaterfall: 瀑布流无限滚动
  hsmap: 地图
  hsdraggable: 拖拽
  hssplitPane: 切割面板
  hsText: 文本省略
  hsElButton: 按钮
  hsCheckButton: 可选按钮
  hsbutton: 按钮动效
  hscropping: 图片裁剪
  hsanimatecss: animate.css选择器
  hscountTo: 数字动画
  hsselector: 范围选择器
  hsflowChart: 流程图
  hsseamless: 无缝滚动
  hscontextmenu: 右键菜单
  hstypeit: 打字机
  hsjsoneditor: JSON编辑器
  hsColorPicker: 颜色选择器
  hsDatePicker: 日期选择器
  hsDateTimePicker: 日期时间选择器
  hsTimePicker: 时间选择器
  hsTag: 标签
  hsStatistic: 统计组件
  hsCollapse: 折叠面板
  hsProgress: 进度条
  hsUpload: 文件上传
  hsCheckCard: 多选卡片
  hsmenus: 多级菜单
  hsmenu1: 菜单1
  hsmenu1-1: 菜单1-1
  hsmenu1-2: 菜单1-2
  hsmenu1-2-1: 菜单1-2-1
  hsmenu1-2-2: 菜单1-2-2
  hsmenu1-3: 菜单1-3
  hsmenu2: 菜单2
  permission: 权限管理
  permissionPage: 页面权限
  permissionButton: 按钮权限
  hstabs: 标签页操作
  hsguide: 引导页
  hsAble: 功能
  hsMenuTree: 菜单树结构
  hsVideoFrame: 视频帧截取-wasm版
  hsWavesurfer: 音频可视化
  hsOptimize: 防抖、截流、复制、长按指令
  hsWatermark: 水印
  hsPrint: 打印
  hsDownload: 下载
  hsExternalPage: 外部页面
  hsExternalDoc: 文档外链
  hsEmbeddedDoc: 文档内嵌
  externalLink: vue-pure-admin
  pureutilsLink: pure-admin-utils
  hsEpDocument: element-plus
  hsTailwindcssDocument: tailwindcss
  hsVueDocument: vue3
  hsViteDocument: vite
  hsPiniaDocument: pinia
  hsRouterDocument: vue-router
  hsAbout: 关于
  hsResult: 结果页面
  hsSuccess: 成功页面
  hsFail: 失败页面
  hsIconSelect: 图标选择器
  hsTimeline: 时间线
  hsLineTree: 树形连接线
  hsList: 列表页
  hsListCard: 卡片列表页
  hsDebounce: 防抖节流
  hsFormDesign: 表单设计器
  hsBarcode: 条形码
  hsQrcode: 二维码
  hsCascader: 区域级联选择器
  hsSwiper: Swiper插件
  hsVirtualList: 虚拟列表
  hsPdf: PDF预览
  hsExcel: 导出Excel
  hsInfiniteScroll: 表格无限滚动
  hsSensitive: 敏感词过滤
  hsPinyin: 汉语拼音
  hsdanmaku: 弹幕
  hsPureTableBase: 基础用法（23个示例）
  hsPureTableHigh: 高级用法（11个示例）
  hsboard: 艺术画板
  hsMenuoverflow: 目录超出显示 Tooltip 文字提示
  hsChildMenuoverflow: 菜单超出显示 Tooltip 文字提示,
  hsLeads: 线索,
  hsAppointment: 预约,
  hsOrders: 产品订单
  hasReportManage: Report Manager
  hasDataSynchronizationManage: hasDataSynchronizationManage
status:
  hsLoad: 加载中...
login:
  title: Log in to your account
  desc: Welcome back! Please enter your details.
  username: 账号
  password: 密码
  verifyCode: 验证码
  remember: 天内免登录
  rememberInfo: 勾选并登录后，规定天数内无需输入用户名和密码会自动登入系统
  sure: 确认密码
  forget: 忘记密码?
  login: 登录
  thirdLogin: 第三方登录
  phoneLogin: 手机登录
  qRCodeLogin: 二维码登录
  register: 注册
  weChatLogin: 微信登录
  alipayLogin: 支付宝登录
  qqLogin: QQ登录
  weiboLogin: 微博登录
  phone: 手机号码
  smsVerifyCode: 短信验证码
  back: 返回
  test: 模拟测试
  tip: 扫码后点击"确认"，即可完成登录
  definite: 确定
  loginSuccess: 登录成功
  registerSuccess: 注册成功
  tickPrivacy: 请勾选隐私政策
  readAccept: 我已仔细阅读并接受
  privacyPolicy: 《隐私政策》
  getVerifyCode: 获取验证码
  info: 秒后重新获取
  usernameReg: 请输入账号
  passwordReg: 请输入密码
  verifyCodeReg: 请输入验证码
  verifyCodeCorrectReg: 请输入正确的验证码
  verifyCodeSixReg: 请输入6位数字验证码
  phoneReg: 请输入手机号码
  phoneCorrectReg: 请输入正确的手机号码格式
  passwordRuleReg: 密码格式应为8-18位数字、字母、符号的任意两种组合
  passwordSureReg: 请输入确认密码
  passwordDifferentReg: 两次密码不一致!
  passwordUpdateReg: 修改密码成功
  resetPassword: Password reset
  emailReg: Please enter email
  firstLoginReminder: This is your first login after reset  password. For your account security, we recommend you to set a new password immediately.

tips:
  deleteConfirm: Are you sure you want to delete it?
fields:
  login_name: Username
common:
  keyword: Keyword
  clear: Clear
  filter: Filter
  reset: Reset
  columnSetting: Column Setting
  refresh: Refresh
  all: All
  columns: Columns
  cancle: Cancel
  input: Input
  select: Select
  create: Create
  search: Search
  comments: Comments
  welcomeBack: Welcome back,
  dashboardDesc: Track, manage and forecast your client and orders.
  changePwd: Change password
  viewProfile: View profile
  createDate: Create Date
  updateDate: Update Date
  empty: Empty
  noData: No Data
  notYet: Not yet
  changeResult: Change Result
  changeComments: Please enter the comments
  resultInfoFirst: The current
  resultInfoSecond: will be updated to the ‘
  resultInfoLast: ’ result. Please fill in the following information.
  resultCancelInfo: Please note that this action will cancel all related appointment.
  resultCompletionInfo: Please make sure to verify that the payment has been successfully completed before confirming this action.
  result: Result
  followupDate: Followup Date
  soldAmount: Sold Amount
  quotedAmount: Quoted Amount
  retailAmount: Retail Amount
  subresult: Subresult
  quoted: Quoted
  sold: Sold
  relatedOrder: Related Order
  productionRequirements: Production Requirements
  changePassword: Change Password
  oldPasswordRequire: Please input the old password
  newPasswordRequire: Please input the new password
  confirmPassword: Confirm Password
  sameAsOladPassword: Please enter a different password than the old one
  oldPassword: Old Password
  newPassword: New Password
  success: Success
  completed: Completed
  cancelled: Cancelled
  completion: Completion
  cm: CM
  ready: Ready
  new: New
  changeApptDuration: Please enter the meeting duration
  viewHistory: View History
  received: Plan Received
role:
  name: Role Name
  permissionCode: Permission Code
  newRole: New Role
  editRole: Edit Role
  menuPermission: Menu Permission
client:
  new: New Client
  edit: Edit Client
  client: Client
  title: Title
  givenName: Given Name
  surname: Surname
  phone: Phone
  secTitle: Second Title
  secGivenName: Second Given Name
  secSurname: Second Surname
  secPhone: Second Phone
  email: Email
  address: Address
  suburb: Suburb
  newClient: New Client
  emailRuleReg: Please enter the correct email format
  phoneRuleReg: Please enter the correct phone number starting with 0
productSpec:
  category: Category
  product: Product
  productCategory: Product Category
  importAndExportBColor: Import & Export BColor
  importAndExportBMesh: Import & Export BMesh
basicData:
  name: Name
  promotion: Promotion
  source: Source
  subSource: Sub source
  supplier: Supplier
  supplierName: Suppler Name
  supplierContactPeople: Contact People
  supplierContactPhone: Contact Phone
  supplierNote: Note
  editSupplier: Edit Supplier
  newSupplier: New Supplier
  supplierContact: Contact
  campaign: Campaign
  newCost: New Cost
  editCost: Edit Cost
  cost: Cost
  costType: Type
  costQueryDate: Enquiry Date
  costEndDate: End Date
  costSubProduct: Sub Product
  costAmount: Cost
  costSource: Cost Source
  costSubSource: Sub Source  
  target: Target
permission:
  module: Module
  item: Item
  setting: Permissions
  viewDashBoard: View Dashboard
  viewAnyAppointment: View Appointment
  editAppointment: Edit Appointment
  addAppointment: Create Appointment
  addLeadsAppointment: Create Appointment
  addOrderAppointment: Create Appointment
  cancelAppointment: Cancel Appointment
  assignAppointment: Assign Appointment
  completeAppointment: Complete Appointment
  viewAnyLeads: View Leads
  addLeads: Create Leads
  editManagement: Edit Management info of Leads
  cancelLeads: Cancel Leads
  changeResult: Change Result
  changeLeadsResult: Change Result
  changeOrderResult: Change Result
  confirmLeads: Confirm Draft Leads
  followupLeads: Followup Leads
  viewAnyOrders: View Order
  orderSpec: Spec Management
  viewPayment: Payment View
  updatePayment: Payment Update
  viewServicePayment: Payment View
  updateServicePayment: Payment Update
  convertToOrder: Convert To Order
  cancelOrder: Cancel Order
  canRevokeOrder: Revoke Complete Order
  addSupplierForOrder: Add/Edit Supplier
  removeSupplierForOrder: Delete Supplier
  addSupplierForService: Add/Edit Supplier
  removeSupplierForService: Delete Supplier
  createService: Create Service
  editOrderManagement: Edit Management
  uploadFileForOrder: Upload File
  viewAnyClient: Client List
  viewClient: Client Detail
  addClient: Add Client
  editClient: Edit Client
  delClient: Delete Client
  viewAnySource: View Source
  viewAnyPromotion: View Promotion
  viewAnySupplier: View Supplier
  addSupplier: Add Supplier
  editSupplier: Edit Supplier
  delSupplier: Delete Supplier
  viewAnyAccount: Account List
  viewAccount: Account Detail
  addAccount: Add Account
  editAccount: Edit Account
  delAccount: Delete Account
  viewAnyRole: View Any Role
  viewRole: View Role
  addRole: Add Role
  editRole: Edit Role
  delRole: Delete Role
  viewAnyProduct: View Products
  resetPassword: Reset Password
  viewAnyService: View Service Order
  editServiceManagement: Edit Management
  addServicePayment: Payment Management
  serviceSpec: Spec Management
  uploadFileForService: Upload File
  cancelService: Cancel Service
  addServiceAppointment: Create Appointment
  changeServiceResult: Change Result
  delServiceUploadFile: Delete Service Upload File
  delLeadsUploadFile: Delete Lead Upload File
  delOrderUploadFile: Delete Order Upload File
  uploadFileForLeads: Upload File For Lead
  removeFromBlacklist: Remove From Blacklist
  moveToBlacklist: Move To Blacklist
  viewAnyCost: View Cost
  changeSource: Change Lead Source
  revokeLeads: Revoke Canceled Leads
  revokeAppointment: Revoke Sale Appointment
  changeLeadsCompany: Company Leads Company
  viewAnyTarget: View Target
leads:
  newDraftLeads: New Draft Leads
  draft: Draft
  all: All Leads
  new: New
  followup: Followup
  appointed: Appointed
  quoted: Quoted
  sold: Sold
  cancelled: Cancelled
  confirm: Confirm
  changeResult: Change Result
  edit: Edit
  cancel: Cancel
  createAppt: Create Appt
  convertToOrder: Convert to Order
  keywordPlaceHolder: Search Leads...
  createNewLeads: Create New Draft Leads
  createWithSameClient: Create New Draft Leads with Same Client
  productInterestedIn: Product Interested In
  clientInformation: Client Information
  leadsSourceInformation: Leads Source Information
  leadsNote: Leads Note
  telephoneOrmobilePhone: Telephone or mobile Phone
  addressOfTheClient: Address of the client
  emailOfTheClient: Email of the client
  productSelectNote: Need multiple products? You can quickly create another leads for the same customer after the lead is created.
  commentsHold: Comments about the products interested in...
  leadsNoteHold: Input leads note please...
  leadsSource: Leads Source
  dateOfEnquiry: Date of Enquiry
  existingClientBtn: Existing Client
  backupContact: Secondary Contact
  clientPhone: Client Phone
  backupContactPhone: Secondary Contact Phone
  management: Management
  salesManager: Sales Manager
  retail: Retail
  spokenTo: Spoken To
  relatedOrder: Related Order
  productRequirements: Product Requirements
  commentsHoldDetail: Write detailsed product requirements...
  soldDate: Sold Date
  campaignHoldDetail: Campaign
  revoke: Revoke Canceled Leads
summary:
  newLeads: New leads
  newOrders: New orders
  installOrders: Installation orders
  newAppointment: New Appointment
  saleAmount: Sales Amount
  totalLeads: Total Leads
  salesAppointment: Sales Appointment
  soldLeads: Sold Leads
  depositAmount: Deposit Amount
  cmAppointment: CM Appointment
  installAppointment: Install Appointment
  serviceAppointment: Service Appointment
  installAmount: Install Amount
  completionAmount: Completion Amount
  addToBlacklist: Move to blacklist
account:
  name: Name
  username: Account Name
  position: Position
  productCategory: Product Category
  role: Role
  mobile: Mobile
  email: Email
  newAccount: New Account
  editAccount: Edit Account
  userType: User Type
appointment:
  edit: Edit Appointment
  new: Create Appointment
  createAppointments: Create Appointments
  objectId: Related Object ID
  date: Date
  time: Time
  type: Appointment Type
  cm: Check Measurers
  install: Install
  service: Service
  labelsale: Sales
  labelinstall: Installer
  labelcm: CM
  relatedObject: Related Object ID
  clientPhone: Client Phone
  sendToMyCalender: Sync Calendar
  notifyClient: Notify Client
  notifyAssign: Notify Assign To
  uploadAttachments: Upload Attachments
  completeAppointment: Complete Appointment
  checkMeasurers: Check Measurers
  assignTo: Assign to
  cmAttachment: Production attachment
  leadsAttachment: Sales Attachment
  serviceAttachment: Service Attachment
  hadOutstandingAppNote: There is already an outstanding appointment, you can modify this appointment for this object.
orders:
  review: Review
  all: All Orders
  new: New
  onProduction: On Production
  readyToInstall: Ready to Install
  searchOrders: search Orders
  result_new_order: New Order
  result_cm: CM
  result_cmReceived: CM Received
  result_onProduction: On Production
  result_ready: Ready
  result_installation: Installation
  result_outstanding: Outstanding
  result_completion: Completion
  result_hold: Hold
  result_new: New Order
  result_cancelled: Cancelled
  result_service: Service
  result_extra: Extra
  result_received: Plan Received
  on_product_date: Product Date
  ready_date: Ready Date
  note: Note
  toBeCollect: To Be Collect
report:
  name: Name
  url: Url
  urlRuleReg: Please enter the correct url format
  editReport: Edit Report
  addReport: Add Report
  newReport: Create Report
  isShow: If Show
dataSynchronizationType:
  export_blinds_contacts: Export Blinds contacts File
  export_blinds_sales: Export Blinds Sales File
  export_blinds_contProd: Export Blinds ContProd File
  export_blinds_salestran: Export Blinds SalesTran File
  export_gdoor_contacts: Export GDoor contacts File
  export_gdoor_sales: Export GDoor Sales File
  export_gdoor_contProd: Export GDoor ContProd File
  export_gdoor_salestran: Export GDoor SalesTran File
  export_lawn_contacts: Export Lawn contacts File
  export_lawn_sales: Export Lawn Sales File
  export_lawn_contProd: Export Lawn ContProd File
  export_lawn_salestran: Export Lawn SalesTran File
  export_roof_contacts: Export Roof contacts File
  export_roof_sales: Export Roof Sales File
  export_roof_contProd: Export Roof ContProd File
  export_roof_salestran: Export Roof SalesTran File
  blinds_contacts: Import Blinds contacts File
  blinds_sales: Import Blinds Sales File
  blinds_contProd: Import Blinds ContProd File
  blinds_salestran: Import Blinds SalesTran File
  gdoor_contacts: Import GDoor contacts File
  gdoor_sales: Import GDoor Sales File
  gdoor_contProd: Import GDoor ContProd File
  gdoor_salestran: Import GDoor SalesTran File
  lawn_contacts: Import Lawn contacts File
  lawn_sales: Import Lawn Sales File
  lawn_contProd: Import Lawn ContProd File
  lawn_salestran: Import Lawn SalesTran File
  roof_contacts: Import Roof contacts File
  roof_sales: Import Roof Sales File
  roof_contProd: Import Roof ContProd File
  roof_salestran: Import Roof SalesTran File
