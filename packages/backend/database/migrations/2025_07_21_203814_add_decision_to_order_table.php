<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order', function (Blueprint $table) {
            //
            $table->string('decision')->nullable()->comment('The decision of the order');
            $table->string('application_id')->nullable()->comment('The application id the order');
            $table->integer('electrician_id')->nullable()->comment('The electrician user for the order.');
            $table->integer('painter_id')->nullable()->comment('The painter user for the order.');
            $table->dateTime('delivery')->nullable()->comment('The delivery date of a order.');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('order', function (Blueprint $table) {
            //
            $table->dropColumn(['decision', 'application_id', 'electrician_id', 'painter_id', 'delivery']);
        });
    }
};
