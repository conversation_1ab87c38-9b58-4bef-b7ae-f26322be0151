<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('appointment', function (Blueprint $table) {
            //
            $table->string('painter_event_id')->nullable()->comment('The painter event id of the calendar.');
            $table->string('painter_calendar_id')->nullable()->comment('The painter calendar id of google calendar.');
            $table->string('electrician_event_id')->nullable()->comment('The electrician event id of the calendar.');
            $table->string('electrician_calendar_id')->nullable()->comment('The electrician calendar id of google calendar.');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('appointment', function (Blueprint $table) {
            //
            $table->dropColumn(['painter_event_id', 'painter_calendar_id', 'electrician_event_id', 'electrician_calendar_id']);
        });
    }
};
