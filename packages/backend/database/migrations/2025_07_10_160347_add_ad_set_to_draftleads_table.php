<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('draft_leads', function (Blueprint $table) {
            //
            $table->string('ad_set')->nullable()->comment('The ad set of the draft leads');  
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('draft_leads', function (Blueprint $table) {
            //
            $table->dropColumn(['ad_set']);
        });
    }
};
