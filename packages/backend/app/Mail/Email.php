<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use Modules\Admin\Models\Account;
use Modules\Admin\Models\Appointment;
use Modules\Admin\Models\Clients;
use Modules\Admin\Models\Leads;
use Illuminate\Mail\Mailables\Attachment;
use App\Services\PdfService;

class Email extends Mailable
{
    use Queueable, SerializesModels;
    public $name;
    public $pwd;
    public $appointment = [];
    /**
     * @var array
     */
    public $attachmentFiles;

  

    public $type;
    public $message;
    public $params;

    public function __construct($name, Appointment $appointment = null, $pwd = '', $type = '', $message = null, $params = [])
    {
        $this->name = $name;
        $this->pwd = $pwd;
        $this->type = $type;
        $this->appointment = $appointment;
        $this->message = $message;
        $this->params = $params;
        $this->attachmentFiles = [];
        $this->subject($message ? 'Import leads issue' : ($appointment ? 'Appointment Notfiy': 'Reset Password'));
    }

    public function addAttachment(string $path): self
    {
        $path = trim($path);
        
        if (empty($path)) {
            throw new \InvalidArgumentException('Attachment path cannot be empty');
        }
        
        if (!file_exists($path)) {
            throw new \RuntimeException('Attachment file not found: '.$path);
        }
        
        if (!is_readable($path)) {
            throw new \RuntimeException('Attachment file not readable: '.$path);
        }
        
        $this->attachmentFiles[] = $path;
        return $this;
    }

    /**
     * 构建消息
     *
     * @return $this
     */
    public function build()
    {
        $email = null;

        if ($this->appointment) {
          if ($this->type == 'notifyAssign') {
            $email = $this->from(env('MAIL_FROM_ADDRESS'), 'Just Quality')
            ->view('emails.appointmentAssign', ['name' => $this->name]);
          } else {
            $this->subject('Our Team Member Is on the way');
            $email = $this->from(env('MAIL_FROM_ADDRESS'), 'Just Quality')
            ->view('emails.appointment', ['name' => $this->name]);
          }
        } else if ($this->message) {
          if ($this->type == 'system') {
            $this->subject('System Issue');
            $email = $this->from(env('MAIL_FROM_ADDRESS'), 'Just Quality')
            ->view('emails.systemIssue', ['errorMessage' => $this->message,'name' => $this->name, 'params' => $this->params]);
          } else if ($this->type == 'cvd') {
            $emailType = $this->params['emailType'] ?? null;
            $leadsId = $this->params['leadsId'] ? ' -'.$this->params['leadsId'] : '';
            $leads = Leads::find($this->params['leadsId']);
            $order = $leads->order ? $leads->order : null;
            switch ($emailType) {
              case 'contract': 
                $this->subject('Contract Received' . $leadsId );
                break;
              case 'submitted_to_council': 
                $this->subject('Submitted to Council' . $leadsId);
                break;
              case 'planning_consent_given':
                $this->subject('Planning Consent Granted' . $leadsId);
                break;
              case 'building_consent_given':
                $this->subject('Building Consent Granted' . $leadsId);
                break;
              case 'development_consent_given':  
                $this->subject('Development Approval Granted' . $leadsId);
                break;
              case 'installation_scheduled':  
                $this->subject('Installation Schedule' . $leadsId);
                break;
              case 'finance_activation':  
                $this->subject(' Finance activation' . $leadsId);
                break;
              case 'satisfaction_slip':  
                $this->subject('Satisfaction Slip' . $leadsId);
                try {
                    $url = PdfService::generatorSatisfactionPDF($order->id, false);
                    $this->addAttachment($url);
                    \Log::debug('Added attachment:', ['url' => $url]);
                } catch (\Exception $e) {
                    \Log::error('Failed to add satisfaction slip attachment', [
                        'exception' => $e,
                        'orderId' => $order->id
                    ]);
                }
                break;
              case 'warranty_card': 
                $this->subject('Warranty Card' . $leadsId);
                try {
                    $url = PdfService::generatorWarrantyCardPDF($order->id, false);
                    $this->addAttachment($url);
                    \Log::debug('Added attachment:', ['url' => $url]);
                } catch (\Exception $e) {
                    \Log::error('Failed to add satisfaction slip attachment', [
                        'exception' => $e,
                        'orderId' => $order->id
                    ]);
                }
                break;
            }
            $email = $this->from(env('MAIL_FROM_ADDRESS'), 'Just Quality')
                          ->view('emails.cvd', [
                            'name' => $this->name, 
                            'emailType' => $emailType, 
                            'operationsCoordinator' => $this->params['operationsCoordinator'] ?? null,
                            'isPatio' => $leads->product_category_id === 5,
                            'deliveryDate' => $order ? $order->delivery : null,
                            'installBooked' => $order ? $order->installation_date : null
                          ]);
          } else if ($this->type == 'payment') { 
            $emailType = $this->params['emailType'] ?? null;
            $leadsId = $this->params['leadsId'] ? ' -'.$this->params['leadsId'] : '';
            $leads = Leads::find($this->params['leadsId']);
            $order = $leads->order ? $leads->order : null;
            $dueAmount = $this->params['amount'] ? $this->params['amount'] : 0;
            switch ($emailType) {
              case 'cm_payment': 
                $this->subject('Progress Payment (Check Measure)' . $leadsId );
                break;
              case 'material_payment': 
                $this->subject('Progress Payment (Material)' . $leadsId );
                break;
              case 'final_payment': 
                $this->subject('Final Payment' . $leadsId );
                break;
              case 'invoice': 
                $this->subject('Paid invoice' . $leadsId );
                break;
            }
            try {
                $url = PdfService::generatorPDF($order->id, false);
                $this->addAttachment($url);
                \Log::debug('Added attachment:', ['url' => $url]);
            } catch (\Exception $e) {
                \Log::error('Failed to add satisfaction slip attachment', [
                    'exception' => $e,
                    'orderId' => $order->id
                ]);
            }
            
            $email = $this->from(env('MAIL_FROM_ADDRESS'), 'Just Quality')
                          ->view('emails.payment', [
                            'name' => $this->name, 
                            'emailType' => $emailType, 
                            'dueAmount' => $order ? $dueAmount : null,
                            'leadsId' => $this->params['leadsId'],
                            'operationsCoordinator' => $this->params['operationsCoordinator'] ?? null,
                          ]);
          } else {
            $email = $this->from(env('MAIL_FROM_ADDRESS'), 'Just Quality')
            ->view('emails.importIssue', ['errorMessage' => $this->message,'name' => $this->name]);
          }
        } else {
          $email = $this->from(env('MAIL_FROM_ADDRESS'), 'Just Quality')
            ->view('emails.password', ['pwd' => $this->pwd,'name' => $this->name]);
        }

        // 安全添加附件
        foreach ($this->attachmentFiles as $attachment) {
            try {
                if (!is_string($attachment)) {
                    throw new \RuntimeException('Attachment must be string path');
                }
                
                $email->attach($attachment, [
                    'as' => basename($attachment),
                    'mime' => mime_content_type($attachment)
                ]);
                
                \Log::debug('Successfully attached file', ['path' => $attachment]);
            } catch (\Exception $e) {
                \Log::error('Failed to process attachment', [
                    'path' => $attachment,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }

        return $email;
    }
}
