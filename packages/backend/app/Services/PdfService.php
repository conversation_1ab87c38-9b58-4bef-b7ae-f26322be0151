<?php
namespace App\Services;

use Illuminate\Support\Str;
use Modules\Admin\Models\Account;
use Modules\Admin\Models\Order;
use Modules\Admin\Models\ProductCategory;
use Modules\Admin\Models\Products;
use Modules\Admin\Models\Appointment;
use PDF;
use Illuminate\Support\Facades\App;


/**
 * Class S3Storage
 */
class PdfService
{
    public static function generatorSatisfactionPDF($orderId, $isPublic = true) {
        $data = [
            "primaryName" => "",
            "secondaryName" => "",
            "address" => "",
            "installed_date" => "",
            "Installer" => "",
            "product" => "",
        ];
        $order = Order::with(['lead'])->find($orderId);
        $leads = $order->lead;
        // client info and sold
        if ($leads && $leads->client) {
            $data["primaryName"] = $leads->client->given_name . " " . $leads->client->surname;
            if ($leads->client->sec_given_name || $leads->client->sec_surname) {
                $data["secondaryName"] = $leads->client->sec_given_name . " " . $leads->client->sec_surname;
            }
            $data["address"] = $leads->client->address;
            if ($order->installed_date) {
                $data["installed_date"] =  format_datetime($order->installed_date, 'd/m/Y');
            }
        }
        if ($order->install_id) {
            $installer = Account::find($order->install_id);
            $data["Installer"] = $installer->name;
        }
        if ($order->product) {
            $data["product"] = $order->product->name;
        }
        try {
            $pdf = App::make('snappy.pdf.wrapper')->setPaper('a4')->setOrientation('portrait');
            $pdf = $pdf->loadView('pdf.customerSatisfactionSlip', ['data' => $data]);
            //  return $pdf->download();
            $folder      = storage_path('app') . '/public/pdf/';
            if (!\Illuminate\Support\Facades\File::exists($folder)) {
                \Illuminate\Support\Facades\File::makeDirectory($folder, 0775, true); //creates directory
            }
            $fileName = "satisfaction_{$leads->id}_" . date("YmdHi") . ".pdf";
            $path = $folder . $fileName;
            $url = "pdf/" . $fileName;
            if (is_file($path)) {
                $publicUrl = $isPublic ? self::getPublicUrl($url) : $path;
            } else {
                $pdf->Output($path, 'F');
                $pdf->save($path);
                $publicUrl = $isPublic ? self::getPublicUrl($url) : $path;
            }
            return $publicUrl;
        } catch (\Exception $e) {
            \Log::error('Generate Customer Satisfaction error: ' . $e->getMessage());
            return false;
        }
    }
    public static function generatorWarrantyCardPDF($orderId, $isPublic = true) {
        $order = Order::with(['lead'])->find($orderId);
        $leads = $order->lead;
        $data = [
            "order_id" => $leads->id,
            "surname" => "",
            "give_name" => "",
            "phone" => "",
            "address" => "",
            "consultant" => "",
            "installer" => "",
            "installed_date" => $order->installed_date,
            "sold" => $leads->sold,
            "product" => ''
            ];
        if ($leads && $leads->client) {
            $data["give_name"] = $leads->client->given_name;
            $data["surname"] = $leads->client->surname;
            $data["phone"] = $leads->client->phone;
            $data["address"] = $leads->client->address;
            if ($order->installed_date) {
                $data["installed_date"] =  format_datetime($order->installed_date, 'd/m/Y');
            }
        }
        if ($order->install_id) {
            $installer = Account::withTrashed()->find($order->install_id);
            $data["installer"] = $installer->name;
        }
        if($leads->sales_consultant_id){
            $salesConsultant  = Account::withTrashed()->find($leads->sales_consultant_id);
            $data["consultant"] = $salesConsultant->name;
        }
        if ($order->product) {
            $data["product"] = $order->product->name;
        }
        if ($leads->product_category_id) {
            $category = ProductCategory::find($leads['product_category_id']);
        }

        $viewTem = 'garageDoorsWarrantyCard';
        switch($category->name){
            case "Landscaping":
                if($data["product"] == 'Turf'){
                $viewTem = 'turfWarrantyCard';
                } if($data["product"] == 'Retaining Wall'){
                $viewTem = 'retainingWallWarrantyCard';
                } else{
                $viewTem = 'pavingWarrantyCard';
                }
                break;
            case "Blinds":
                if($data["product"] == 'Outdoor Blinds'){
                $viewTem = 'outdoorBlindsWarrantyCard';
                }else if($data["product"] == 'Block Out Blinds'){
                $viewTem = 'blockOutBlindsWarrantyCard';
                }else if($data["product"] == 'Plantation Shutters'){
                $viewTem = 'plantationShuttersWarrantyCard';
                }else if($data["product"] == 'Roller Shutters'){
                $viewTem = 'rollerShuttersWarrantyCard';
                }
                break;
            case "Roof":
                $viewTem = 'roofWarrantyCard';
                break;
            case "Patio":
                $viewTem = 'patioWarrantyCard';
                break;
        }

        try {
            $pdf = App::make('snappy.pdf.wrapper')->setPaper('a4')->setOrientation('portrait');
            $pdf = $pdf->loadView('pdf.'.$viewTem, ['data' => $data]);
            $folder      = storage_path('app') . '/public/pdf/';
            if (!\Illuminate\Support\Facades\File::exists($folder)) {
                \Illuminate\Support\Facades\File::makeDirectory($folder, 0775, true); //creates directory
            }
            $fileName = "{$viewTem}_{$leads->id}_" . date("YmdHi") . ".pdf";
            $path = $folder . $fileName;
            $url = "pdf/" . $fileName;
            if (is_file($path)) {
                $publicUrl = $isPublic ? self::getPublicUrl($url) : $path;
            } else {
                $pdf->Output($path, 'F');
                $pdf->save($path);
                $publicUrl = $isPublic ? self::getPublicUrl($url) : $path;
            }
            return $publicUrl;
        } catch (\Exception $e) {
            \Log::error('Generate Generate Warranty Card error: ' . $e->getMessage());
            return false;
        }
    }

    public static function generatorPDF($orderId, $type, $amount, $dueDate, $isPublic = true) {
        $order = Order::with(['lead'])->find($orderId);
        $leads = $order->lead;
       
        $invoice = [
            "date" => date("d/m/Y"), // input date
            "amount" => '', // input amount:Current Payment Due
            "orderID" => '',
            "now" => date("d/m/Y"),
            "primaryName" => '',
            "secondaryName" => '',
            "primaryName" => '',
            "address" => ["beforeSuburb" => "", "afterSuburb" => ""],
            "code" => "",
            "quantity" => 0,
            "unit" => "pcs",
            "product" => "",
            "sold" => 0,
            "itemAmount" => 0,
            "gst" => 0,
            "payments" => [],
            "unPaid" => [],
            "paymentReceived" => 0,
            "totalBalanceDueOnCOmpletion" => "",
            "installDate" => "",
            "desc" => "includes material , installation charges and service"
            ];
        $invoice["date"] = $dueDate;
        $invoice["amount"] = $amount;
        $invoice["orderID"] = $leads->id;
        $invoice["now"] = date("d/m/Y");
        // client info and sold
        if ($leads && $leads->client) {
            $invoice["primaryName"] = $leads->client->given_name . " " . $leads->client->surname;
            $invoice["secondaryName"] = $leads->client->sec_given_name . " " . $leads->client->sec_surname;
            $invoice["address"] = clientAddressHandle($leads->client->address);
            $invoice["sold"] = $leads->sold;
            $invoice["itemAmount"] = round($leads->sold / 1.1, 2);
            $invoice["gst"] = round($leads->sold - $invoice["itemAmount"], 2);
        }
        if ($leads && $leads->sub_category_ids) {
            $sub_category_ids = explode(',', $leads->sub_category_ids);
            $products = Products::whereIn('id',  $sub_category_ids)->select('code', 'name')->get()->toArray();
            $invoice["code"] = implode(',', array_column($products, 'code'));
            $invoice["product"] = implode(',', array_column($products, 'name'));
        }
        $product_spec = $order->productSpec;
        if ($leads->category) {
            $qty = 0;
            switch ($leads->category->name) {
                case 'Landscaping':
                $invoice["unit"] = "sqm";
                if ($product_spec && isset($product_spec[0]) && $product_spec[0]["spec"]) {
                    $qty = $product_spec[0]["spec"]["Site area(m2)"] || 0;
                }
                break;
                case 'Roof':
                $invoice["unit"] = "sqm";
                if (strpos($invoice["code"], 'GT') !== false || strpos($invoice["code"], 'GG') !== false) {
                    $invoice["unit"] = "m";
                }
                if ($product_spec && isset($product_spec[0]) && $product_spec[0]["spec"]) {
                    $qty = $product_spec[0]["spec"]["RoofArea(m2)"] || 0;
                }
                break;
                default:
                $invoice["unit"] = "pcs";
                if ($product_spec) {
                    $qty = count($product_spec);
                }
            }
            $invoice["quantity"] = $qty;
        }
        // payment info
        if ($order->payments) {
            $invoice["payments"] = [];
            $invoice["unPaid"] = [];
            $paymentReceived = 0;
            foreach ($order->payments as $v) {
                if ($v->type == 'Refund') {
                $paymentReceived -= $v->amount;
                } else if ($v->type != 'Unpaid') {
                $paymentReceived += $v->amount;
                }
                if ($v->type != 'Unpaid') {
                $invoice["payments"][] = $v;
                }
                if ($v->type == 'Unpaid') {
                $invoice["unPaid"][] = $v;
                }
            }
            $invoice["paymentReceived"] = $paymentReceived;
            $invoice["totalBalanceDueOnCOmpletion"] = $invoice["sold"] - $paymentReceived;
        }

        if ($type == 'paid_invoice') {
            $lastAppt = Appointment::where("object_id", $leads->id)
            ->where('type', 'install')
            ->orderBy('id','desc')
            ->first();
            if (!empty($lastAppt)) {
                $invoice["installDate"] =  $lastAppt['date'];
            }
        }

        try {
            $pdf = App::make('snappy.pdf.wrapper')->setPaper('a4')->setOrientation('portrait');
            if ($type == 'due_invoice') {
                \Log::debug('here due invoice', $invoice);
                $pdf = $pdf->loadView('invoices.invoiceDue', ['invoice' => $invoice]);
            } else {
                $pdf = $pdf->loadView('invoices.invoicePaid', ['invoice' => $invoice]);
            }
            //  return $pdf->download();
            $folder      = storage_path('app') . '/public/pdf/';
            if (!\Illuminate\Support\Facades\File::exists($folder)) {
                \Illuminate\Support\Facades\File::makeDirectory($folder, 0775, true); //creates directory
            }
            $fileName = "{$type}_{$order->id}_" . date("YmdHi") . ".pdf";
            $path = $folder . $fileName;
            $url = "pdf/" . $fileName;
            if (is_file($path)) {
                $publicUrl = $isPublic ? self::getPublicUrl($url) : $path;
            } else {
                $pdf->Output($path, 'F');
                $pdf->save($path);
                $publicUrl = $isPublic ? self::getPublicUrl($url) : $path;
            }
            return $publicUrl;
        } catch (\Exception $e) {
            \Log::error('Generate invoice error: ' . $e->getMessage());
            return false;
        }
    }
    public static function getPublicUrl($path)
    {
        $url = url('storage/' . $path);
        if (env('URL_IS_HTTPS', false) && $url && strpos($url, 'https') == false) {
            $url = Str::replaceFirst("http://", 'https://', $url);
        }
        return $url;
    }
}

