<?php

namespace Tests\Modules\Admin\Order;

use Modules\Admin\Models\Role;
use Modules\Admin\Models\Account;
use Modules\Admin\Repositories\OrderRepository;
use Modules\Admin\Models\Order;
use Tests\TestCase;
use Illuminate\Foundation\Testing\DatabaseTransactions;
/**
 * TODO OrderSendPaymentEmailTest
 * POST /Admin/Order/sendPaymentEmail/{id}
 * Reference: https://laravel.com/docs/6.x/http-tests
 */
class OrderSendPaymentEmailTest extends TestCase
{
    use DatabaseTransactions;
    private $url;
    /**
    * @var OrderRepository
    */
    private $repository;
    public function setUp() : void
    {
        parent::setUp();
        $this->repository = $this->app->make(OrderRepository::class);
        $this->url = '/Admin/Order/sendPaymentEmail/{id}';
    }
    /**
     * TODO Not login shall NOT pass
     */
    public function testNotLoginShallNotPass()
    {
        $this->postJson($this->url)->assertStatus(401);
    }
    /**
     * TODO Super admin shall pass
     */
    public function testSuperAdminShallPass()
    {
        $userAccount = factory(Account::class)->state(Role::SUPER_ADMIN)->create();
        $request = $this->actingAs($userAccount, 'api');
        $response = $request->postJson($this->url);
        $response->assertStatus(200);
    }
    /**
     * TODO Platform admin
     */
    public function testPlatformAdmin()
    {
        $userAccount = factory(Account::class)->state('platform_admin')->create();
        $request = $this->actingAs($userAccount, 'api');
        $response = $request->postJson($this->url);
        $response->assertStatus(200);
    }
    /**
     * TODO Tenant admin
     */
    public function testTenantAdmin()
    {
        $userAccount = factory(Account::class)->state('tenant_admin')->create();
        $request = $this->actingAs($userAccount, 'api');
        $response = $request->postJson($this->url);
        $response->assertStatus(200);
    }
}