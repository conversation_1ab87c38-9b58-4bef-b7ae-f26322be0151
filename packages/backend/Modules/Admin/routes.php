<?php

Route::prefix('admin')->group(function () {
    Route::get('account/resetPasswordByEmail', ['as' => 'Admin.Account.resetPasswordByEmail', 'uses' => '\\Modules\\Admin\\Controllers\\AccountController@resetPasswordByEmail']);
    Route::post('auth/login', ['as'   => 'Admin.Auth.login',
                               'uses' => '\\Modules\\Admin\\Controllers\\AuthController@login']);
    Route::middleware('auth:api')->group(function () {
        Route::get('auth/me', ['as'   => 'Admin.Auth.me',
                               'uses' => '\\Modules\\Admin\\Controllers\\AuthController@me']);
        Route::post('auth/logout', ['as'   => 'Admin.Auth.logout',
                                    'uses' => '\\Modules\\Admin\\Controllers\\AuthController@logout']);
        Route::post('auth/refresh', ['as'   => 'Admin.Auth.refresh',
                                     'uses' => '\\Modules\\Admin\\Controllers\\AuthController@refresh']);
        Route::get('export/accounts', ['as'   => 'Admin.Export.accounts',
                                       'uses' => '\\Modules\\Admin\\Controllers\\ExportController@accounts']);
        Route::get('activity_log', ['as'   => 'Admin.ActivityLogs.index',
                                    'uses' => '\\Modules\\Admin\\Controllers\\ActivityLogsController@index']);
        Route::get('setting', ['as' => 'Admin.Setting.index', 'uses' => '\\Modules\\Admin\\Controllers\\SettingController@index']);
        Route::get('setting/{id}', ['as' => 'Admin.Setting.show', 'uses' => '\\Modules\\Admin\\Controllers\\SettingController@show']);
        Route::post('setting', ['as' => 'Admin.Setting.store', 'uses' => '\\Modules\\Admin\\Controllers\\SettingController@store']);
        Route::put('setting/{id}', ['as' => 'Admin.Setting.update', 'uses' => '\\Modules\\Admin\\Controllers\\SettingController@update']);
        Route::delete('setting/{id}', ['as' => 'Admin.Setting.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\SettingController@destroy']);
        Route::get('permission', ['as' => 'Admin.Permission.index', 'uses' => '\\Modules\\Admin\\Controllers\\PermissionController@index']);
        Route::get('permission/{id}', ['as' => 'Admin.Permission.show', 'uses' => '\\Modules\\Admin\\Controllers\\PermissionController@show']);
        Route::post('permission', ['as' => 'Admin.Permission.store', 'uses' => '\\Modules\\Admin\\Controllers\\PermissionController@store']);
        Route::put('permission/{id}', ['as' => 'Admin.Permission.update', 'uses' => '\\Modules\\Admin\\Controllers\\PermissionController@update']);
        Route::delete('permission/{id}', ['as' => 'Admin.Permission.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\PermissionController@destroy']);
        Route::get('upload', ['as'   => 'Admin.Upload.index',
                              'uses' => '\\Modules\\Admin\\Controllers\\UploadController@index']);
        Route::post('upload', ['as'   => 'Admin.Upload.store',
                               'uses' => '\\Modules\\Admin\\Controllers\\UploadController@store']);
        Route::get('upload/{id}', ['as'   => 'Admin.Upload.show',
                                   'uses' => '\\Modules\\Admin\\Controllers\\UploadController@show']);
        Route::put('upload/{id}', ['as'   => 'Admin.Upload.update',
                                   'uses' => '\\Modules\\Admin\\Controllers\\UploadController@update']);
        Route::delete('upload/{id}', ['as'   => 'Admin.Upload.destroy',
                                      'uses' => '\\Modules\\Admin\\Controllers\\UploadController@destroy']);
        Route::get('news_section', ['as' => 'Admin.NewsSection.index', 'uses' => '\\Modules\\Admin\\Controllers\\NewsSectionController@index']);
        Route::get('news_section/{id}', ['as' => 'Admin.NewsSection.show', 'uses' => '\\Modules\\Admin\\Controllers\\NewsSectionController@show']);
        Route::post('news_section', ['as' => 'Admin.NewsSection.store', 'uses' => '\\Modules\\Admin\\Controllers\\NewsSectionController@store']);
        Route::put('news_section/{id}', ['as' => 'Admin.NewsSection.update', 'uses' => '\\Modules\\Admin\\Controllers\\NewsSectionController@update']);
        Route::delete('news_section/{id}', ['as' => 'Admin.NewsSection.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\NewsSectionController@destroy']);
        Route::get('role', ['as' => 'Admin.Role.index', 'uses' => '\\Modules\\Admin\\Controllers\\RoleController@index']);
        Route::get('role/{id}', ['as' => 'Admin.Role.show', 'uses' => '\\Modules\\Admin\\Controllers\\RoleController@show']);
        Route::post('role', ['as' => 'Admin.Role.store', 'uses' => '\\Modules\\Admin\\Controllers\\RoleController@store']);
        Route::put('role/{id}', ['as' => 'Admin.Role.update', 'uses' => '\\Modules\\Admin\\Controllers\\RoleController@update']);
        Route::delete('role/{id}', ['as' => 'Admin.Role.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\RoleController@destroy']);
        Route::get('account', ['as' => 'Admin.Account.index', 'uses' => '\\Modules\\Admin\\Controllers\\AccountController@index']);
        Route::get('account/{id}', ['as' => 'Admin.Account.show', 'uses' => '\\Modules\\Admin\\Controllers\\AccountController@show']);
        Route::post('account', ['as' => 'Admin.Account.store', 'uses' => '\\Modules\\Admin\\Controllers\\AccountController@store']);
        Route::put('account/{id}', ['as' => 'Admin.Account.update', 'uses' => '\\Modules\\Admin\\Controllers\\AccountController@update']);
        Route::delete('account/{id}', ['as' => 'Admin.Account.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\AccountController@destroy']);
        Route::get('clients', ['as' => 'Admin.Clients.index', 'uses' => '\\Modules\\Admin\\Controllers\\ClientsController@index']);
        Route::get('clients/{id}', ['as' => 'Admin.Clients.show', 'uses' => '\\Modules\\Admin\\Controllers\\ClientsController@show']);
        Route::post('clients', ['as' => 'Admin.Clients.store', 'uses' => '\\Modules\\Admin\\Controllers\\ClientsController@store']);
        Route::put('clients/{id}', ['as' => 'Admin.Clients.update', 'uses' => '\\Modules\\Admin\\Controllers\\ClientsController@update']);
        Route::delete('clients/{id}', ['as' => 'Admin.Clients.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\ClientsController@destroy']);
        Route::get('source', ['as' => 'Admin.Source.index', 'uses' => '\\Modules\\Admin\\Controllers\\SourceController@index']);
        Route::get('source/{id}', ['as' => 'Admin.Source.show', 'uses' => '\\Modules\\Admin\\Controllers\\SourceController@show']);
        Route::post('source', ['as' => 'Admin.Source.store', 'uses' => '\\Modules\\Admin\\Controllers\\SourceController@store']);
        Route::put('source/{id}', ['as' => 'Admin.Source.update', 'uses' => '\\Modules\\Admin\\Controllers\\SourceController@update']);
        Route::delete('source/{id}', ['as' => 'Admin.Source.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\SourceController@destroy']);
        Route::get('promotion', ['as' => 'Admin.Promotion.index', 'uses' => '\\Modules\\Admin\\Controllers\\PromotionController@index']);
        Route::get('promotion/{id}', ['as' => 'Admin.Promotion.show', 'uses' => '\\Modules\\Admin\\Controllers\\PromotionController@show']);
        Route::post('promotion', ['as' => 'Admin.Promotion.store', 'uses' => '\\Modules\\Admin\\Controllers\\PromotionController@store']);
        Route::put('promotion/{id}', ['as' => 'Admin.Promotion.update', 'uses' => '\\Modules\\Admin\\Controllers\\PromotionController@update']);
        Route::delete('promotion/{id}', ['as' => 'Admin.Promotion.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\PromotionController@destroy']);
        Route::get('product_category', ['as' => 'Admin.ProductCategory.index', 'uses' => '\\Modules\\Admin\\Controllers\\ProductCategoryController@index']);
        Route::get('product_category/{id}', ['as' => 'Admin.ProductCategory.show', 'uses' => '\\Modules\\Admin\\Controllers\\ProductCategoryController@show']);
        Route::post('product_category', ['as' => 'Admin.ProductCategory.store', 'uses' => '\\Modules\\Admin\\Controllers\\ProductCategoryController@store']);
        Route::put('product_category/{id}', ['as' => 'Admin.ProductCategory.update', 'uses' => '\\Modules\\Admin\\Controllers\\ProductCategoryController@update']);
        Route::delete('product_category/{id}', ['as' => 'Admin.ProductCategory.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\ProductCategoryController@destroy']);
        Route::get('products', ['as' => 'Admin.Products.index', 'uses' => '\\Modules\\Admin\\Controllers\\ProductsController@index']);
        Route::get('products/{id}', ['as' => 'Admin.Products.show', 'uses' => '\\Modules\\Admin\\Controllers\\ProductsController@show']);
        Route::post('products', ['as' => 'Admin.Products.store', 'uses' => '\\Modules\\Admin\\Controllers\\ProductsController@store']);
        Route::put('products/{id}', ['as' => 'Admin.Products.update', 'uses' => '\\Modules\\Admin\\Controllers\\ProductsController@update']);
        Route::delete('products/{id}', ['as' => 'Admin.Products.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\ProductsController@destroy']);
        Route::get('draft_leads', ['as' => 'Admin.DraftLeads.index', 'uses' => '\\Modules\\Admin\\Controllers\\DraftLeadsController@index']);
        Route::get('draft_leads/{id}', ['as' => 'Admin.DraftLeads.show', 'uses' => '\\Modules\\Admin\\Controllers\\DraftLeadsController@show']);
        Route::post('draft_leads', ['as' => 'Admin.DraftLeads.store', 'uses' => '\\Modules\\Admin\\Controllers\\DraftLeadsController@store']);
        Route::put('draft_leads/{id}', ['as' => 'Admin.DraftLeads.update', 'uses' => '\\Modules\\Admin\\Controllers\\DraftLeadsController@update']);
        Route::delete('draft_leads/{id}', ['as' => 'Admin.DraftLeads.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\DraftLeadsController@destroy']);
        Route::put('draft_leads/cancel/{id}', ['as' => 'Admin.DraftLeads.cancel', 'uses' => '\\Modules\\Admin\\Controllers\\DraftLeadsController@cancel']);
        Route::put('draft_leads/setspoken/{id}', ['as' => 'Admin.DraftLeads.setspoken', 'uses' => '\\Modules\\Admin\\Controllers\\DraftLeadsController@setspoken']);
        Route::post('notes', ['as' => 'Admin.Notes.store', 'uses' => '\\Modules\\Admin\\Controllers\\NotesController@store']);
        Route::put('draft_leads/confirm/{id}', ['as' => 'Admin.DraftLeads.confirm', 'uses' => '\\Modules\\Admin\\Controllers\\DraftLeadsController@confirm']);
        Route::get('leads', ['as' => 'Admin.Leads.index', 'uses' => '\\Modules\\Admin\\Controllers\\LeadsController@index']);
        Route::get('leads/{id}', ['as' => 'Admin.Leads.show', 'uses' => '\\Modules\\Admin\\Controllers\\LeadsController@show']);
        Route::put('leads/{id}', ['as' => 'Admin.Leads.update', 'uses' => '\\Modules\\Admin\\Controllers\\LeadsController@update']);
        Route::get('supplier', ['as' => 'Admin.Supplier.index', 'uses' => '\\Modules\\Admin\\Controllers\\SupplierController@index']);
        Route::get('supplier/{id}', ['as' => 'Admin.Supplier.show', 'uses' => '\\Modules\\Admin\\Controllers\\SupplierController@show']);
        Route::post('supplier', ['as' => 'Admin.Supplier.store', 'uses' => '\\Modules\\Admin\\Controllers\\SupplierController@store']);
        Route::put('supplier/{id}', ['as' => 'Admin.Supplier.update', 'uses' => '\\Modules\\Admin\\Controllers\\SupplierController@update']);
        Route::delete('supplier/{id}', ['as' => 'Admin.Supplier.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\SupplierController@destroy']);
        Route::put('leads/changeResult/{id}', ['as' => 'Admin.Leads.changeResult', 'uses' => '\\Modules\\Admin\\Controllers\\LeadsController@changeResult']);
        Route::get('appointment', ['as' => 'Admin.Appointment.index', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@index']);
        Route::get('appointment/{id}', ['as' => 'Admin.Appointment.show', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@show']);
        Route::post('appointment', ['as' => 'Admin.Appointment.store', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@store']);
        Route::put('appointment/{id}', ['as' => 'Admin.Appointment.update', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@update']);
        Route::delete('appointment/{id}', ['as' => 'Admin.Appointment.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@destroy']);
        Route::put('appointment/assign/{id}', ['as' => 'Admin.Appointment.assign', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@assign']);
        Route::put('account/resetPassword/{id}', ['as' => 'Admin.Account.resetPassword', 'uses' => '\\Modules\\Admin\\Controllers\\AccountController@resetPassword']);
        Route::get('order', ['as' => 'Admin.Order.index', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@index']);
        Route::get('order/{id}', ['as' => 'Admin.Order.show', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@show']);
        Route::put('order/{id}', ['as' => 'Admin.Order.update', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@update']);
        Route::delete('order/{id}', ['as' => 'Admin.Order.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@destroy']);
        Route::put('leads/convert/{id}', ['as' => 'Admin.Leads.convert', 'uses' => '\\Modules\\Admin\\Controllers\\LeadsController@convert']);
        Route::put('order/add_payment/{id}', ['as' => 'Admin.Order.addPayment', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@addPayment']);
        Route::put('order/change_result/{id}', ['as' => 'Admin.Order.changeResult', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@changeResult']);
        Route::put('appointment/notify_client/{id}', ['as' => 'Admin.Appointment.notifyClient', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@notifyClient']);
        Route::post('order/save_spec', ['as' => 'Admin.Order.saveSpec', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@saveSpec']);
        Route::put('appointment/cancel/{id}', ['as' => 'Admin.Appointment.cancel', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@cancel']);
        Route::put('appointment/complete/{id}', ['as' => 'Admin.Appointment.complete', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@complete']);
        Route::put('order/add_supplier/{id}', ['as' => 'Admin.Order.addSupplier', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@addSupplier']);
        Route::put('order/remove_supplier/{id}', ['as' => 'Admin.Order.removeSupplier', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@removeSupplier']);
        Route::put('order/create_service/{id}', ['as' => 'Admin.Order.createService', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@createService']);
        Route::get('service', ['as' => 'Admin.Service.index', 'uses' => '\\Modules\\Admin\\Controllers\\ServiceController@index']);
        Route::get('service/{id}', ['as' => 'Admin.Service.show', 'uses' => '\\Modules\\Admin\\Controllers\\ServiceController@show']);
        Route::post('service', ['as' => 'Admin.Service.store', 'uses' => '\\Modules\\Admin\\Controllers\\ServiceController@store']);
        Route::put('service/{id}', ['as' => 'Admin.Service.update', 'uses' => '\\Modules\\Admin\\Controllers\\ServiceController@update']);
        Route::get('service/get_summary/info', ['as' => 'Admin.Service.getSummaryInfo', 'uses' => '\\Modules\\Admin\\Controllers\\ServiceController@getSummaryInfo']);
        Route::put('service/change_result/{id}', ['as' => 'Admin.Service.changeResult', 'uses' => '\\Modules\\Admin\\Controllers\\ServiceController@changeResult']);
        Route::get('dashboard/summary', ['as' => 'Admin.Dashboard.summary', 'uses' => '\\Modules\\Admin\\Controllers\\DashboardController@summary']);
        Route::get('dashboard/getNewLeadStatistic', ['as' => 'Admin.Dashboard.getNewLeadStatistic', 'uses' => '\\Modules\\Admin\\Controllers\\DashboardController@getNewLeadStatistic']);
        Route::get('dashboard/getAppointmentStatistic', ['as' => 'Admin.Dashboard.getAppointmentStatistic', 'uses' => '\\Modules\\Admin\\Controllers\\DashboardController@getAppointmentStatistic']);
        Route::post('appointment/sendToGoogleCalendar', ['as' => 'Admin.Appointment.sendToGoogleCalendar', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@sendToGoogleCalendar']);
        Route::get('productCategory/exportBcolor', ['as' => 'Admin.ProductCategory.exportBcolor', 'uses' => '\\Modules\\Admin\\Controllers\\ProductCategoryController@exportBcolor']);
        Route::post('service/saveSpec', ['as' => 'Admin.Service.saveSpec', 'uses' => '\\Modules\\Admin\\Controllers\\ServiceController@saveSpec']);
        Route::post('productCategory/importBcolorFromExcel', ['as' => 'Admin.ProductCategory.importBcolorFromExcel', 'uses' => '\\Modules\\Admin\\Controllers\\ProductCategoryController@importBcolorFromExcel']);
        Route::get('report', ['as' => 'Admin.Report.index', 'uses' => '\\Modules\\Admin\\Controllers\\ReportController@index']);
        Route::get('report/{id}', ['as' => 'Admin.Report.show', 'uses' => '\\Modules\\Admin\\Controllers\\ReportController@show']);
        Route::post('report', ['as' => 'Admin.Report.store', 'uses' => '\\Modules\\Admin\\Controllers\\ReportController@store']);
        Route::put('report/{id}', ['as' => 'Admin.Report.update', 'uses' => '\\Modules\\Admin\\Controllers\\ReportController@update']);
        Route::delete('report/{id}', ['as' => 'Admin.Report.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\ReportController@destroy']);
        Route::put('appointment/notifyAssign/{id}', ['as' => 'Admin.Appointment.notifyAssign', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@notifyAssign']);
        Route::put('appointment/assignAccept/{id}', ['as' => 'Admin.Appointment.assignAccept', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@assignAccept']);
        Route::put('appointment/confirm/{id}', ['as' => 'Admin.Appointment.confirm', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@confirm']);
        Route::delete('order/del_payment/{id}', ['as' => 'Admin.Order.delPayment', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@delPayment']);
        Route::post('order/generatorPDF/{id}', ['as' => 'Admin.Order.generatorPDF', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@generatorPDF']);
        Route::post('service/generatorPDF/{id}', ['as' => 'Admin.Service.generatorPDF', 'uses' => '\\Modules\\Admin\\Controllers\\ServiceController@generatorPDF']);
        Route::get('dashboard/getLeadStatisticByTimeRange', ['as' => 'Admin.Dashboard.getLeadStatisticByTimeRange', 'uses' => '\\Modules\\Admin\\Controllers\\DashboardController@getLeadStatisticByTimeRange']);
        Route::get('dashboard/getSalesAmountStatisticBySoldDateRange', ['as' => 'Admin.Dashboard.getSalesAmountStatisticBySoldDateRange', 'uses' => '\\Modules\\Admin\\Controllers\\DashboardController@getSalesAmountStatisticBySoldDateRange']);
        Route::post('order/generatorSatisfactionPDF/{id}', ['as' => 'Admin.Order.generatorSatisfactionPDF', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@generatorSatisfactionPDF']);
        Route::post('order/generatorWarrantyCardPDF/{id}', ['as' => 'Admin.Order.generatorWarrantyCardPDF', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@generatorWarrantyCardPDF']);
        Route::put('leads/changeSalesConsultant/{id}', ['as' => 'Admin.Leads.changeSalesConsultant', 'uses' => '\\Modules\\Admin\\Controllers\\LeadsController@changeSalesConsultant']);
        Route::post('productCategory/dataSynchronizationFromOld', ['as' => 'Admin.ProductCategory.dataSynchronizationFromOld', 'uses' => '\\Modules\\Admin\\Controllers\\ProductCategoryController@dataSynchronizationFromOld']);
        Route::get('import_tasks', ['as' => 'Admin.ImportTasks.index', 'uses' => '\\Modules\\Admin\\Controllers\\ImportTasksController@index']);
        Route::get('import_tasks/{id}', ['as' => 'Admin.ImportTasks.show', 'uses' => '\\Modules\\Admin\\Controllers\\ImportTasksController@show']);
        Route::post('import_tasks', ['as' => 'Admin.ImportTasks.store', 'uses' => '\\Modules\\Admin\\Controllers\\ImportTasksController@store']);
        Route::put('import_tasks/{id}', ['as' => 'Admin.ImportTasks.update', 'uses' => '\\Modules\\Admin\\Controllers\\ImportTasksController@update']);
        Route::delete('import_tasks/{id}', ['as' => 'Admin.ImportTasks.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\ImportTasksController@destroy']);
        Route::get('productCategory/dataSynchronizationToOld', ['as' => 'Admin.ProductCategory.dataSynchronizationToOld', 'uses' => '\\Modules\\Admin\\Controllers\\ProductCategoryController@dataSynchronizationToOld']);
        Route::post('Admin/order/generatorReport', ['as' => 'Admin.Order.generatorReport', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@generatorReport']);
        Route::post('Admin/Order/UpdatePayment', ['as' => 'Admin.Order.updatePayment', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@updatePayment']);
        Route::post('Admin/Leads/MoveToBlacklist/{id}', ['as' => 'Admin.Leads.moveToBlacklist', 'uses' => '\\Modules\\Admin\\Controllers\\LeadsController@moveToBlacklist']);
        Route::post('Admin/Clients/RemoveFromBlacklist/{id}', ['as' => 'Admin.Clients.removeFromBlacklist', 'uses' => '\\Modules\\Admin\\Controllers\\ClientsController@removeFromBlacklist']);
        Route::get('cost', ['as' => 'Admin.Cost.index', 'uses' => '\\Modules\\Admin\\Controllers\\CostController@index']);
        Route::get('cost/{id}', ['as' => 'Admin.Cost.show', 'uses' => '\\Modules\\Admin\\Controllers\\CostController@show']);
        Route::post('cost', ['as' => 'Admin.Cost.store', 'uses' => '\\Modules\\Admin\\Controllers\\CostController@store']);
        Route::put('cost/{id}', ['as' => 'Admin.Cost.update', 'uses' => '\\Modules\\Admin\\Controllers\\CostController@update']);
        Route::delete('cost/{id}', ['as' => 'Admin.Cost.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\CostController@destroy']);
        Route::post('Admin/Leads/RevokedLeads/{id}', ['as' => 'Admin.Leads.revokedLeads', 'uses' => '\\Modules\\Admin\\Controllers\\LeadsController@revokedLeads']);
        Route::get('Admin/Order/revokeOrder/{id}', ['as' => 'Admin.Order.revokeOrder', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@revokeOrder']);
        Route::post('Admin/Appointment/RevokeCancel/{id}', ['as' => 'Admin.Appointment.revokeCancel', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@revokeCancel']);
        Route::get('Admin/Cost/syncReport', ['as' => 'Admin.Cost.syncReport', 'uses' => '\\Modules\\Admin\\Controllers\\CostController@syncReport']);
        Route::get('Admin/Appointment/syncCalendar/{id}', ['as' => 'Admin.Appointment.syncCalendar', 'uses' => '\\Modules\\Admin\\Controllers\\AppointmentController@syncCalendar']);
        Route::get('target', ['as' => 'Admin.Target.index', 'uses' => '\\Modules\\Admin\\Controllers\\TargetController@index']);
        Route::get('target/{id}', ['as' => 'Admin.Target.show', 'uses' => '\\Modules\\Admin\\Controllers\\TargetController@show']);
        Route::post('target', ['as' => 'Admin.Target.store', 'uses' => '\\Modules\\Admin\\Controllers\\TargetController@store']);
        Route::put('target/{id}', ['as' => 'Admin.Target.update', 'uses' => '\\Modules\\Admin\\Controllers\\TargetController@update']);
        Route::delete('target/{id}', ['as' => 'Admin.Target.destroy', 'uses' => '\\Modules\\Admin\\Controllers\\TargetController@destroy']);
        Route::post('Admin/Target/CopyTarget', ['as' => 'Admin.Target.copyTarget', 'uses' => '\\Modules\\Admin\\Controllers\\TargetController@copyTarget']);
        Route::post('Admin/Target/updateDate', ['as' => 'Admin.Target.updateDate', 'uses' => '\\Modules\\Admin\\Controllers\\TargetController@updateDate']);
        Route::get('Admin/Dashboard/Target', ['as' => 'Admin.Dashboard.target', 'uses' => '\\Modules\\Admin\\Controllers\\DashboardController@target']);
        Route::post('Admin/Order/EmailToClient/{id}', ['as' => 'Admin.Order.emailToClient', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@emailToClient']);
        Route::post('Admin/Order/sendPaymentEmail/{id}', ['as' => 'Admin.Order.sendPaymentEmail', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@sendPaymentEmail']);

    });
    Route::get('common/basic_data', ['as' => 'Admin.Common.basicData', 'uses' => '\\Modules\\Admin\\Controllers\\CommonController@basicData']);
    Route::get('notes/list/{type}/{id}', ['as' => 'Admin.Notes.list', 'uses' => '\\Modules\\Admin\\Controllers\\NotesController@list']);
    Route::post('account/changePassword', ['as' => 'Admin.Account.changePassword', 'uses' => '\\Modules\\Admin\\Controllers\\AccountController@changePassword']);
    Route::get('order/get_summary/info', ['as' => 'Admin.Order.getSummaryInfo', 'uses' => '\\Modules\\Admin\\Controllers\\OrderController@getSummaryInfo']);
    Route::get('leads/get_summary/info', ['as' => 'Admin.Leads.getSummaryInfo', 'uses' => '\\Modules\\Admin\\Controllers\\LeadsController@getSummaryInfo']);
    Route::post('google/saveGoogleAccession', ['as' => 'Admin.Google.saveGoogleAccession', 'uses' => '\\Modules\\Admin\\Controllers\\GoogleController@saveGoogleAccession']);
    Route::match(['get', 'post'], 'draft_leads/ad/webhooks', ['as' => 'Admin.DraftLeads.webhooks', 'uses' => '\\Modules\\Admin\\Controllers\\DraftLeadsController@webhooks']);
});
