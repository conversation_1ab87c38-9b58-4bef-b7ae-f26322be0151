<?php

namespace Modules\Admin\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;
/**
 * @OA\RequestBody(
 *     request="Admin.OrderEmailToClientRequestBody",
 *     description="Payload to method `OrderController::emailToClient`", required=true,
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/Admin.OrderEmailToClientRequestSchema")
 *     ),
 * ),
 *
 * @OA\Schema(
 *     schema="Admin.OrderEmailToClientRequestSchema",
 *     title="Admin.OrderEmailToClientRequestSchema",
 *     description="Schema to method method `OrderController::emailToClient`",
 *     type="object",
 *     @OA\Property(property="type", type="string", description="Email type"),
 * ),
 */
class OrderEmailToClientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [];
        return $rules;
    }
    public function attributes()
    {
        return __('Admin/Order.fields');
    }
}