<?php

namespace Modules\Admin\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;
/**
 * @OA\RequestBody(
 *     request="Admin.OrderSendPaymentEmailRequestBody",
 *     description="Payload to method `OrderController::sendPaymentEmail`", required=true,
 *     @OA\MediaType(
 *         mediaType="application/json",
 *         @OA\Schema(ref="#/components/schemas/Admin.OrderSendPaymentEmailRequestSchema")
 *     ),
 * ),
 *
 * @OA\Schema(
 *     schema="Admin.OrderSendPaymentEmailRequestSchema",
 *     title="Admin.OrderSendPaymentEmailRequestSchema",
 *     description="Schema to method method `OrderController::sendPaymentEmail`",
 *     type="object",
 *     @OA\Property(property="foo", type="string", description="bar"),
 * ),
 */
class OrderSendPaymentEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [];
        return $rules;
    }
    public function attributes()
    {
        return __('Admin/Order.fields');
    }
}