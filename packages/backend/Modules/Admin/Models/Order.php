<?php

namespace Modules\Admin\Models;

use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Model;
/**
* @OA\Schema(
*     schema="Admin.Order",
*     title="Admin.Order",
*     description="Order model of Admin module.",
*     type="object",
*     @OA\Property(property="id", ref="#/components/schemas/id"),
*     @OA\Property(property="lead_id", type="integer", description="The lead id of a order."),
*     @OA\Property(property="received_date", type="string", description="The received date of a order."),
*     @OA\Property(property="result", type="string", description="The result of a order."),
*     @OA\Property(property="under_sell", type="integer", description="The under sell of a order."),
*     @OA\Property(property="estimated_comm", type="integer", description="The under sell of a order."),
*     @OA\Property(property="start_date", type="string", description="The start date of a order."),
*     @OA\Property(property="product_staff_id", type="integer", description="The product staff of a order."),
*     @OA\Property(property="cm_id", type="integer", description="The cm of a order."),
*     @OA\Property(property="cm_booked_date", type="string", description="The cm booked date of a order."),
*     @OA\Property(property="cm_received_date", type="string", description="The cm received date of a order."),
*     @OA\Property(property="on_product_date", type="string", description="The on product date of a order."),
*     @OA\Property(property="ready_date", type="string", description="The ready date of a order."),
*     @OA\Property(property="install_id", type="integer", description="The install of a order."),
*     @OA\Property(property="installation_date", type="string", description="The installation date of a order."),
*     @OA\Property(property="completion_date", type="string", description="The completion date of a order."),
*     @OA\Property(property="reason", type="string", description="The reason of a order."),
*     @OA\Property(property="finance_company", type="string", description="The finance company of a order."),
*     @OA\Property(property="finance_type", type="string", description="The sub result of a order."),
*     @OA\Property(property="finance_amount", type="integer", description="The sub result of a order."),
*     @OA\Property(property="finance_approved_date", type="string", description="The finance approved date of a order."),
*     @OA\Property(property="finance_approved_no", type="string", description="The finance approved no of a order."),
*     @OA\Property(property="electrician", type="integer", description="If the order need electrician."),
*     @OA\Property(property="created_at", ref="#/components/schemas/created_at"),
*     @OA\Property(property="updated_at", ref="#/components/schemas/updated_at"),
*     @OA\Property(property="deleted_at", ref="#/components/schemas/deleted_at"),
* )
* @property integer $id
* @property integer $lead_id
* @property string $received_date
* @property string $result
* @property double $under_sell
* @property double $estimated_comm
* @property string $start_date
* @property integer $product_staff_id
* @property integer $cm_id
* @property string $cm_booked_date
* @property string $cm_received_date
* @property string $on_product_date
* @property string $ready_date
* @property integer $install_id
* @property string $installation_date
* @property string $completion_date
* @property string $reason
* @property string $finance_company
* @property string $finance_type
* @property double $finance_amount
* @property string $finance_approved_date
* @property string $finance_approved_no
* @property string $created_at
* @property string $updated_at
* @property string $deleted_at
* @property integer $electrician
*/
class Order extends BaseModel
{
    use SoftDeletes;
    const RESULT_NEW = 'new_order';
    const RESULT_CM = 'cm';
    const RESULT_CM_RECEIVED = 'cmReceived';
    const RESULT_ON_PRODUCTION = 'onProduction';
    const RESULT_READY = 'ready';
    const RESULT_INSTALLATION = 'installation';
    const RESULT_EXTRA = 'extra';
    const RESULT_OUTSTANDING = 'outstanding';
    const RESULT_COMPLETION = 'completion';
    const RESULT_HOLD = 'hold';
    const RESULT_CANCELLED = 'cancelled';
    const RESULT_IN_COUNCIL = 'inCouncil';

    public $table = 'order';
    public $fillable = ['id','qty','product_id','installed_date','hold_time','lead_id', 'received_date', 'result', 'under_sell', 'estimated_comm', 'start_date', 'product_staff_id', 'cm_id', 'cm_booked_date', 'cm_received_date', 'on_product_date', 'ready_date', 'install_id', 'installation_date', 'completion_date', 'reason', 'finance_company', 'finance_type', 'finance_amount', 'finance_approved_date', 'finance_approved_no','is_stagnation','last_result','followup_date', 'to_be_collect','transno','from_old', 'electrician', 'submit_date', 'approved_date', 'is_ready', 'company_region', 'decision', 'application_id'];
    public $searchable = ['product','product.name','followup_date','received_date', 'created_at', 'result','lead_id','transno','from_old', 'company_region'];
    protected $casts = ['id' => 'integer', 'followup_date' => 'datetime', 'lead_id' => 'integer', 'received_date' => 'datetime', 'result' => 'string', 'under_sell' => 'double', 'estimated_comm' => 'double', 'start_date' => 'datetime', 'product_staff_id' => 'integer', 'cm_id' => 'integer', 'cm_booked_date' => 'datetime', 'cm_received_date' => 'datetime', 'on_product_date' => 'datetime', 'ready_date' => 'datetime', 'install_id' => 'integer', 'installation_date' => 'datetime', 'completion_date' => 'datetime', 'reason' => 'string', 'finance_company' => 'string', 'finance_type' => 'string', 'finance_amount' => 'double', 'finance_approved_date' => 'datetime', 'finance_approved_no' => 'string', 'electrician' => 'integer', 'submit_date' => 'datetime', 'approved_date' => 'datetime', 'is_ready' => 'integer', 'company_region' => 'string'];
    protected $dates = ['followup_date', 'received_date', 'start_date', 'cm_booked_date', 'cm_received_date', 'on_product_date', 'ready_date', 'installation_date', 'completion_date', 'finance_approved_date', 'deleted_at','hold_time','installed_date', 'submit_date', 'approved_date'];
    public static $rules = ['lead_id' => [], 'received_date' => [], 'result' => [], 'under_sell' => [], 'estimated_comm' => ['nullable'], 'start_date' => ['nullable'], 'product_staff_id' => ['nullable'], 'cm_id' => ['nullable'], 'cm_booked_date' => ['nullable'], 'cm_received_date' => ['nullable'], 'followup_date' => ['nullable'], 'on_product_date' => ['nullable'], 'ready_date' => ['nullable'], 'install_id' => ['nullable'], 'installation_date' => ['nullable'], 'completion_date' => ['nullable'], 'reason' => ['nullable'], 'finance_company' => [], 'finance_type' => [], 'finance_amount' => [], 'finance_approved_date' => [], 'finance_approved_no' => []];
    public function payments()
    {
        return $this->hasMany('Modules\\Admin\\Models\\Payment', 'order_id', 'id')->where('payment_type', Payment::TYPE_ORDER);
    }
    public function cm()
    {
        return $this->hasOne('Modules\\Admin\\Models\\Account', 'id', 'cm_id');
    }
    public function lead()
    {
        return $this->hasOne('Modules\\Admin\\Models\\Leads', 'id', 'lead_id');
    }
    public function install()
    {
        return $this->hasOne('Modules\\Admin\\Models\\Account', 'id', 'install_id');
    }
    public function productStaff()
    {
        return $this->hasOne('Modules\\Admin\\Models\\Account', 'id', 'product_staff_id');
    }
    public function productSpec()
    {
        return $this->hasMany('Modules\\Admin\\Models\\OrderProductSpec', 'order_id', 'id')->where('spec_type', OrderProductSpec::TYPE_ORDER);
    }
    public function appointment()
    {
        return $this->hasMany('Modules\\Admin\\Models\\Appointment', 'object_id', 'lead_id')->whereIn('type', ['cm', 'install'])->whereNotIn('status', [Appointment::STATUS_COMPLETED, Appointment::STATUS_CANCEL])->whereNull('service_id');
    }
    public function supplierList()
    {
        return $this->hasMany('Modules\\Admin\\Models\\OrderSupplier', 'order_id', 'id')->where('type', 'order');
    }
    public function contracts()
    {
        return $this->hasMany('Modules\\Admin\\Models\\Upload', 'object_id', 'lead_id')->whereIn('type', ['contract']);
    }
    public function productFiles()
    {
        return $this->hasMany('Modules\\Admin\\Models\\Upload', 'object_id', 'lead_id')->whereIn('type', ['cm', 'install']);
    }
    public function invoice()
    {
        return $this->hasMany('Modules\\Admin\\Models\\Upload', 'object_id', 'lead_id')->where('type', 'invoice');
    }
    public function product(){
        return $this->hasOne('Modules\\Admin\\Models\\Products', 'id', 'product_id');
    }
    public function notes()
    {
        return $this->hasMany('Modules\\Admin\\Models\\Notes', 'order_id', 'id');
    }
    public function service()
    {
        return $this->hasMany('Modules\\Admin\\Models\\Service', 'order_id', 'id');
    }
    public function trade()
    {
        return $this->hasMany('Modules\\Admin\\Models\\Upload', 'object_id', 'lead_id')->where('type', 'trade');
    }
}