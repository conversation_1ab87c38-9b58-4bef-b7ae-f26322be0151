<?php

namespace Modules\Admin\Controllers;

use Modules\Admin\Requests\Order\OrderRevokeOrderRequest;
use DateTime;
use Modules\Admin\Requests\Order\OrderGeneratorReportRequest;
use Modules\Admin\Requests\Order\OrderGeneratorWarrantyCardPDFRequest;
use Modules\Admin\Requests\Order\OrderGeneratorSatisfactionPDFRequest;
use Modules\Admin\Requests\Order\OrderGeneratorPDFRequest;
use Modules\Admin\Requests\Order\OrderDelPaymentRequest;
use Modules\Admin\Requests\Order\OrderCreateServiceRequest;
use Modules\Admin\Requests\Order\OrderRemoveSupplierRequest;
use Modules\Admin\Requests\Order\OrderAddSupplierRequest;
use Hamcrest\Type\IsString;
use Modules\Admin\Requests\Order\OrderSaveSpecRequest;
use Modules\Admin\Requests\Order\OrderGetSummaryInfoRequest;
use Modules\Admin\Requests\Order\OrderChangeResultRequest;
use Modules\Admin\Requests\Order\OrderAddPaymentRequest;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Scaffold\QueryBuilder\QueryBuilder;
use Scaffold\BaseController;
use Modules\Admin\Requests\Order\OrderUpdateRequest;
use Modules\Admin\Requests\Order\OrderListRequest;
use Modules\Admin\Requests\Order\OrderGetRequest;
use Modules\Admin\Requests\Order\OrderDeleteRequest;
use Modules\Admin\Repositories\OrderRepository;
use Modules\Admin\Resources\OrderResource;
use Modules\Admin\Resources\OrderCollection;
use Modules\Admin\Models\Order;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Modules\Admin\Models\Appointment;
use Modules\Admin\Models\Leads;
use Modules\Admin\Models\OrderProductSpec;
use Modules\Admin\Models\OrderSupplier;
use Modules\Admin\Models\Payment;
use Modules\Admin\Models\Service;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Modules\Admin\Models\Account;
use Modules\Admin\Models\Notes;
use Modules\Admin\Models\ProductCategory;
use Modules\Admin\Models\Products;
use PDF;
use App\Services\CalendarService;

class OrderController extends BaseController
{
  public function __construct(OrderRepository $repository)
  {
    $this->repository = $repository;
  }
  /**
   * @OA\Get(
   *      path="/admin/order",
   *      operationId="OrderController::index",
   *      tags={"Admin.Order"},
   *      summary="Get list of Orders",
   *      description="Returns list of Orders",
   *      security={{"api_http_auth": {}}},
   *     @OA\Parameter(
   *         name="search",
   *         in="query",
   *         description="Global search term. Use `%` to wildcard search.",
   *         @OA\Schema(
   *             type="string",
   *         ),
   *         style="form"
   *     ),
   *     @OA\Parameter(
   *         name="filter",
   *         in="query",
   *         description="field_name:operator:value. Available operators: `gt` `gte` `lt` `lte` `eq` `ne` `neq` `like` `in` `nin` `null` `not-null` `true` `false`
   *      Example: `age:gt:18` `status:in:pending|in_progress`",
   *         @OA\Schema(
   *              type="string",
   *                pattern="^.*:(gt|gte|lt|lte|eq|ne|neq|like|in|nin|null|not\-null|true|false):.*$"
   *         ),
   *         style="form"
   *     ),
   *     @OA\Parameter(
   *         name="sort",
   *         in="query",
   *
   *         description="Sorting fields. Leading minus as DESC sorting.",
   *         @OA\Schema(
   *             type="string",default="-id",
   *         ),
   *         style="form"
   *     ),
   *     @OA\Parameter(
   *         name="with",
   *         in="query",
   *         description="Get relationships.",
   *         @OA\Schema(
   *              type="string"
   *         ),
   *         style="form"
   *     ),
   *     @OA\Parameter(
   *         name="with_count",
   *         in="query",
   *         description="Get the count number of relationships.",
   *         @OA\Schema(
   *             type="string"
   *         ),
   *         style="form"
   *     ),
   *     @OA\Parameter(
   *         name="page",
   *         in="query",
   *         description="Page number. Start from 1.",
   *         @OA\Schema(
   *             type="integer", default=1
   *         ),
   *         style="form"
   *     ),
   *     @OA\Parameter(
   *         name="size",
   *         in="query",
   *         description="Items per page.",
   *         @OA\Schema(
   *             type="integer", default=10 ,
   *         ),
   *         style="form"
   *     ),
   *      @OA\Parameter(
   *         name="other_param",
   *         in="query",
   *         description="filter by the sub category ids of leads",
   *         @OA\Schema(
   *             type="string"
   *         ),
   *         style="form"
   *     ),
   *      @OA\Response(response=200, description="successful operation",
   *          @OA\JsonContent(type="object", description="Response of an index API.",
   *              required={"links","meta","data"},
   *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
   *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
   *              @OA\Property(property="data", type="array", description="Data array.",
   *                   @OA\Items(ref="#/components/schemas/Admin.OrderResource")
   *              ),
   *          ),
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param OrderListRequest $request
   * @return OrderCollection
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function index(OrderListRequest $request)
  {
    $this->authorize('viewAny', Order::class);
    $query = QueryBuilder::for(Order::class)->getQuery();

    $query->join('leads as leads_1', 'order.lead_id', '=', 'leads_1.id');

    $input = $request->all();
    if (isset($input["other_param"]) && $input["other_param"]) {
      $otherParam = json_decode($input["other_param"]);
     foreach ($otherParam as $k => $v) {
        $input[$k] = $v;
      }
    }
    if (isset($input["subCategoryIds"]) && $input["subCategoryIds"]) {
      // product filter
      $subCategoryIds = $input["subCategoryIds"];
      if ($subCategoryIds) {
        $ids = explode(',', $subCategoryIds);
        if ($ids && count($ids)) {
          $where = [];
          foreach ($ids as $id) {
            $where[] = "leads_1.sub_category_ids LIKE '%,{$id},%'";
            $where[] = "leads_1.sub_category_ids LIKE '%,{$id}'";
            $where[] = "leads_1.sub_category_ids LIKE '{$id},%'";
            $where[] = "leads_1.sub_category_ids = '{$id}'";
          }
          $where = implode(' or ', $where);
          $query->whereRaw("({$where})");
        }
      }
    }
    // client filter
    if (isset($input["clientKeyWords"]) && $input["clientKeyWords"]) {
      $clientInfo = "%{$input["clientKeyWords"]}%";
      $query->join('clients', 'clients.id', '=', 'leads_1.client_id')
        ->whereRaw("(clients.surname LIKE ? or clients.phone LIKE ? or clients.given_name LIKE ? or clients.address LIKE ?)", [$clientInfo, $clientInfo, $clientInfo, $clientInfo]);
    }

    if (isset($input["reasonKeyWords"]) && $input["reasonKeyWords"]) {
      $reasonInfo = "%{$input["reasonKeyWords"]}%";
      $query->where('order.reason', 'like', $reasonInfo);
    }

    if (isset($input['installerIds']) && $input['installerIds']) {
      $installerIds = $input["installerIds"];
      if ($installerIds) {
        $ids = explode(',', $installerIds);
        if ($ids && count($ids)) {
          $where = [];
          $where[] = "install_id in ({$installerIds}) and order.result = 'installation'";
          $where[] = "cm_id in ({$installerIds}) and order.result = 'cm'";
          $where[] = "(install_id is null and order.result = 'installation')";
          $where[] = "(cm_id is null and order.result = 'cm') ";
          $where[] = "order.result = 'new_order'";
          $where = implode(' or ', $where);
          $query->whereRaw("({$where})");
        }
      }
    }

    if (isset($input['apptDate']) && $input['apptDate']) {
      $apptDate = $input["apptDate"];
      if ($apptDate) {
        $apptDateList = explode(',', $apptDate);
        if ($apptDateList && count($apptDateList)) {
          $where = [];
          $start = $apptDateList[0];
          $end = $apptDateList[1];
          $where[] = "(order.cm_booked_date >= '{$start}' and order.cm_booked_date <= '{$end}')";
          $where[] = "(order.installation_date >= '{$start}' and order.installation_date <= '{$end}')";
          $where[] = "(order.result in ('new_order', 'ready', 'extra'))";
          $where = implode(' or ', $where);
          $query->whereRaw("({$where})");
        }
      }
    }
    if (isset($input['staff']) && $input['staff']) {
      $staff = $input['staff'];
      $query->leftJoin('accounts as accounts_1', 'accounts_1.id', '=', 'order.product_staff_id')
              ->where(function ($q) use ($staff) {
                  $q->whereNull('order.product_staff_id')
                   ->orWhere('accounts_1.login_name', 'not like', "%{$staff}%");
              });
    }

    if (!$request->user()->isPlatformUser()) {
      $query->where('order.company_region', $request->user()->company_region);
    }  else if (!$request->user()->isSuperAdmin()) {
      $companyRegions = explode(',', $request->user()->company_ids);
      $query->whereIn('order.company_region', $companyRegions);
    }

    $statsQuery = clone $query;

    $totalCount = $statsQuery->count();

    $totalUnit = $statsQuery->sum('qty');

    $totalSold = $statsQuery->sum('leads_1.sold');
    $paginated = QueryBuilder::paginate($query);
    $data = new OrderCollection($paginated);
    $data->additional([
      'summaryData' => [
        'totalCount' => $totalCount,
        'totalUnit' => $totalUnit,
        'totalSold' => $totalSold,
      ]
    ]);
    return $data;
  }
  /**
   * @OA\Get(
   *      path="/admin/order/{id}",
   *      operationId="OrderController::show",
   *      tags={"Admin.Order"},
   *      summary="Display the specified Order",
   *      description="Get one Order by id",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(
   *          name="id",
   *          description="Order id",
   *          required=true,
   *          in="path",
   *          @OA\Schema(
   *              type="integer"
   *          )
   *      ),
   *      @OA\Parameter(ref="#/components/parameters/with"),
   *      @OA\Response(response=200, description="successful operation",
   *          @OA\JsonContent(type="object", description="Fetched record.",
   *              required={"data"},
   *              @OA\Property(property="data", ref="#/components/schemas/Admin.OrderResource")
   *          ),
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   *
   * @param OrderGetRequest $request
   * @return JsonResponse|OrderResource
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function show($id, OrderGetRequest $request)
  {
    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError('You can not view other company order');
    }
    $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'view product order');
    if ($errorMessage) {
      return $this->sendError($errorMessage);
    }
    $this->authorize('view', $item);
    $item->load($request->getWith());
    return new OrderResource($item);
  }
  /**
   * @OA\Put(
   *      path="/admin/order/{id}",
   *      operationId="OrderController::update",
   *      tags={"Admin.Order"},
   *      summary="Update the specified Order in storage.",
   *      description="Update one Order by id",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(
   *          name="id",
   *          description="Order id",
   *          required=true,
   *          in="path",
   *          @OA\Schema(
   *              type="integer"
   *          )
   *      ),
   *      @OA\RequestBody(
   *          description="Payload to update Order",
   *          required=true,
   *          @OA\JsonContent(ref="#/components/schemas/Admin.Order")
   *      ),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   * @param OrderUpdateRequest $request
   *
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function update($id, OrderUpdateRequest $request)
  {
    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError('You can not update other company order');
    }
    $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'update product order');
    if ($errorMessage) {
      return $this->sendError($errorMessage);
    }
    $this->authorize('update', $item);
    $input = $request->all();
    $oldToBeCollect = $item->to_be_collect;
    if (isset($input['lead']) && $input['lead']['sub_category_ids']) {
      $sub_category_ids = explode(',', $input['lead']['sub_category_ids']);
      $product_id = $sub_category_ids[0];
      $input["product_id"] = $product_id;
    }
    unset($input['appointment']);
    unset($input['invoice']);
    unset($input['contracts']);
    unset($input['result']);
    unset($input['product_files']);
    unset($input['payments']);
    unset($input['trade']);
    if (!isset($input['finance_approved_date'])) {
      $input['finance_approved_date'] = null;
    }
    $this->repository->update($input, $id);
    if (isset($input['lead'])) {
      $leads = ['sub_category_ids' => $input['lead']['sub_category_ids']];
      Leads::where('id', $input['lead_id'])->update($leads);
    }
    activity_log()->withObject($item)->add('Update Order', $input);
    if (isset($input['to_be_collect']) && $input['to_be_collect'] != $oldToBeCollect) {
      $appointments = $item->appointment;
      foreach($appointments as $appointment){
        if ($appointment->event_id && $appointment->status != Appointment::STATUS_COMPLETED) {
          CalendarService::updateEventToCalendar($appointment);
        }
      }
    }
    return $this->sendResponse(null, 'Order updated successfully.');
  }
  /**
   * @OA\Delete(
   *      path="/admin/order/{id}",
   *      operationId="OrderController::destroy",
   *      tags={"Admin.Order"},
   *      summary="Cancel the specified Order.",
   *      description="Cancel one Order by id",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(
   *          name="id",
   *          description="Order id",
   *          required=true,
   *          in="path",
   *          @OA\Schema(
   *              type="integer"
   *          )
   *      ),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   *
   * @param OrderDeleteRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function destroy($id, OrderDeleteRequest $request)
  {
    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError('You can not delete other company order');
    }
    $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'delete product order');
    if ($errorMessage) {
      return $this->sendError($errorMessage);
    }
    $this->authorize('delete', $item);
    // $this->repository->delete($id);
    activity_log()->withObject($item)->add('Cancel Order', $item->toArray());
    return $this->sendResponse(null, 'Order cancelled successfully.');
  }
  /**
   * @OA\Put(
   *      path="/admin/order/add_payment/{id}",
   *      operationId="OrderController::addPayment",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="TODO OrderController::addPayment ",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(name="id", description="id", required=true, in="path",
   *          @OA\Schema(type="integer")
   *      ),
   *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderAddPaymentRequestBody"),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   * @param OrderAddPaymentRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function addPayment($id, OrderAddPaymentRequest $request)
  {
    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError('You can not add payment for other company order');
    }
    $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'add payment for the product order');
    if ($errorMessage) {
      return $this->sendError($errorMessage);
    }
    $input = $request->input();
    if ($input['payment_type'] == 'order') {
      $this->authorize('updatePayment', $item);
    } else {
      $this->authorize('updateServicePayment', $item);
    }
    $input['order_id'] = $id;
    $input['company_region'] = $item->company_region;
    $orderPaymentId = $request->input("order_payment_id", null);
    if ($orderPaymentId) {
      $payment = Payment::find($orderPaymentId);
      if (empty($payment)) {
        return $this->sendError('Payment not found');
      }
      $payment->received_date = $input['received_date'];
      $payment->amount = $input['amount'];
      $payment->detail = $input['detail'];
      $payment->type = $input['type'];
      $payment->save();
      note_log()->withOrder($id)->withActivity(Notes::TYPE_ACTIVITY)->add("Update Payment");
      activity_log()->withObject($item)->add('Update Payment', $input);
      return $this->sendResponse(null, 'Edit payment processed successfully.');
    } else {
      Payment::create($input);
      note_log()->withOrder($id)->withActivity(Notes::TYPE_ACTIVITY)->add("Add Payment");
      activity_log()->withObject($item)->add('Add Payment', $input);
    }

    // TODO OrderController::addPayment
    return $this->sendResponse(null, 'addPayment processed successfully.');
  }
  /**
   * @OA\Put(
   *      path="/admin/order/change_result/{id}",
   *      operationId="OrderController::changeResult",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="TODO OrderController::changeResult ",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(name="id", description="id", required=true, in="path",
   *          @OA\Schema(type="integer")
   *      ),
   *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderChangeResultRequestBody"),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   * @param OrderChangeResultRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function changeResult($id, OrderChangeResultRequest $request)
  {
    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    \Log::debug('Start change order result!');
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError("You can not change other company order's result");
    }
    $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'chang result for the product order');
    if ($errorMessage) {
      return $this->sendError($errorMessage);
    }
    $input = $request->input();
    switch ($input['result']) {
      case "cancelled":
        $this->authorize('cancelOrder', $item);
        break;
      default:
        $this->authorize('changeResult', $item);
    }
    if ($input['result'] == Order::RESULT_ON_PRODUCTION && !isset($input['on_product_date'])) {
      return $this->sendError('Please input the on production date!');
    }
    if ($input['result'] == Order::RESULT_ON_PRODUCTION) {
      $input['ready_date'] = null;
    }
    if ($input['result'] == Order::RESULT_READY) {
       \Log::debug('Check field for ready result!');
      if (!isset($input['ready_date']) && !$item->result == 'installation' ) {
        return $this->sendError('Please input the ready date!');
      }
      if (!isset($input['followup_time']) && $item->result == 'installation' ) {
        return $this->sendError('Please input the followup date!');
      }
      if (isset($input['followup_time'])) {
        $input['followup_date'] = $input['followup_time'];
      }
      \Log::debug('Check field for ready result success!');
    }
    if ($input['result'] == Order::RESULT_COMPLETION && !$item->completion_date) {
      $input['completion_date'] = $item->installation_date ? $item->installation_date : date("Y-m-d H:i:s");
    }
    if ($input['result'] == Order::RESULT_HOLD) {
      $input['hold_time'] = date("Y-m-d H:i:s");
    }
    if ($input['result'] == Order::RESULT_IN_COUNCIL) {
      if (!isset($input['submit_date'])) {
        return $this->sendError('Please input the submit date!');
      }
      if ($item->result === Order::RESULT_CM) {
        $input['decision'] = 'Submitted';
      }
      $input['submit_date'] = $input['submit_date'];
    }

    if ($input['result'] != Order::RESULT_IN_COUNCIL && $item->result ==  Order::RESULT_IN_COUNCIL) {
      \Log::debug('Check last result for ready result!');
      if (!isset($input['approved_date'])) {
         \Log::debug('Check last result for ready result failed!');
        return $this->sendError('Please input the approved date!');
      }
      \Log::debug('Check last result for ready result success!');
      $input['approved_date'] = $input['approved_date'];
      if ($input['result']  === Order::RESULT_ON_PRODUCTION) {
         $input['decision'] = 'Development Approval';
      }
    }

    // add last order result
    $input['last_result'] =  $item->result;
    \Log::debug($input);
    $this->repository->update($input, $id);
    // when cancel the order we need cancel the lead too
    if ($input['result'] == Order::RESULT_CANCELLED) {
      $leads = $item->lead;
      $leads->result = Order::RESULT_CANCELLED;
      $leads->save();
      Appointment::where('object_id', $leads->id)->whereNotIn('status', [Appointment::STATUS_COMPLETED, Appointment::STATUS_CANCEL])->whereIn('type', ['cm', 'install'])->update(['status' => Appointment::STATUS_CANCEL]);
    }

    $cancelAppt = "";
    if ($input['result'] == Order::RESULT_READY || $input['result'] == Order::RESULT_NEW) {
      $leads = $item->lead;
      $res = Appointment::where('object_id', $leads->id)->whereNotIn('status', [Appointment::STATUS_COMPLETED, Appointment::STATUS_CANCEL])->whereIn('type', ['cm', 'install'])->update(['status' => Appointment::STATUS_CANCEL]);
      if ($res) {
        $cancelAppt = "and cancelled the appointment.";
      }
    }
    note_log()->withOrder($id)->withResult($input['result'])->add("Change the result to '" . $input['result'] . "'" . $cancelAppt, isset($input['note']) ? $input['note'] : '');
    // TODO OrderController::changeResult
    activity_log()->withObject($item)->add('Change Result', $input);

    return $this->sendResponse(null, 'changeResult processed successfully.');
  }

  /**
   * @OA\Get(
   *      path="/admin/order/get_summary/info",
   *      operationId="OrderController::getSummaryInfo",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="TODO OrderController::getSummaryInfo ",
   *      @OA\Parameter(
   *         name="company_region",
   *         in="query",
   *         description="filter by company_region",
   *         @OA\Schema(
   *             type="string"
   *         ),
   *         style="form"
   *     ),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param OrderGetSummaryInfoRequest $request
   * @return JsonResponse
   */
  public function getSummaryInfo(OrderGetSummaryInfoRequest $request)
  {
    // TODO OrderController::getSummaryInfo
    $newQuery = Order::where('result', Order::RESULT_NEW);
    $cmQuery = Order::where('result', Order::RESULT_CM);
    $cmReceivedQuery = Order::where('result', Order::RESULT_CM_RECEIVED);
    $onProductionQuery = Order::where('result', Order::RESULT_ON_PRODUCTION);
    $readyQuery = Order::where('result', Order::RESULT_READY);
    $installationQuery = Order::where('result', Order::RESULT_INSTALLATION);
    $extraQuery = Order::where('result', Order::RESULT_EXTRA);
    $outstandingQuery = Order::where('result', Order::RESULT_OUTSTANDING);
    $completionQuery = Order::where('result', Order::RESULT_COMPLETION);
    $holdQuery = Order::where('result', Order::RESULT_HOLD);
    $cancelledQuery = Order::where('result', Order::RESULT_CANCELLED);
    $allQuery = Order::query();
    $input = $request->all();
    if (!$request->user()->isSuperAdmin() && $request->user()->isPlatformUser()) {
      $input["company_region"] = $request->user()->company_ids;
    }
    if (!$request->user()->isPlatformUser()) {
      $regions = [$request->user()->company_region];
    }  else if(isset($input["company_region"]) && $input["company_region"]){
      $regions = explode(',', $input["company_region"]);
    }
    if (!empty($regions)) {
      $newQuery->whereIn('company_region',  $regions);
      $cmQuery->whereIn('company_region',  $regions);
      $cmReceivedQuery->whereIn('company_region',  $regions);
      $onProductionQuery->whereIn('company_region',  $regions);
      $readyQuery->whereIn('company_region',  $regions);
      $installationQuery->whereIn('company_region',  $regions);
      $extraQuery->whereIn('company_region',  $regions);
      $outstandingQuery->whereIn('company_region',  $regions);
      $completionQuery->whereIn('company_region',  $regions);
      $holdQuery->whereIn('company_region',  $regions);
      $cancelledQuery->whereIn('company_region',  $regions);
      $allQuery->whereIn('company_region',  $regions);
    }
    $data['new_order'] = $newQuery->count();
    $data['cm'] = $cmQuery->count();
    $data['cmReceived'] = $cmReceivedQuery->count();
    $data['onProduction'] = $onProductionQuery->count();
    $data['ready'] = $readyQuery->count();
    $data['installation'] = $installationQuery->count();
    $data['extra'] = $extraQuery->count();
    $data['outstanding'] = $outstandingQuery->count();
    $data['completion'] = $completionQuery->count();
    $data['hold'] = $holdQuery->count();
    $data['cancelled'] = $cancelledQuery->count();
    $data['all'] = $allQuery->count();

    return $this->sendResponse($data, 'getSummaryInfo processed successfully.');
  }
  /**
   * @OA\Post(
   *      path="/admin/order/save_spec",
   *      operationId="OrderController::saveSpec",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="TODO OrderController::saveSpec ",
   *      security={{"api_http_auth": {}}},
   *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderSaveSpecRequestBody"),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param OrderSaveSpecRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function saveSpec(OrderSaveSpecRequest $request)
  {
    $this->authorize('saveSpec', Order::class);
    // TODO OrderController::saveSpec
    $data = $request->input();
    if (is_string($data)) {
      $data = json_decode($data, true);
    }
    $item = $this->repository->findWithoutFail($data['order_id']);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError("You can not change other company order's spec");
    }
    $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'chang spec for the product order');
    if ($errorMessage) {
      return $this->sendError($errorMessage);
    }
    DB::beginTransaction();
    $productIds = [];
    try {
      OrderProductSpec::where('order_id', $data['order_id'])->where('spec_type', OrderProductSpec::TYPE_ORDER)->forceDelete();
      $isElectrician = false;
      foreach ($data['list'] as $specItem) {
        $inset = [
          'order_id' => $data['order_id'],
          'product_id' => $specItem['product_id'],
          'spec' => $specItem['spec'],
          'spec_type' => OrderProductSpec::TYPE_ORDER,
          'note' => $specItem['note']
        ];
        if (!in_array($specItem['product_id'],  $productIds)) {
          $productIds[] = $specItem['product_id'];
        }

        if (isset($specItem['spec']['Electrician']) && $specItem['spec']['Electrician'] && $specItem['spec']['Electrician'] != 'No') {
          $isElectrician = true;
        }
        $orderProductSpec = new OrderProductSpec($inset);
        $orderProductSpec->save();
      }
        $qty = null;
       if(isset($specItem['spec']["Site area(m2)"])){
        $qty = $specItem['spec']["Site area(m2)"];
       }else if(isset($specItem['spec']["RoofArea(m2)"]) || isset($specItem['spec']["GLength(m)"])){
        $qty = $specItem['spec']["RoofArea(m2)"];
        $qty += $specItem['spec']["GLength(m)"];
       }else{
        $qty = count($data['list']);
       }
      $leads = $item->lead;
      $leads->sub_category_ids = implode(',', $productIds);
      $leads->save();
      // update order product
      $product_id = null;
      if ($productIds && count($productIds)) {
        $product_id = $productIds[0];
      }
      $item->product_id = $product_id;
      $item->qty = $qty;
      $item->electrician = $isElectrician ? 1 : 0;
      if (!$request->user()->isAccounting()) {
        $item->product_staff_id = $request->user()->id;
      }
      $item->save();
    } catch (\Exception $exception) {
      DB::rollBack();
      return $this->sendError('Fail to save product spec!');
    }
    DB::commit();

    return $this->sendResponse(null, 'saveSpec processed successfully.');
  }
  /**
   * @OA\Put(
   *      path="/admin/order/add_supplier/{id}",
   *      operationId="OrderController::addSupplier",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="Add or update supplier order info",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(name="id", description="id", required=true, in="path",
   *          @OA\Schema(type="integer")
   *      ),
   *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderAddSupplierRequestBody"),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   * @param OrderAddSupplierRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function addSupplier($id, OrderAddSupplierRequest $request)
  {
    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError("You can not add supplier for other company order");
    }
    $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'add supplier for the product order');
    if ($errorMessage) {
      return $this->sendError($errorMessage);
    }
    $input = $request->input();
    if (isset($input['type'])) {
      if ($input['type'] == 'service') {
        $this->authorize('addServiceSupplier', $item);
      } else {
        $this->authorize('addOrderSupplier', $item);
      }
    }
    $message = 'addSupplier processed successfully.';
    $orderSupplierId = $request->input("order_supplier_id", null);
    if ($orderSupplierId) {
      $supplier = OrderSupplier::find($orderSupplierId);
      if ($supplier->type == 'service') {
        $this->authorize('addServiceSupplier', $item);
      } else {
        $this->authorize('addOrderSupplier', $item);
      }
      if (empty($supplier)) {
        return $this->sendError('Supplier not found');
      }
      if(isset($input['supplier_received_date'])){
        $supplier->supplier_received_date = $input['supplier_received_date'];
      } else {
         $supplier->supplier_received_date = null;
      }
      if(isset($input['supplier_amount'])){
        $supplier->supplier_amount = $input['supplier_amount'];
      } else {
        $supplier->supplier_amount = null;
      }

      if(isset($input['note'])){
        $supplier->note = $input['note'];
      } else {
        $supplier->note = null;
      }

      $supplier->save();
      $message = 'Edit supplier processed successfully.';
    } else {
      OrderSupplier::create($input);
    }
     
    // update followup date
    if (empty($input['type']) || $input['type'] == 'order') {
      $supplierList = OrderSupplier::where('order_id', $id)
                                    ->where('type', 'order')
                                    ->whereNull('supplier_received_date')
                                    ->orderBy('order_date', 'asc')
                                    ->get();
      $followupDate = null;
      if (count( $supplierList)) {
        $followupDate = $supplierList[0]->order_date;
      }
      if ($followupDate) {
        $item->followup_date = $followupDate;
        $item->save();
      }
    }
    // TODO OrderController::addSupplier
    return $this->sendResponse(null, $message);
  }
  /**
   * @OA\Put(
   *      path="/admin/order/remove_supplier/{id}",
   *      operationId="OrderController::removeSupplier",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="TODO OrderController::removeSupplier ",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(name="id", description="id", required=true, in="path",
   *          @OA\Schema(type="integer")
   *      ),
   *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderRemoveSupplierRequestBody"),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   * @param OrderRemoveSupplierRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function removeSupplier($id, OrderRemoveSupplierRequest $request)
  {
    $input = $request->input();
    $orderSupplierId  = $input["order_supplier_id"];
    $supplier = OrderSupplier::find($orderSupplierId);
    if (empty($supplier)) {
      return $this->sendError('Supplier not found');
    } else {
      if (!$request->user()->isPlatformUser() && $supplier->company_region != $request->user()->company_region) {
        return $this->sendError("You can not delete supplier for other company order");
      }
      $errorMessage = $this->checkPlatformCompany($request, $supplier->company_region, 'delete supplier for the product order');
      if ($errorMessage) {
        return $this->sendError($errorMessage);
      }
      $supplier->delete();
    }
    // TODO OrderController::removeSupplier
    return $this->sendResponse(null, 'removeSupplier processed successfully.');
  }
  /**
   * @OA\Put(
   *      path="/admin/order/create_service/{id}",
   *      operationId="OrderController::createService",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="TODO OrderController::createService ",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(name="id", description="id", required=true, in="path",
   *          @OA\Schema(type="integer")
   *      ),
   *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderCreateServiceRequestBody"),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   * @param OrderCreateServiceRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function createService($id, OrderCreateServiceRequest $request)
  {
    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError("You can not create service for other company order");
    }
    $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'create service for the product order');
    if ($errorMessage) {
      return $this->sendError($errorMessage);
    }
    $this->authorize('createService', $item);
    // TODO OrderController::createService
    if ($item->result != Order::RESULT_COMPLETION) {
      return $this->sendError('This order is not completed and cannot create after-sales service.');
    }
    $input = $request->input();
    $data = [
      'lead_id' => $item->lead_id,
      'order_id' =>  $item->id,
      'result' => Service::RESULT_NEW,
      'amount' => $input['amount'],
      'received_date' => date("Y-m-d H:i:s"),
      'product_id' => $item->product_id,
      'company_region' => $item->company_region
    ];
    $serviceCount = Service::where('lead_id', $item->lead_id)->whereIn('result', [
      Service::RESULT_NEW, Service::RESULT_INSTALLATION, Service::RESULT_READY, Service::RESULT_OUTSTANDING,  Service::RESULT_ON_PRODUCTION
    ])->count();
    if ($serviceCount) {
      return $this->sendError('This order has an outstanding after-sales service.');
    }
    $service = Service::create($data);
    note_log()->withService($service->id)->withActivity('Create a service');
    return $this->sendResponse(null, 'Create Service processed successfully.');
  }
  /**
   * @OA\Delete(
   *      path="/admin/order/del_payment/{id}",
   *      operationId="OrderController::delPayment",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="TODO OrderController::delPayment ",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(name="id", description="id", required=true, in="path",
   *          @OA\Schema(type="integer")
   *      ),
   *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderDelPaymentRequestBody"),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   * @param OrderDelPaymentRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function delPayment($id, OrderDelPaymentRequest $request)
  {
    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError("You can not delete payment for other company order");
    }
    $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'delete payment for the product order');
    if ($errorMessage) {
      return $this->sendError($errorMessage);
    }
    $this->authorize('updatePayment', $item);
    $input = $request->input();
    if (isset($input["payment_id"]) && isset($input["type"])) {
      Payment::where('id', $input["payment_id"])->where('payment_type', $input["type"])->delete();
    }
    // TODO OrderController::delPayment
    return $this->sendResponse(null, 'delPayment processed successfully.');
  }
  /**
   * @OA\Post(
   *      path="/admin/order/generatorPDF/{id}",
   *      operationId="OrderController::generatorPDF",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="TODO OrderController::generatorPDF ",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(name="id", description="id", required=true, in="path",
   *          @OA\Schema(type="integer")
   *      ),
   *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderGeneratorPDFRequestBody"),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   * @param OrderGeneratorPDFRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function generatorPDF($id, OrderGeneratorPDFRequest $request)
  {

    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError("You can not generator PDF for other company order");
    }
    $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'generator PDF for the product order');
    if ($errorMessage) {
      return $this->sendError($errorMessage);
    }
    $this->authorize('generatorPDF', $item);
    $invoice = [
      "date" => date("d/m/Y"), // input date
      "amount" => '', // input amount:Current Payment Due
      "orderID" => '',
      "now" => date("d/m/Y"),
      "primaryName" => '',
      "secondaryName" => '',
      "primaryName" => '',
      "address" => ["beforeSuburb" => "", "afterSuburb" => ""],
      "code" => "",
      "quantity" => 0,
      "unit" => "pcs",
      "product" => "",
      "sold" => 0,
      "itemAmount" => 0,
      "gst" => 0,
      "payments" => [],
      "paymentReceived" => 0,
      "totalBalanceDueOnCOmpletion" => "",
      "installDate" => "",
      "desc" => "includes material , installation charges and service"
    ];
    $leads = $item->lead;
    $type = $request->input("type");
    $invoice["date"] = $request->input("date", date("d/m/Y"));
    $invoice["amount"] = $request->input("amount", '');
    $invoice["orderID"] = $leads->id;
    $invoice["now"] = date("d/m/Y");
    // client info and sold
    if ($leads && $leads->client) {
      $invoice["primaryName"] = $leads->client->given_name . " " . $leads->client->surname;
      $invoice["secondaryName"] = $leads->client->sec_given_name . " " . $leads->client->sec_surname;
      $invoice["address"] = clientAddressHandle($leads->client->address);
      $invoice["sold"] = $leads->sold;
      $invoice["itemAmount"] = round($leads->sold / 1.1, 2);
      $invoice["gst"] = round($leads->sold - $invoice["itemAmount"], 2);
    }
    if ($leads && $leads->sub_category_ids) {
      $sub_category_ids = explode(',', $leads->sub_category_ids);
      $products = Products::whereIn('id',  $sub_category_ids)->select('code', 'name')->get()->toArray();
      $invoice["code"] = implode(',', array_column($products, 'code'));
      $invoice["product"] = implode(',', array_column($products, 'name'));
    }
    $product_spec = $item->productSpec;
    if ($leads->category) {
      $qty = 0;
      switch ($leads->category->name) {
        case 'Landscaping':
          $invoice["unit"] = "sqm";
          if ($product_spec && isset($product_spec[0]) && $product_spec[0]["spec"]) {
            $qty = $product_spec[0]["spec"]["Site area(m2)"] || 0;
          }
          break;
        case 'Roof':
          $invoice["unit"] = "sqm";
          if (strpos($invoice["code"], 'GT') !== false || strpos($invoice["code"], 'GG') !== false) {
            $invoice["unit"] = "m";
          }
          if ($product_spec && isset($product_spec[0]) && $product_spec[0]["spec"]) {
            $qty = $product_spec[0]["spec"]["RoofArea(m2)"] || 0;
          }
          break;
        default:
          $invoice["unit"] = "pcs";
          if ($product_spec) {
            $qty = count($product_spec);
          }
      }
      $invoice["quantity"] = $qty;
    }
    // payment info
    if ($item->payments) {
      $invoice["payments"] = [];
      $paymentReceived = 0;
      foreach ($item->payments as $v) {
        if ($v->type == 'Refund') {
          $paymentReceived -= $v->amount;
        } else if ($v->type != 'Unpaid') {
          $paymentReceived += $v->amount;
        }
        if ($v->type != 'Unpaid') {
           $invoice["payments"][] = $v;
        }
      }
      $invoice["paymentReceived"] = $paymentReceived;
      $invoice["totalBalanceDueOnCOmpletion"] = $invoice["sold"] - $paymentReceived;
    }

    if ($type == 'paid_invoice') {
      $lastAppt = Appointment::where("object_id", $leads->id)
      ->where('type', 'install')
      ->orderBy('id','desc')
      ->first();
      if (!empty($lastAppt)) {
        $invoice["installDate"] =  $lastAppt['date'];
      }
    }

    try {
      $pdf = App::make('snappy.pdf.wrapper')->setPaper('a4')->setOrientation('portrait');
      if ($type == 'due_invoice') {
        $pdf = $pdf->loadView('invoices.invoiceDue', ['invoice' => $invoice]);
      } else {
        $pdf = $pdf->loadView('invoices.invoicePaid', ['invoice' => $invoice]);
      }
      //  return $pdf->download();
      $folder      = storage_path('app') . '/public/pdf/';
      if (!\Illuminate\Support\Facades\File::exists($folder)) {
        \Illuminate\Support\Facades\File::makeDirectory($folder, 0775, true); //creates directory
      }
      $fileName = "{$type}_{$id}_" . date("YmdHi") . ".pdf";
      $path = $folder . $fileName;
      $url = "pdf/" . $fileName;
      if (is_file($path)) {
        $publicUrl = $this->getPublicUrl($url);
      } else {
        $pdf->Output($path, 'F');
        $pdf->save($path);
        $publicUrl = $this->getPublicUrl($url);
      }
      return $this->sendResponse(["url" => $publicUrl], 'Generate invoice successfully.');
    } catch (\Exception $e) {
      \Log::error('Generate invoice error: ' . $e->getMessage());
      return $this->sendResponse(null, 'Failed to generate invoice!', 500);
    }
  }

  public function getPublicUrl($path)
  {
    $url = url('storage/' . $path);
    if (env('URL_IS_HTTPS', false) && $url && strpos($url, 'https') == false) {
      $url = Str::replaceFirst("http://", 'https://', $url);
    }
    return $url;
  }
  /**
   * @OA\Post(
   *      path="/admin/order/generatorSatisfactionPDF/{id}",
   *      operationId="OrderController::generatorSatisfactionPDF",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="TODO OrderController::generatorSatisfactionPDF ",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(name="id", description="id", required=true, in="path",
   *          @OA\Schema(type="integer")
   *      ),
   *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderGeneratorSatisfactionPDFRequestBody"),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   * @param OrderGeneratorSatisfactionPDFRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function generatorSatisfactionPDF($id, OrderGeneratorSatisfactionPDFRequest $request)
  {
    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError("You can not generator Satisfaction PDF for other company order");
    }
    $this->authorize('generatorSatisfactionPDF', $item);
    $data = [
      "primaryName" => "",
      "secondaryName" => "",
      "address" => "",
      "installed_date" => "",
      "Installer" => "",
      "product" => "",
    ];
    $leads = $item->lead;
    // client info and sold
    if ($leads && $leads->client) {
      $data["primaryName"] = $leads->client->given_name . " " . $leads->client->surname;
      if ($leads->client->sec_given_name || $leads->client->sec_surname) {
        $data["secondaryName"] = $leads->client->sec_given_name . " " . $leads->client->sec_surname;
      }
      $data["address"] = $leads->client->address;
      if ($item->installed_date) {
        $data["installed_date"] =  format_datetime($item->installed_date, 'd/m/Y');
      }
    }
    if ($item->install_id) {
      $installer = Account::find($item->install_id);
      $data["Installer"] = $installer->name;
    }
    if ($item->product) {
      $data["product"] = $item->product->name;
    }
    try {
      $pdf = App::make('snappy.pdf.wrapper')->setPaper('a4')->setOrientation('portrait');
      $pdf = $pdf->loadView('pdf.customerSatisfactionSlip', ['data' => $data]);
      //  return $pdf->download();
      $folder      = storage_path('app') . '/public/pdf/';
      if (!\Illuminate\Support\Facades\File::exists($folder)) {
        \Illuminate\Support\Facades\File::makeDirectory($folder, 0775, true); //creates directory
      }
      $fileName = "satisfaction_{$leads->id}_" . date("YmdHi") . ".pdf";
      $path = $folder . $fileName;
      $url = "pdf/" . $fileName;
      if (is_file($path)) {
        $publicUrl = $this->getPublicUrl($url);
      } else {
        $pdf->Output($path, 'F');
        $pdf->save($path);
        $publicUrl = $this->getPublicUrl($url);
      }
      return $this->sendResponse(["url" => $publicUrl], 'Generate Customer Satisfaction successfully.');
    } catch (\Exception $e) {
      \Log::error('Generate Customer Satisfaction error: ' . $e->getMessage());
      return $this->sendResponse(null, 'Failed to generate Generate Customer Satisfaction!', 500);
    }
  }
  /**
   * @OA\Post(
   *      path="/admin/order/generatorWarrantyCardPDF/{id}",
   *      operationId="OrderController::generatorWarrantyCardPDF",
   *      tags={"Admin.Order"},
   *      summary="",
   *      description="TODO OrderController::generatorWarrantyCardPDF ",
   *      security={{"api_http_auth": {}}},
   *      @OA\Parameter(name="id", description="id", required=true, in="path",
   *          @OA\Schema(type="integer")
   *      ),
   *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderGeneratorWarrantyCardPDFRequestBody"),
   *      @OA\Response(
   *          response=200,
   *          description="successful operation"
   *      ),
   *      @OA\Response(response=400, description="Bad request")
   *  )
   *
   * @param int $id
   * @param OrderGeneratorWarrantyCardPDFRequest $request
   * @return JsonResponse
   * @throws \Illuminate\Auth\Access\AuthorizationException
   */
  public function generatorWarrantyCardPDF($id, OrderGeneratorWarrantyCardPDFRequest $request)
  {
    /**
     * @var $item Order
     */
    $item = $this->repository->findWithoutFail($id);
    if (empty($item)) {
      return $this->sendError('Order not found');
    }
    if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
      return $this->sendError("You can not generator WarrantyCard PDF for other company order");
    }
    $leads = $item->lead;
    if (empty($leads)) {
      return $this->sendError('Leads not found');
    }
    $data = [
      "order_id" => $leads->id,
      "surname" => "",
      "give_name" => "",
      "phone" => "",
      "address" => "",
      "consultant" => "",
      "installer" => "",
      "installed_date" => $item->installed_date,
      "sold" => $leads->sold,
      "product" => ''
    ];
    if ($leads && $leads->client) {
      $data["give_name"] = $leads->client->given_name;
      $data["surname"] = $leads->client->surname;
      $data["phone"] = $leads->client->phone;
      $data["address"] = $leads->client->address;
      if ($item->installed_date) {
        $data["installed_date"] =  format_datetime($item->installed_date, 'd/m/Y');
      }
    }
    if ($item->install_id) {
      $installer = Account::withTrashed()->find($item->install_id);
      $data["installer"] = $installer->name;
    }
    if($leads->sales_consultant_id){
      $salesConsultant  = Account::withTrashed()->find($leads->sales_consultant_id);
      $data["consultant"] = $salesConsultant->name;
    }
    if ($item->product) {
      $data["product"] = $item->product->name;
    }
    if ($leads->product_category_id) {
      $category = ProductCategory::find($leads['product_category_id']);
    }

    $viewTem = 'garageDoorsWarrantyCard';
    switch($category->name){
      case "Landscaping":
        if($data["product"] == 'Turf'){
          $viewTem = 'turfWarrantyCard';
        } if($data["product"] == 'Retaining Wall'){
          $viewTem = 'retainingWallWarrantyCard';
        } else{
          $viewTem = 'pavingWarrantyCard';
        }
        break;
      case "Blinds":
        if($data["product"] == 'Outdoor Blinds'){
          $viewTem = 'outdoorBlindsWarrantyCard';
        }else if($data["product"] == 'Block Out Blinds'){
          $viewTem = 'blockOutBlindsWarrantyCard';
        }else if($data["product"] == 'Plantation Shutters'){
          $viewTem = 'plantationShuttersWarrantyCard';
        }else if($data["product"] == 'Roller Shutters'){
          $viewTem = 'rollerShuttersWarrantyCard';
        }
        break;
      case "Roof":
        $viewTem = 'roofWarrantyCard';
        break;
      case "Patio":
          $viewTem = 'patioWarrantyCard';
          break;
    }

    try {
      $pdf = App::make('snappy.pdf.wrapper')->setPaper('a4')->setOrientation('portrait');
      $pdf = $pdf->loadView('pdf.'.$viewTem, ['data' => $data]);
      $folder      = storage_path('app') . '/public/pdf/';
      if (!\Illuminate\Support\Facades\File::exists($folder)) {
        \Illuminate\Support\Facades\File::makeDirectory($folder, 0775, true); //creates directory
      }
      $fileName = "{$viewTem}_{$leads->id}_" . date("YmdHi") . ".pdf";
      $path = $folder . $fileName;
      $url = "pdf/" . $fileName;
      if (is_file($path)) {
        $publicUrl = $this->getPublicUrl($url);
      } else {
        $pdf->Output($path, 'F');
        $pdf->save($path);
        $publicUrl = $this->getPublicUrl($url);
      }
      return $this->sendResponse(["url" => $publicUrl], 'Generate Warranty Card successfully.');
    } catch (\Exception $e) {
      \Log::error('Generate Generate Warranty Card error: ' . $e->getMessage());
      return $this->sendResponse(null, 'Failed to generate Warranty Card!', 500);
    }
    // $this->authorize('generatorWarrantyCardPDF', $item);
    // // TODO OrderController::generatorWarrantyCardPDF
    // return $this->sendResponse(null, 'generatorWarrantyCardPDF processed successfully.');
  }
  /**
  * @OA\Post(
  *      path="/admin/Admin/order/generatorReport",
  *      operationId="OrderController::generatorReport",
  *      tags={"Admin.Order"},
  *      summary="",
  *      description="TODO OrderController::generatorReport ",
  *      security={{"api_http_auth": {}}},
  *      @OA\Parameter(
  *         name="company_region",
  *         in="query",
  *         description="filter by company_region",
  *         @OA\Schema(
  *             type="string"
  *         ),
  *         style="form"
  *     ),
  *      @OA\RequestBody(ref="#/components/requestBodies/Admin.OrderGeneratorReportRequestBody"),
  *      @OA\Response(
  *          response=200,
  *          description="successful operation"
  *      ),
  *      @OA\Response(response=400, description="Bad request")
  *  )
  *
  * @param OrderGeneratorReportRequest $request
  * @return JsonResponse
  * @throws \Illuminate\Auth\Access\AuthorizationException
  */
  public function generatorReport(OrderGeneratorReportRequest $request)
  {
      $this->authorize('generatorReport', Order::class);
      // TODO OrderController::generatorReport
      $subCategoryIds = $request->input('subCategoryIds');

      if ($subCategoryIds) {
        $subCategoryIdList = explode(',', $subCategoryIds);
        $query = Order::whereNotIn('result', [Order::RESULT_CANCELLED, Order::RESULT_COMPLETION])
                            ->whereIn('product_id', $subCategoryIdList)
                            ->orderBy('result', 'asc');
      } else {
        $query = Order::whereNotIn('result', [Order::RESULT_CANCELLED, Order::RESULT_COMPLETION])
                            ->orderBy('result', 'asc');
      }
      $input = $request->all();
      if (!$request->user()->isPlatformUser()) {
        $regions = [$request->user()->company_region];
      }  else if(isset($input["company_region"]) && $input["company_region"]){
        $regions = explode(',', $input["company_region"]);
      }
      if (!empty($regions)) {
        $query->whereIn('company_region', $regions);
      }
      $orderList = $query->get();
      // \Log::debug(count($orderList));
      $data = [
        'new_order' => [
          'totalCount' => 1,
          'totalValue' => 0,
          'totalPaid' => 0,
          'totalBalance' => 0,
          'totalQty' => 0,
          'totalElectrician' => 0,
          'name' => 'New',
          'productList' => []
        ],
        'cm' => [
          'totalCount' => 1,
          'totalValue' => 0,
          'totalPaid' => 0,
          'totalBalance' => 0,
          'totalQty' => 0,
          'totalElectrician' => 0,
          'name' => 'CM',
          'productList' => []
        ],
        'cmReceived' => [
          'totalCount' => 1,
          'totalValue' => 0,
          'totalPaid' => 0,
          'totalBalance' => 0,
          'totalQty' => 0,
          'totalElectrician' => 0,
          'name' => 'CM Received',
          'productList' => []
        ],
        'inCouncil' => [
          'totalCount' => 1,
          'totalValue' => 0,
          'totalPaid' => 0,
          'totalBalance' => 0,
          'totalQty' => 0,
          'totalElectrician' => 0,
          'name' => 'In Council',
          'productList' => []
        ],
        'onProduction' => [
          'totalCount' => 1,
          'totalValue' => 0,
          'totalPaid' => 0,
          'totalBalance' => 0,
          'totalQty' => 0,
          'totalElectrician' => 0,
          'name' => 'On Production',
          'productList' => []
        ],
        'ready' => [
          'totalCount' => 1,
          'totalValue' => 0,
          'totalPaid' => 0,
          'totalBalance' => 0,
          'totalQty' => 0,
          'totalElectrician' => 0,
          'name' => 'Ready',
          'productList' => []
        ],
        'installation' => [
          'totalCount' => 1,
          'totalValue' => 0,
          'totalPaid' => 0,
          'totalBalance' => 0,
          'totalQty' => 0,
          'totalElectrician' => 0,
          'name' => 'Installation',
          'productList' => []
        ],
        'extra' => [
          'totalCount' => 1,
          'totalValue' => 0,
          'totalPaid' => 0,
          'totalBalance' => 0,
          'totalQty' => 0,
          'totalElectrician' => 0,
          'name' => 'Extra',
          'productList' => []
        ],
        'outstanding' => [
          'totalCount' => 1,
          'totalValue' => 0,
          'totalPaid' => 0,
          'totalBalance' => 0,
          'totalQty' => 0,
          'totalElectrician' => 0,
          'name' => 'Outstanding',
          'productList' => []
        ],
        'hold' => [
          'totalCount' => 1,
          'totalValue' => 0,
          'totalPaid' => 0,
          'totalBalance' => 0,
          'totalQty' => 0,
          'totalElectrician' => 0,
          'name' => 'On Hold',
          'productList' => []
        ]
      ];
      $totalSummary = [
        'totalCount' => count($orderList),
        'totalValue' => 0,
        'totalPaid' => 0,
        'totalBalance' => 0,
        'totalQty' => 0,
        'totalElectrician' => 0
      ];
      $currentDateTime = new DateTime();
      foreach ($orderList as $order) {
        $result = $order->result;
        $product = $order->product->name;
        $sold = $order->lead->sold ?? 0;
        $paid = $order->payments ? $order->payments->sum('amount') : 0;
        \Log::debug($order->id);
        $client = $order->lead->client;
        $date = $currentDateTime;
        $days = 0;
        if ($result == Order::RESULT_NEW) {
          $date = $order->lead->sold_date;
        } else if ($result == Order::RESULT_CM) {
          $date = $order->cm_booked_date;
        } else if ($result == Order::RESULT_CM_RECEIVED) {
          $date = $order->cm_received_date;
        } else if ($result == Order::RESULT_ON_PRODUCTION) {
          $date = $order->on_product_date;
        } else if ($result == Order::RESULT_READY) {
          $date = $order->ready_date;
        } else if ($result == Order::RESULT_INSTALLATION) {
          $date = $order->installation_date;
        } else if ($result == Order::RESULT_OUTSTANDING) {
          $date = $order->installed_date;
        } else if ($result == Order::RESULT_HOLD) {
          $date = $order->hold_time;
        } else if ($result == Order::RESULT_IN_COUNCIL) {
          $date = $order->submit_date;
        }
        $productList = $order->productSpec;
        $extra = '';
        foreach ($productList as $specItem) {
          $spec = $specItem->spec;
          if (isset($spec['Extras']) && $spec['Extras'] == 'Yes') {
            $extra = 'Yes';
          }
        }
        if (!$date) {
          $date = $currentDateTime;
        } else if (is_string($date)) {
          $date = new DateTime($date);
        }
        $days = $currentDateTime->diff($date)->days;
        if (isset($data[$result]['productList'][$product])) {
          $data[$result]['productList'][$product]['data'][] = [
            'rowspan' => 0,
            'firstRowSpan' => 0,
            'leadId' => $order->lead->id,
            'dayCount' => intval($days),
            'surname' => $client ? $client->given_name . ' ' . $client->surname  : '',
            'suburb' => $client ? $client->suburb : '',
            'value' => $sold,
            'paid' => $paid,
            'balance' => round($sold - $paid, 2),
            'qty' => $order->qty,
            'electrician' => $order->electrician,
            'extra' => $extra
          ];
          $data[$result]['productList'][$product]['totalValue'] += $sold;
          $data[$result]['productList'][$product]['totalPaid'] += $paid;
          $data[$result]['productList'][$product]['totalBalance'] += $sold - $paid;
          $data[$result]['productList'][$product]['totalQty'] += $order->qty;
          $data[$result]['productList'][$product]['totalElectrician'] += $order->electrician;
          $data[$result]['productList'][$product]['totalCount'] += 1;
        } else {
          $firstRowSpan = count($data[$result]['productList']) ? 0 : 1;
          $data[$result]['productList'][$product] = [
            'data' => [
              [
                'rowspan' => 0,
                'firstRowSpan' => 0,
                'firstRowSpanFlag' => $firstRowSpan,
                'leadId' => $order->lead->id,
                'dayCount' => intval($days),
                'surname' => $client ? $client->given_name . ' ' . $client->surname : '',
                'suburb' => $client ? $client->suburb : '',
                'value' => $sold,
                'paid' => $paid,
                'balance' => round($sold - $paid, 2),
                'qty' => $order->qty,
                'electrician' => $order->electrician,
                'extra' => $extra
              ]
            ],
            'firstRowSpan' => 0,
            'totalCount' => 1,
            'totalValue' => $sold,
            'totalPaid' => $paid,
            'totalBalance' => round($sold - $paid, 2),
            'totalQty' => $order->qty,
            'totalElectrician' => $order->electrician
          ];
        }
      }

      foreach ($data as $key => $item) {
        $total = 0;
        foreach($item['productList'] as $k => $product) {
          $total += $product['totalCount'] + 1;
          $data[$key]['totalValue'] += $product['totalValue'];
          $data[$key]['totalPaid'] += $product['totalPaid'];
          $data[$key]['totalBalance'] += $product['totalBalance'];
          $data[$key]['totalQty'] += $product['totalQty'];
          $data[$key]['totalElectrician'] += $product['totalElectrician'];

          $totalSummary['totalValue'] += $product['totalValue'];
          $totalSummary['totalPaid'] += $product['totalPaid'];
          $totalSummary['totalBalance'] += $product['totalBalance'];
          $totalSummary['totalQty'] += $product['totalQty'];
          $totalSummary['totalElectrician'] += $product['totalElectrician'];
          if (isset($data[$key]['productList'][$k]['data'])) {
            $firstRowSpanFlag = $data[$key]['productList'][$k]['data'][0]['firstRowSpanFlag'];
            usort($data[$key]['productList'][$k]['data'], function($a, $b) {
              return $b['dayCount'] <=> $a['dayCount']; // 按年龄升序排序，保留键名
            });
            $data[$key]['productList'][$k]['data'][0]['rowspan'] = 1;
            $data[$key]['productList'][$k]['data'][0]['firstRowSpan'] = $firstRowSpanFlag;
          }
        }
        if ($total) {
          $data[$key]['totalCount'] = $total;
        }
      }

      // \Log::debug($data);
      try {
        $pdf = App::make('snappy.pdf.wrapper')->setPaper('a4')->setOrientation('portrait');
        $pdf = $pdf->loadView('pdf.orderReportTemplate', [
                  'data' => $data,
                  'totalSummary' => $totalSummary
                  ])
                    ->setOption('orientation', 'landscape');
        $folder      = storage_path('app') . '/public/pdf/orderReport/';
        if (!\Illuminate\Support\Facades\File::exists($folder)) {
          \Illuminate\Support\Facades\File::makeDirectory($folder, 0775, true); //creates directory
        }
        $fileName = "orderReport_" . date("YmdHi") . ".pdf";
        $path = $folder . $fileName;
        $url = "pdf/orderReport/" . $fileName;
        if (is_file($path)) {
          $publicUrl = $this->getPublicUrl($url);
        } else {
          $pdf->Output($path, 'F');
          $pdf->save($path);
          $publicUrl = $this->getPublicUrl($url);
        }
        return $this->sendResponse(["url" => $publicUrl], 'Generate Warranty Card successfully.');
      } catch (\Exception $e) {
        \Log::error('Generate Generate order report error: ' . $e->getMessage());
        return $this->sendResponse(null, 'Failed to generate order report!', 500);
      }
  }
  /**
  * @OA\Get(
  *      path="/admin/Admin/Order/revokeOrder/{id}",
  *      operationId="OrderController::revokeOrder",
  *      tags={"Admin.Order"},
  *      summary="",
  *      description="TODO OrderController::revokeOrder ",
  *      security={{"api_http_auth": {}}},
  *      @OA\Parameter(name="id", description="id", required=true, in="path",
  *          @OA\Schema(type="integer")
  *      ),
  *      @OA\Response(
  *          response=200,
  *          description="successful operation"
  *      ),
  *      @OA\Response(response=400, description="Bad request")
  *  )
  *
  * @param int $id
  * @param OrderRevokeOrderRequest $request
  * @return JsonResponse
  * @throws \Illuminate\Auth\Access\AuthorizationException
  */
  public function revokeOrder($id, OrderRevokeOrderRequest $request)
  {
      /**
       * @var $item Order
       */
      $item = $this->repository->findWithoutFail($id);
      if (empty($item)) {
          return $this->sendError('Order not found');
      }
      if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
        return $this->sendError("You can not revoke other company order");
      }
      $this->authorize('revokeOrder', $item);
      $item->result = Order::RESULT_OUTSTANDING;
      $item->completion_date = null;
      $item->save();
      // TODO OrderController::revokeOrder
      return $this->sendResponse(null, 'revokeOrder processed successfully.');
  }
}
