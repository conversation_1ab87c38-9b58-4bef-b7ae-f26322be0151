<?php

namespace Modules\Admin\Controllers;

use Modules\Admin\Requests\Leads\LeadsRevokedLeadsRequest;
use App\Services\CommonService;
use Modules\Admin\Requests\Leads\LeadsMoveToBlacklistRequest;
use Modules\Admin\Requests\Leads\LeadsChangeSalesConsultantRequest;
use EasyWeChat\Kernel\Contracts\Jsonable;
use Modules\Admin\Requests\Leads\LeadsGetSummaryInfoRequest;
use Modules\Admin\Requests\Leads\LeadsConvertRequest;
use Modules\Admin\Requests\Leads\LeadsChangeResultRequest;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Scaffold\QueryBuilder\QueryBuilder;
use Scaffold\BaseController;
use Modules\Admin\Requests\Leads\LeadsUpdateRequest;
use Modules\Admin\Requests\Leads\LeadsListRequest;
use Modules\Admin\Requests\Leads\LeadsGetRequest;
use Modules\Admin\Repositories\LeadsRepository;
use Modules\Admin\Resources\LeadsResource;
use Modules\Admin\Resources\LeadsCollection;
use Modules\Admin\Models\Leads;
use Illuminate\Http\JsonResponse;
use Modules\Admin\Models\Clients;
use Modules\Admin\Models\Notes;
use Modules\Admin\Models\Payment;
use Illuminate\Support\Facades\DB;
use Modules\Admin\Models\Account;
use Modules\Admin\Models\Appointment;
use Modules\Admin\Models\ClientsBlackList;
use Modules\Admin\Models\ClientsExistingProduct;
use Modules\Admin\Models\DraftLeads;
use Modules\Admin\Models\Order;
use Modules\Admin\Models\Products;
use App\Services\KlaviyoService;
use App\Services\FacebookService;

class LeadsController extends BaseController
{
    public function __construct(LeadsRepository $repository)
    {
        $this->repository = $repository;
    }
    /**
     * @OA\Get(
     *      path="/admin/leads",
     *      operationId="LeadsController::index",
     *      tags={"Admin.Leads"},
     *      summary="Get list of Leads",
     *      description="Returns list of Leads",
     *      security={{"api_http_auth": {}}},
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Global search term. Use `%` to wildcard search.",
     *         @OA\Schema(
     *             type="string",
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="filter",
     *         in="query",
     *         description="field_name:operator:value. Available operators: `gt` `gte` `lt` `lte` `eq` `ne` `neq` `like` `in` `nin` `null` `not-null` `true` `false`
     *      Example: `age:gt:18` `status:in:pending|in_progress`",
     *         @OA\Schema(
     *              type="string",
     *                pattern="^.*:(gt|gte|lt|lte|eq|ne|neq|like|in|nin|null|not\-null|true|false):.*$"
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="sort",
     *         in="query",
     *
     *         description="Sorting fields. Leading minus as DESC sorting.",
     *         @OA\Schema(
     *             type="string",default="-id",
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="with",
     *         in="query",
     *         description="Get relationships.",
     *         @OA\Schema(
     *              type="string"
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="with_count",
     *         in="query",
     *         description="Get the count number of relationships.",
     *         @OA\Schema(
     *             type="string"
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number. Start from 1.",
     *         @OA\Schema(
     *             type="integer", default=1
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="size",
     *         in="query",
     *         description="Items per page.",
     *         @OA\Schema(
     *             type="integer", default=10 ,
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="other_param",
     *         in="query",
     *         description="filter by the sub category ids of leads",
     *         @OA\Schema(
     *             type="string"
     *         ),
     *         style="form"
     *     ),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/Admin.LeadsResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param LeadsListRequest $request
     * @return LeadsCollection
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function index(LeadsListRequest $request)
    {
        $input = $request->all();
        $this->authorize('viewAny', Leads::class);
        $query = QueryBuilder::for(Leads::class)->getQuery();
        if(isset($input["other_param"]) && $input["other_param"]){
          $otherParam = json_decode($input["other_param"]);
          foreach($otherParam as $k=>$v){
            $input[$k] = $v;
          }
        }
        if(isset($input["subCategoryIds"]) && $input["subCategoryIds"]){
          // product filter
          $subCategoryIds = $input["subCategoryIds"];
          if($subCategoryIds){
            $ids = explode(',',$subCategoryIds);
            if($ids&&count($ids)){
              $where = [];
              foreach($ids as $id){
                $where[]="leads.sub_category_ids LIKE '%,{$id},%'";
                $where[]="leads.sub_category_ids LIKE '%,{$id}'";
                $where[]="leads.sub_category_ids LIKE '{$id},%'";
                $where[]="leads.sub_category_ids = '{$id}'";
              }
              $where=implode(' or ',$where);
              $query->whereRaw("({$where})");
            }
          }
        }
        // client filter
        if(isset($input["clientKeyWords"]) && $input["clientKeyWords"]){
          $clientInfo ="%{$input["clientKeyWords"]}%";
          $query->join('clients', 'clients.id', '=', 'leads.client_id')
          ->whereRaw("(clients.surname LIKE ? or clients.phone LIKE ? or clients.given_name LIKE ? or clients.address LIKE ? or clients.sec_phone LIKE ?)",[$clientInfo,$clientInfo,$clientInfo,$clientInfo,$clientInfo]);
        }
        if(isset($input["subSource"]) && $input["subSource"]){
          $subSourceInfo="%{$input["subSource"]}%";
          $query->join('source', 'source.id', '=', 'leads.sub_source_id')
          ->whereRaw("source.name LIKE ?", [$subSourceInfo]);
        }
        if (!$request->user()->isPlatformUser()) {
          $query->where('leads.company_region', $request->user()->company_region);
        } else if (!$request->user()->isSuperAdmin()) {
          $companyRegions = explode(',', $request->user()->company_ids);
          $query->whereIn('leads.company_region', $companyRegions);
        }
        $paginated = QueryBuilder::paginate($query);
        $data = new LeadsCollection($paginated);
        return $data;
    }
    /**
     * @OA\Get(
     *      path="/admin/leads/{id}",
     *      operationId="LeadsController::show",
     *      tags={"Admin.Leads"},
     *      summary="Display the specified Leads",
     *      description="Get one Leads by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Leads id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/with"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Fetched record.",
     *              required={"data"},
     *              @OA\Property(property="data", ref="#/components/schemas/Admin.LeadsResource")
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param LeadsGetRequest $request
     * @return JsonResponse|LeadsResource
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function show($id, LeadsGetRequest $request)
    {
        /**
         * @var $item Leads
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Leads not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not view other company leads');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'view the leads');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('view', $item);
        $item->load($request->getWith());
        return new LeadsResource($item);
    }
    /**
     * @OA\Put(
     *      path="/admin/leads/{id}",
     *      operationId="LeadsController::update",
     *      tags={"Admin.Leads"},
     *      summary="Update the specified Leads in storage.",
     *      description="Update one Leads by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Leads id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          description="Payload to update Leads",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/Admin.Leads")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param LeadsUpdateRequest $request
     *
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function update($id, LeadsUpdateRequest $request)
    {
        /**
         * @var $item Leads
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Leads not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not update other company leads');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'update the leads');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('update', $item);
        $input = $request->all();
        $client = $input['client'];
        // check the client with the phone 
        $oldClient =  Clients::where('phone', $input['phone'])
                               ->where('id', '<>', $input['client_id'])
                               ->first();
        if ($oldClient ) { 
          $input['client_id'] = $oldClient->id;
        } 
        
        unset($client['id']);
        unset($input['result']);
        unset($input['contracts']);
        Clients::where('id', $input['client_id'])->update($client);
        
        if (empty($input['sub_category_ids'])) {
          switch ($input['product_category_id']) {
            case 1: $input['sub_category_ids'] = 1;break;
            case 2: $input['sub_category_ids'] = 3;break;
            case 3: $input['sub_category_ids'] = 7;break;
            case 4: $input['sub_category_ids'] = 11;break;
            case 5: $input['sub_category_ids'] = 13;break;
          }
        }
        $this->repository->update($input, $id);
        activity_log()->withObject($item)->add('Update Leads', $input);
        // update oder 的product字段
        if($item->order && $item->result == 'sold'){
          $product_id = null;
          if($input["sub_category_ids"]){
            $sub_category_ids = explode(',', $input["sub_category_ids"]);
            $product_id = $sub_category_ids[0];
          }
          $item->order->product_id = $product_id;
          $item->order ->save();
        }
        // update existing product
        CommonService::updateExisting($item->client_id);
        return $this->sendResponse(null, 'Leads updated successfully.');
    }
    /**
    * @OA\Put(
    *      path="/admin/leads/changeResult/{id}",
    *      operationId="LeadsController::changeResult",
    *      tags={"Admin.Leads"},
    *      summary="",
    *      description="TODO LeadsController::changeResult ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.LeadsChangeResultRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param LeadsChangeResultRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function changeResult($id, LeadsChangeResultRequest $request)
    {
        /**
         * @var $item Leads
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Leads not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not change the result for other company leads');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'change the result for the leads');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $input = $request->all();
        $result = $input['result'];
        $eventName = '';
        switch($result){
          case "followup":
            $this->authorize('followupLeads', $item);
            $eventName = 'In progress';
          break;
          case "cancelled":
            $this->authorize('cancelLeads', $item);
            $eventName = 'Not qualified';
          break;
          default:
            $this->authorize('changeResult', $item);
        }
        if ($result == 'quoted') {
          $eventName = 'Converted';
        }
        if ($result == 'sold') {
          $eventName = 'Complete';
        }
        if ($result == 'followup' && (!isset($input['followup_time']) || !$input['followup_time'])) {
          return $this->sendError('Please enter the follow up date');
        }
        if ($result == 'sold' && (!isset($input['sold']) || !$input['sold'])) {
          return $this->sendError('Please enter the sold amount');
        }

        if ($result == 'quoted' && (!isset($input['quoted']) || !$input['quoted'])) {
          return $this->sendError('Please enter the quoted amount');
        }
        if (!isset($input['sub_result'])) {
          $input['sub_result'] = null;
        }
        $comment = null;
        if (isset($input['comment'])) {
          $comment = $input['comment'];
          unset($input['comment']);
        }
        if ($item->result == Leads::RESULT_NEW && !$request->user()->isSuperAdmin() && !$request->user()->isSalesManager())
        {
          $input['appt_setter_id'] = $request->user()->id;
        }

        // update sold_date
        if($result == leads::RESULT_SOLD && empty($item->sold_date)){
          $input['sold_date'] = isset($input['sold_date']) && $input['sold_date'] ? $input['sold_date'] : date("Y-m-d H:i:s");
        }
        // we need cancel all the appointment
        if ($result == 'cancelled') {
          Appointment::where('object_id', $id)
          ->where('type','sale')
          ->whereNotIn('status', [Appointment::STATUS_COMPLETED, Appointment::STATUS_CANCEL])
          ->update(['status' => Appointment::STATUS_CANCEL, 'user_id' => null]);
          // cancel related order
          $order = $item->order;
          if ($order) {
            $order->result = Order::RESULT_CANCELLED;
            $order->save();
            note_log()->withOrder($order->id)->withResult($input['result'])->add("Change the result to '" . $order->result. "' for canceling the associated leads");
          }
        }

        // Cancel the appointment if the leads status changes to followup
        if($item->result == leads::RESULT_APPOINTED && $result == 'followup'){
          Appointment::where('object_id', $id)
          ->where('type','sale')
          ->whereNotIn('status', [Appointment::STATUS_COMPLETED, Appointment::STATUS_CANCEL])
          ->update(['status' => Appointment::STATUS_CANCEL, 'user_id' => null]);
        }
        // complete the appointmnet if leads status change to sold or quoted
        if($result == leads::RESULT_QUOTED || $result == leads::RESULT_SOLD){
          Appointment::where('object_id', $id)
          ->where('type','sale')
          ->whereNotIn('status', [Appointment::STATUS_COMPLETED, Appointment::STATUS_CANCEL])
          ->update(['status' => Appointment::STATUS_COMPLETED]);
        }
        if ($result == leads::RESULT_SOLD) {
          $subCategoryIds = explode(',', $item->sub_category_ids);
          foreach ($subCategoryIds as $subCategoryId) {
            ClientsExistingProduct::updateOrCreate(
              ['client_id' => $item->client_id, 'product_id' => $subCategoryId],
              ['client_id' => $item->client_id, 'product_id' => $subCategoryId]
            );
          }
        }
        $this->repository->update($input , $id);
        note_log()->withLeads($id)->withResult($result)->add("Change the result to '" . $input['result']. "'", $comment);
        // TODO LeadsController::changeResult
        // update klaviyo profile
        KlaviyoService::UpdateOrCreateProfile($item->client);
        // create facebook event
        FacebookService::createEvent($item, $eventName);
        return $this->sendResponse(null, 'changeResult processed successfully.');
    }
    /**
    * @OA\Put(
    *      path="/admin/leads/convert/{id}",
    *      operationId="LeadsController::convert",
    *      tags={"Admin.Leads"},
    *      summary="",
    *      description="TODO LeadsController::convert ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.LeadsConvertRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param LeadsConvertRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function convert($id, LeadsConvertRequest $request)
    {
        /**
         * @var $item Leads
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Leads not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not convert other company leads');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'convert the leads');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('convert', $item);
        if ($item->result != Leads::RESULT_SOLD) {
          return $this->sendError('Leads cannot be converted to order!');
        }
        if ($item->order_id) {
          return $this->sendError('Leads has been converted to order!');
        }
        $input = $request->input();
        $input['name'] = 'Deposit';

        // get order product_id
        $product_id = null;
        if($item->sub_category_ids){
          $sub_category_ids = explode(',', $item->sub_category_ids);
          if($sub_category_ids){
            if(count($sub_category_ids)>1){
              return $this->sendError('When converting leads to orders, please ensure only one product is selected!');
            }
            $product_id =  $sub_category_ids[0];
          }
        }
        try {
          DB::beginTransaction();
          $sold = $item->sold;
          $retail = $item->retail;
          $num = (($retail - $sold) / $retail) * 100;
          $USell=(float) number_format($num, 1, '.', '');
          if($item->stagnation_order_id){
              //Restore the old order Info
             $order = Order::withTrashed()->find($item->stagnation_order_id);
             if(empty($order)){
              // create new order
              $orderInfo = [
                'lead_id' => $id,
                'received_date' => date("Y-m-d H:i:s"),
                'result' => Order::RESULT_NEW,
                'under_sell' => $USell,
                'estimated_comm' => $item->estimated_comm,
                'transno' => $item->transno,
                'from_old' => $item->from_old,
                'product_id' => $product_id,
                'company_region' => $item->company_region
              ];
              $order = Order::create($orderInfo);
              // Add note
              note_log()->withOrder($order->id)->withResult(Notes::ICON_NEW)->add("Convert the lead '" . $id . " ' to Order");
             } else {

              $order->is_stagnation = 0;
              $order->deleted_at = null;
              $order->result = Order::RESULT_NEW;
              $order->received_date = date("Y-m-d H:i:s");
              $order->under_sell = $USell;
              $order->estimated_comm = $item->estimated_comm;
              $order->product_id = $product_id;
              $order->save();
              note_log()->withOrder($order->id)->withResult(Notes::ICON_NEW)->add("Convert the lead '" . $id . " ' to order and restore order data.");
             }

             // update last stagnation orde Deposit info
             if(isset($input["old_payment_id"])){
              $payment = Payment::find($input["old_payment_id"]);
              $payment ->amount = $input["amount"];
              $payment ->detail = $input["detail"];
              $payment ->received_date = $input["received_date"];
              $payment ->order_id = $order->id;
              $payment ->type = $input["type"];
              $payment ->save();
             }
             if(!isset($input["old_payment_id"]) || empty($payment)){
              $input['order_id'] = $order->id;
              unset($input['id']);
              if(isset($input["old_payment_id"])){
                unset($input['old_payment_id']);
              }
              $input['name'] = 'Deposit';
              $input['company_region'] = $item->company_region;
              Payment::create($input);
             }
          }else{
            // create new order
            $orderInfo = [
              'lead_id' => $id,
              'received_date' => date("Y-m-d H:i:s"),
              'result' => Order::RESULT_NEW,
              'under_sell' => $USell,
              'estimated_comm' => $item->estimated_comm,
              'transno' => $item->transno,
              'from_old' => $item->from_old,
              'product_id' => $product_id,
              'company_region' => $item->company_region
            ];
            $order = Order::create($orderInfo);
            // Add note
            note_log()->withOrder($order->id)->withResult(Notes::ICON_NEW)->add("Convert the lead '" . $id . " ' to Order");
            $input['order_id'] = $order->id;
            unset($input['id']);
            $input['name'] = 'Deposit';
            $input['company_region'] = $item->company_region;
            Payment::create($input);
          }
        $leadsUpdateInfo=['order_id' => $order->id,"stagnation_order_id"=>null];
        $this->repository->update($leadsUpdateInfo, $id);

        // close all appointment of this leads
        $cancelRes=Appointment::where('object_id', $id)->whereNotIn('status', [Appointment::STATUS_COMPLETED, Appointment::STATUS_CANCEL])->update(['status' => Appointment::STATUS_CANCEL]);
          if($cancelRes){
            note_log()->withLeads($id)->withComment()->add("When converting lead to order, cancel all Sales type appointments");
          }
          DB::commit();
        } catch (\Exception $exception) {
          DB::rollBack();
          \Log::error('Error convert lead to order: ' . $exception->getMessage());
          return $this->sendError('Convert lead to order fail.');
        }
        // TODO LeadsController::convert
        return $this->sendResponse(null, 'convert processed successfully.');
    }
    /**
    * @OA\Get(
    *      path="/admin/leads/get_summary/info",
    *      operationId="LeadsController::getSummaryInfo",
    *      tags={"Admin.Leads"},
    *      summary="",
    *      description="TODO LeadsController::getSummaryInfo ",
    *      @OA\Parameter(
    *         name="company_region",
    *         in="query",
    *         description="filter by company_region",
    *         @OA\Schema(
    *             type="string"
    *         ),
    *         style="form"
    *     ),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param LeadsGetSummaryInfoRequest $request
    * @return JsonResponse
    */
    public function getSummaryInfo(LeadsGetSummaryInfoRequest $request)
    {
      $newQuery = Leads::where('result', Leads::RESULT_NEW);
      $followupQuery = Leads::where('result', 'followup');
      $appointedQuery = Leads::where('result', 'appointed');
      $quotedQuery = Leads::where('result', 'quoted');
      $soldQuery = Leads::where('result', 'sold');
      $cancelledQuery = Leads::where('result', 'cancelled');
      $allQuery = Leads::query();
      $draftQuery = DraftLeads::where('status','active');
      $input = $request->all();
      if (!$request->user()->isSuperAdmin() && $request->user()->isPlatformUser()) {
        $input["company_region"] = $request->user()->company_ids;
      }
      if (!$request->user()->isPlatformUser()) {
        $companyRegion = $request->user()->company_region;
        $newQuery->whereIn('company_region',  [$companyRegion])->where('result', Leads::RESULT_NEW);
        $followupQuery->whereIn('company_region',  [$companyRegion])->where('result', 'followup');
        $appointedQuery->whereIn('company_region',  [$companyRegion])->where('result', 'appointed');
        $quotedQuery->whereIn('company_region',  [$companyRegion])->where('result', 'quoted');
        $soldQuery->whereIn('company_region',  [$companyRegion])->where('result', 'sold');
        $cancelledQuery->whereIn('company_region',  [$companyRegion])->where('result', 'cancelled');
        $allQuery->whereIn('company_region',  [$companyRegion]);
        $draftQuery->where(function ($query) use($companyRegion){
                                      $query->whereIn('company_region', [$companyRegion, ''])
                                            ->orWhereNull('company_region');
                                      }
                                    )->where('status','active');
      } else if(isset($input["company_region"]) && $input["company_region"]){
          $regions = explode(',', $input["company_region"]);
          // $companyRegion = $request->user()->company_region;
          $newQuery->whereIn('company_region',  $regions)->where('result', Leads::RESULT_NEW);
          $followupQuery->whereIn('company_region',  $regions)->where('result', 'followup');
          $appointedQuery->whereIn('company_region',  $regions)->where('result', 'appointed');
          $quotedQuery->whereIn('company_region',  $regions)->where('result', 'quoted');
          $soldQuery->whereIn('company_region',  $regions)->where('result', 'sold');
          $cancelledQuery->whereIn('company_region',  $regions)->where('result', 'cancelled');
          $allQuery->whereIn('company_region',  $regions);
          $draftQuery->where(function ($query) use($regions){
                                        $query->whereIn('company_region', $regions)
                                              ->orWhereNull('company_region');
                                        }
                                      )->where('status','active');
      }
      $data['new'] = $newQuery->count();
      $data['followup'] = $followupQuery->count();
      $data['appointed'] = $appointedQuery->count();
      $data['quoted'] = $quotedQuery->count();
      $data['sold'] = $soldQuery->count();
      $data['cancelled'] = $cancelledQuery->count();
      $data['all'] = $allQuery->count();
      $data['draft'] = $draftQuery->count();
      return $this->sendResponse($data, 'getSummaryInfo processed successfully.');
    }
    /**
    * @OA\Put(
    *      path="/admin/leads/changeSalesConsultant/{id}",
    *      operationId="LeadsController::changeSalesConsultant",
    *      tags={"Admin.Leads"},
    *      summary="",
    *      description="Change Leads SalesConsultant",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.LeadsChangeSalesConsultantRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param LeadsChangeSalesConsultantRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function changeSalesConsultant($id, LeadsChangeSalesConsultantRequest $request)
    {
        /**
         * @var $item Leads
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Leads not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not change the sales consultant for other company leads');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, ' change the sales consultant for the leads');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $input = $request->all();
        $userId = $input['user_id'];
        if($userId){
          $user = Account::find($userId);
        }else{
          $user = null;
        }
        $item->sales_consultant_id = $userId;
        $item->save();
        $leadsNoteIcon =  Notes::ICON_USER;
        // change appointment userid
        $lastSaleAppt = Appointment::where('object_id', $id)
        ->where('type', 'sale')
        ->orderBy('id','desc')
        ->first();
        if($lastSaleAppt && $user){
          if (!$user->isAppointmentAssign()) {
             $msg = 'Not the correct person to assign to the appointment';
             return $this->sendResponse(null, $msg);
          } else {
            $lastSaleAppt->user_id = $userId;
            if($lastSaleAppt->status != Appointment::STATUS_COMPLETED && $lastSaleAppt->status != Appointment::STATUS_CANCEL ){
              $lastSaleAppt->is_notify_assign = null;
              $lastSaleAppt->is_assign_received = null;
              $lastSaleAppt->assign_received_date = null;
              $lastSaleAppt->status = Appointment::STATUS_SCHEDULED;
            }
            $lastSaleAppt->save();
          }
          note_log()->withLeads($id)->withActivity(Notes::ICON_APPT)->add("Assign the appointment to " . $user->name);
        }else if($lastSaleAppt){
          $lastSaleAppt->user_id = null;
          $lastSaleAppt->is_notify_assign = null;
          $lastSaleAppt->is_assign_received = null;
          $lastSaleAppt->assign_received_date = null;
          $lastSaleAppt->status = Appointment::STATUS_SCHEDULED;
          $lastSaleAppt->save();
          note_log()->withLeads($id)->withActivity(Notes::ICON_APPT)->add("Remove the sales consultant of the lead. ");
        }else{
          if($userId){
            $noteMsg = "Change the sales consultant of the lead to" . $user->name;
          }else{
            $noteMsg = "Remove the sales consultant of the lead. ";
          }
          note_log()->withLeads($id)->withActivity($leadsNoteIcon)->add($noteMsg);
        }

        $this->authorize('changeSalesConsultant', $item);
        return $this->sendResponse(null, 'Change Sales Consultant processed successfully.');
    }
    /**
    * @OA\Post(
    *      path="/admin/Admin/Leads/MoveToBlacklist/{id}",
    *      operationId="LeadsController::moveToBlacklist",
    *      tags={"Admin.Leads"},
    *      summary="",
    *      description="TODO LeadsController::moveToBlacklist ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="leads", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.LeadsMoveToBlacklistRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param LeadsMoveToBlacklistRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function moveToBlacklist($id, LeadsMoveToBlacklistRequest $request)
    {
        /**
         * @var $item Leads
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Leads not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not move other company leads to blacklist');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'move the leads to blacklist');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('moveToBlacklist', $item);
        // TODO LeadsController::moveToBlacklist
        $input = $request->input();
        try {
          DB::beginTransaction();
          $client = $item->client;
          $client->is_in_blacklist = true;
          $client->save();
          $data = [
            'client_id' => $client->id,
            'created_by' => $request->user()->id,
            'reason' => $input['reason'],
            'lead_id' => $id,
            'type' => 'in'
          ];
          ClientsBlackList::updateOrCreate(
            ['lead_id' => $id],
            $data
          );
          DB::commit();
        } catch (\Exception $exception) {
          DB::rollBack();
          \Log::error('Error move client to blacklist: ' . $exception->getMessage());
          return $this->sendError('Move client to blacklist fail.');
        }
        return $this->sendResponse(null, 'moveToBlacklist processed successfully.');
    }
    /**
    * @OA\Post(
    *      path="/admin/Admin/Leads/RevokedLeads/{id}",
    *      operationId="LeadsController::revokedLeads",
    *      tags={"Admin.Leads"},
    *      summary="",
    *      description="TODO LeadsController::revokedLeads ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.LeadsRevokedLeadsRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param LeadsRevokedLeadsRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function revokedLeads($id, LeadsRevokedLeadsRequest $request)
    {
        /**
         * @var $item Leads
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Leads not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not revoke other company leads from blacklist');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'revoke the leads');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('revokedLeads', $item);
        $item->result = Leads::RESULT_FOLLOWUP;
        $item->followup_time = date("Y-m-d H:i:s");
        $item->save();
        note_log()->withLeads($item->id)->withActivity(Notes::ICON_USER)->add('Revoked canceled leads and change result to followup.');
        // update klaviyo profile
        KlaviyoService::UpdateOrCreateProfile($item->client);
        FacebookService::createEvent($item, 'In progress');
        // TODO LeadsController::revokedLeads
        return $this->sendResponse(null, 'Revoked Canceled Leads processed successfully.');
    }
}