<?php

namespace Modules\Admin\Controllers;

use Modules\Admin\Requests\Appointment\AppointmentSyncCalendarRequest;
use Modules\Admin\Requests\Appointment\AppointmentRevokeCancelRequest;
use Modules\Admin\Requests\Appointment\AppointmentConfirmRequest;
use Modules\Admin\Requests\Appointment\AppointmentAssignAcceptRequest;
use Modules\Admin\Requests\Appointment\AppointmentNotifyAssignRequest;
use Modules\Admin\Requests\Appointment\AppointmentSendToGoogleCalendarRequest;
use Modules\Admin\Requests\Appointment\AppointmentCompleteRequest;
use Modules\Admin\Requests\Appointment\AppointmentCancelRequest;
use App\Mail\Email;
use App\Services\CalendarService;
use App\Services\GoogleCalendarService;
use App\SMS\SMS;
use Modules\Admin\Requests\Appointment\AppointmentNotifyClientRequest;
use Modules\Admin\Requests\Appointment\AppointmentAssignRequest;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Scaffold\QueryBuilder\QueryBuilder;
use Scaffold\BaseController;
use Modules\Admin\Requests\Appointment\AppointmentUpdateRequest;
use Modules\Admin\Requests\Appointment\AppointmentListRequest;
use Modules\Admin\Requests\Appointment\AppointmentGetRequest;
use Modules\Admin\Requests\Appointment\AppointmentDeleteRequest;
use Modules\Admin\Requests\Appointment\AppointmentCreateRequest;
use Modules\Admin\Repositories\AppointmentRepository;
use Modules\Admin\Resources\AppointmentResource;
use Modules\Admin\Resources\AppointmentCollection;
use Modules\Admin\Models\Appointment;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Mail;
use Modules\Admin\Models\Account;
use Modules\Admin\Models\Leads;
use Modules\Admin\Models\Notes;
use Modules\Admin\Models\Order;
use Modules\Admin\Models\Service;
use Spatie\GoogleCalendar\Event as GoogleCalendarEnvent;
use Carbon\Carbon;
use Exception;
use Google\Service\Calendar\Calendar;
use Spatie\GoogleCalendar\GoogleCalendar;
use Illuminate\Http\Request;
use Modules\Admin\Models\ClientsExistingProduct;
use App\Services\KlaviyoService;
use App\Services\FacebookService;

// use Laravel\Socialite\Facades\Socialite;

class AppointmentController extends BaseController
{
    public function __construct(AppointmentRepository $repository)
    {
        $this->repository = $repository;
    }
    /**
     * @OA\Get(
     *      path="/admin/appointment",
     *      operationId="AppointmentController::index",
     *      tags={"Admin.Appointment"},
     *      summary="Get list of Appointments",
     *      description="Returns list of Appointments",
     *      security={{"api_http_auth": {}}},
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Global search term. Use `%` to wildcard search.",
     *         @OA\Schema(
     *             type="string",
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="filter",
     *         in="query",
     *         description="field_name:operator:value. Available operators: `gt` `gte` `lt` `lte` `eq` `ne` `neq` `like` `in` `nin` `null` `not-null` `true` `false`
     *      Example: `age:gt:18` `status:in:pending|in_progress`",
     *         @OA\Schema(
     *              type="string",
     *                pattern="^.*:(gt|gte|lt|lte|eq|ne|neq|like|in|nin|null|not\-null|true|false):.*$"
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="sort",
     *         in="query",
     *
     *         description="Sorting fields. Leading minus as DESC sorting.",
     *         @OA\Schema(
     *             type="string",default="-id",
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="with",
     *         in="query",
     *         description="Get relationships.",
     *         @OA\Schema(
     *              type="string"
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="with_count",
     *         in="query",
     *         description="Get the count number of relationships.",
     *         @OA\Schema(
     *             type="string"
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number. Start from 1.",
     *         @OA\Schema(
     *             type="integer", default=1
     *         ),
     *         style="form"
     *     ),
     *     @OA\Parameter(
     *         name="size",
     *         in="query",
     *         description="Items per page.",
     *         @OA\Schema(
     *             type="integer", default=10 ,
     *         ),
     *         style="form"
     *     ),
     *      @OA\Parameter(
     *         name="sub_category_ids",
     *         in="query",
     *         description="filter by the sub category ids of leads",
     *         @OA\Schema(
     *             type="string"
     *         ),
     *         style="form"
     *     ),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Response of an index API.",
     *              required={"links","meta","data"},
     *              @OA\Property(property="links", ref="#/components/schemas/IndexResponseLinks"),
     *              @OA\Property(property="meta", ref="#/components/schemas/IndexResponseMeta"),
     *              @OA\Property(property="data", type="array", description="Data array.",
     *                   @OA\Items(ref="#/components/schemas/Admin.AppointmentResource")
     *              ),
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param AppointmentListRequest $request
     * @return AppointmentCollection
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function index(AppointmentListRequest $request)
    {
        $this->authorize('viewAny', Appointment::class);
        $query = QueryBuilder::for(Appointment::class)->getQuery();
        if ($request->user()->isAppointmentUser()) {
          $query->where('user_id', $request->user()->id);
        }
        $query->join('leads', 'appointment.object_id', '=', 'leads.id')
              ->whereNull('leads.deleted_at');
        $subCategoryIds = $request->input('sub_category_ids');
        if($subCategoryIds){
          $ids = explode(',',$subCategoryIds);
          if($ids&&count($ids)){
            $where = [];
            foreach($ids as $id){
              $where[]="leads.sub_category_ids LIKE '%,{$id},%'";
              $where[]="leads.sub_category_ids LIKE '%,{$id}'";
              $where[]="leads.sub_category_ids LIKE '{$id},%'";
              $where[]="leads.sub_category_ids = '{$id}'";
            }
            $where=implode(' or ',$where);
            $query->whereRaw("({$where})");
          }
        }

        if (!$request->user()->isPlatformUser()) {
          $query->where('appointment.company_region', $request->user()->company_region);
        }
        if ($request->user()->isPlatformUser() && !$request->user()->isSuperAdmin() && !$request->user()->isAppointmentUser()) {
          $companyRegions = explode(',', $request->user()->company_ids);
          $query->whereIn('appointment.company_region', array_merge($companyRegions, ['']));
        }
        $paginated = QueryBuilder::paginate($query);
        $data = new AppointmentCollection($paginated);
        return $data;
    }
    /**
     * @OA\Post(
     *      path="/admin/appointment",
     *      operationId="AppointmentController::store",
     *      tags={"Admin.Appointment"},
     *      summary="Store a newly created Appointment in storage.",
     *      description="Store a new Appointment",
     *      security={{"api_http_auth": {}}},
     *      @OA\RequestBody(
     *          description="Payload to create Appointment",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/Admin.Appointment")
     *      ),
     *      @OA\Response(response=200, description="Store complete",  @OA\JsonContent(ref="#/components/schemas/BaseStoreResponse200")),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param AppointmentCreateRequest $request
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function store(AppointmentCreateRequest $request)
    {
        $this->authorize('create', Appointment::class);
        $input = $request->all();
        /**
         * @var $item Appointment
         */
        $objectId =  $input['object_id'];
        $leads = Leads::with('client')->find($objectId);
        if (!$leads) {
          return $this->sendError('Please seletc an exit object');
        }
        $errorMessage = $this->checkPlatformCompany($request, $leads->company_region, 'create appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        if ($leads->result == 'cancelled') {
          return $this->sendError('The object has been closed, please select another one.');
        }
        if ($leads->order_id && !isset($input['type'])) {
          return $this->sendError('Please select the appointment type.');
        }
        if($input['type'] == 'sale' && $leads->result == 'sold'){
          $order = $leads->order;
          if($order && $order->result == Order::RESULT_COMPLETION) {
            return $this->sendError('The order under this lead has been completed, and this operation cannot be performed.');
          }
        }
        // Cancel current appointments
        Appointment::where('object_id', $leads->id)->whereNotIn('status', [Appointment::STATUS_COMPLETED, Appointment::STATUS_CANCEL])->update(['status' => Appointment::STATUS_CANCEL]);
        // Add new appointemnts
        $date = new \DateTime($input['date']);
        $date->setTime(date('H', strtotime($input['time'])), date('i', strtotime($input['time'])), 0);
        $input['date'] = $date->format('Y-m-d H:i:s');
        // if(isset($input['end_time']) && !empty($input['end_time'])){
        //   $endTime = new \DateTime($input['date']);
        //   $endTime->setTime(date('H', strtotime($input['end_time'])), date('i', strtotime($input['end_time'])), 0);
        //   $input['end_time'] = $endTime->format('Y-m-d H:i:s');
        // }
        $endDate = new \DateTime($input['endDate']);
        $endDate->setTime(date('H', strtotime($input['end_time'])), date('i', strtotime($input['end_time'])), 0);
        $input['end_time'] = $endDate->format('Y-m-d H:i:s');
        $input['client_id'] = $leads->client_id;
        $input['created_by'] = $request->user()->id;
        $input['company_region'] = $leads->company_region;
        $serviceId = isset($input['service_id']) ? $input['service_id'] : null;
        try {
          $item = $this->repository->create($input);
        } catch(\Exception $exception) {
          \Log::error('Fail to create appoint: ' . $exception->getMessage());
          return $this->sendError('Fail to create appoint');
        }
        if (!$item) {
          \Log::error('Fail to create appoint with the data ', $input);
          return $this->sendError('Fail to create appoint');
        }
        $oldResult = $leads->result;

        // the leads don't convert to order so change the result to appointment
        if (!$leads->order_id) {
          if ($oldResult == Leads::RESULT_NEW && !$request->user()->isSuperAdmin() && !$request->user()->isSalesManager())
          {
            $leads->appt_setter_id = $request->user()->id;
          }
          $leads->result = 'appointed';
          $leads->sub_result = null;
          $leads->appt_date = $input['date'];
          $leads->save();
          note_log()->withLeads($leads->id)->withActivity(Notes::ICON_APPT)->add('Create an appointment for ' . $input['date'] . ' and change the result to appointed.', isset($input['comment']) ? $input['comment'] : null );
          // update klaviyo profile
          KlaviyoService::UpdateOrCreateProfile($leads->client);
          FacebookService::createEvent($leads, 'Qualified');
        } else if($input['type'] == 'sale'&& $leads->result == 'sold'){
          note_log()->withOrder($order->id)->withActivity(Notes::ICON_APPT)->add('Create an sale appointment for ' . $input['date'] . ' and stop this order.', isset($input['comment']) ? $input['comment'] : null );
          // order
          $order = $leads->order;
          $order->is_stagnation = true;
          $order->deleted_at = date("Y-m-d H:i:s");
          $order->save();
          $leads->stagnation_order_id = $leads->order_id;
          $leads->order_id = null;
          $leads->result = 'appointed';
          $leads->sub_result = null;
          $leads->appt_date = $input['date'];
          $leads->save();
          note_log()->withLeads($leads->id)->withActivity(Notes::ICON_APPT)->add('Create an appointment for ' . $input['date'] . ' and change the result to appointed. Suspend the related order.', isset($input['comment']) ? $input['comment'] : null );
          // update klaviyo profile
          KlaviyoService::UpdateOrCreateProfile($leads->client);
          FacebookService::createEvent($leads, 'Qualified');
        } else if ($serviceId || $input['type'] == 'service') {
            if(empty($serviceId)){
              return $this->sendError('Not find the service order.');
            }
            $service = Service::where('id', $serviceId)->first();
            if (!$service || !in_array($service->result, [Service::RESULT_NEW, Service::RESULT_READY])) {
              return $this->sendError('Please confirm the service result or the service does not exist');
            }
            $service->result = Service::RESULT_INSTALLATION;
            $service->installation_date = $input['end_time'];
            $service->save();
            if (!empty($input['appt_duration'])) {
              note_log()->withService($service->id)->withComment()->add($input['appt_duration']);
            }
            note_log()->withService($service->id)->withActivity(Notes::ICON_APPT)->add('Create an appointment for ' . $input['date'] . ' and change the result to '.Service::RESULT_INSTALLATION.'.', isset($input['comment']) ? $input['comment'] : null );
        } else {
          $order = $leads->order;
          $order->last_result =  $order->result;
          if($order->result== Order::RESULT_COMPLETION || $order->result== Order::RESULT_CANCELLED){
            return $this->sendError("The current order status is ".$order->result." and therefore, creating a '".$input['type']."' type appointment is not allowed.");
          }
          if ($input['type'] == 'cm') {
            $order->result = Order::RESULT_CM;
            $order->cm_booked_date = $input['end_time'];
            note_log()->withOrder($order->id)->withActivity(Notes::ICON_APPT)->add('Create an appointment for ' . $input['date'] . ' and change the result to cm.', isset($input['comment']) ? $input['comment'] : null );
          } else if($input['type'] == 'install'){
            $order->result = Order::RESULT_INSTALLATION;
            $order->installation_date = $input['end_time'];
            note_log()->withOrder($order->id)->withActivity(Notes::ICON_APPT)->add('Create an appointment for ' . $input['date'] . ' and change the result to '.Order::RESULT_INSTALLATION.'.', isset($input['comment']) ? $input['comment'] : null );
          } else {
            return $this->sendError("The current order status is ".$order->result." and therefore, creating a '".$input['type']."' type appointment is not allowed.");
          }
          if (!empty($input['appt_duration'])) {
            note_log()->withOrder($order->id)->withComment()->add($input['appt_duration']);
          }
          $order->save();
        }
        activity_log()->withObject($item)->add('Create Appointment', $input);
        return $this->sendResponse($item->getKey(), 'Appointment saved successfully.');
    }
    /**
     * @OA\Get(
     *      path="/admin/appointment/{id}",
     *      operationId="AppointmentController::show",
     *      tags={"Admin.Appointment"},
     *      summary="Display the specified Appointment",
     *      description="Get one Appointment by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Appointment id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter(ref="#/components/parameters/with"),
     *      @OA\Response(response=200, description="successful operation",
     *          @OA\JsonContent(type="object", description="Fetched record.",
     *              required={"data"},
     *              @OA\Property(property="data", ref="#/components/schemas/Admin.AppointmentResource")
     *          ),
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     *
     * @param AppointmentGetRequest $request
     * @return JsonResponse|AppointmentResource
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function show($id, AppointmentGetRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not view other company  appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'view appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('view', $item);
        $item->load($request->getWith());
        return new AppointmentResource($item);
    }
    /**
     * @OA\Put(
     *      path="/admin/appointment/{id}",
     *      operationId="AppointmentController::update",
     *      tags={"Admin.Appointment"},
     *      summary="Update the specified Appointment in storage.",
     *      description="Update one Appointment by id",
     *      security={{"api_http_auth": {}}},
     *      @OA\Parameter(
     *          name="id",
     *          description="Appointment id",
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\RequestBody(
     *          description="Payload to update Appointment",
     *          required=true,
     *          @OA\JsonContent(ref="#/components/schemas/Admin.Appointment")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation"
     *      ),
     *      @OA\Response(response=400, description="Bad request")
     *  )
     *
     * @param int $id
     * @param AppointmentUpdateRequest $request
     *
     * @return JsonResponse
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function update($id, AppointmentUpdateRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not update other company  appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'update appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('update', $item);
        $input = $request->all();
        $date = new \DateTime($input['date']);
        $date->setTime(date('H', strtotime($input['time'])), date('i', strtotime($input['time'])), 0);
        $input['date'] = $date->format('Y-m-d H:i:s');
        // if(isset($input['end_time']) && !empty($input['end_time'])){
        //   $endTime = new \DateTime($input['date']);
        //   $endTime->setTime(date('H', strtotime($input['end_time'])), date('i', strtotime($input['end_time'])), 0);
        //   $input['end_time'] = $endTime->format('Y-m-d H:i:s');
        // }
        $endDate = new \DateTime($input['endDate']);
        $endDate->setTime(date('H', strtotime($input['end_time'])), date('i', strtotime($input['end_time'])), 0);
        $input['end_time'] = $endDate->format('Y-m-d H:i:s');

        if($item->end_time != $input['end_time'] || $item->date != $input['date']){
          $input["status"] = Appointment::STATUS_SCHEDULED;
        }
        unset($input['leads']);
        unset($input['client']);
        $this->repository->update($input, $id);
        if($item->type == 'sale' && $item->leads){
          $item->leads->appt_date = $input['date'];
          $item->leads->save();
          note_log()->withLeads($item->leads->id)->withComment()->add("Update appt date of leads to  " .$input['date'] );
          // update klaviyo profile
          KlaviyoService::UpdateOrCreateProfile($item->leads->client);
        }
        if ($item->type == 'cm') {
          $order = $item->leads->order;
          $order->cm_booked_date = $input['end_time'];
          $order->save();
          note_log()->withOrder($order->id)->withComment()->add("Update appt date of cm book to  " .$input['date'] );
        }
        if ($item->type == 'install') {
          $order = $item->leads->order;
          $order->installation_date = $input['end_time'];
          $order->save();
          note_log()->withOrder($order->id)->withComment()->add("Update appt date of installation to  " .$input['date'] );
        }
        if ($item->type == 'service') {
          $service = $item->service;
          $service->installation_date = $input['end_time'];
          $service->save();
          note_log()->withService($service->id)->withComment()->add("Update appt date of service to  " .$input['date'] );
        }
        activity_log()->withObject($item)->add('Update Appointment', $input);
        if ($item->event_id) {
          $appt = Appointment::find($id);
          CalendarService::updateEventToCalendar($appt);
        }
        return $this->sendResponse(null, 'Appointment updated successfully.');
    }

    /**
    * @OA\Put(
    *      path="/admin/appointment/assign/{id}",
    *      operationId="AppointmentController::assign",
    *      tags={"Admin.Appointment"},
    *      summary="",
    *      description="TODO AppointmentController::assign ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.AppointmentAssignRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param AppointmentAssignRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function assign($id, AppointmentAssignRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not assign other company  appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'assign appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('assign', $item);
        $userId = $request->input('user_id');
        if($userId){
          $user = Account::find($userId);
        }else{
          $user = null;
        }
        if ($user && !$user->isAppointmentAssign()) {
          return $this->sendError('Please select the correct user!');
        }

        $data = [
          'user_id' => $userId,
          'is_notify_assign'  => null,
          'is_assign_received'=> false,
          'assign_received_date' => null,
          'status' => Appointment::STATUS_SCHEDULED,
          'event_id' => null
        ];
        if ($item->event_id) {
          $res = CalendarService::delEventFromCalendar($item);
          if ($res) {
            \Log::debug('Remove the calendar event failed: ' . $res);
          }
        }
        // add activity note
        if ($item->service_id) {
          $service = $item->service;
          $service->install_id = $userId;
          $service->save();
          if($user){
            note_log()->withService($item->service_id)->withActivity(Notes::ICON_APPT)->add("Assign the appointment to " . $user->name.".");
          }else{
            note_log()->withService($item->service_id)->withActivity(Notes::ICON_APPT)->add("Remove appointment assignment staff.");
          }
        } else if ($item->leads->order_id) {
          $order = $item->leads->order;
          if ($item->type == 'cm') {
            $order->cm_id = $userId;
          } else if ($item->type == 'install') {
            $order->install_id = $userId;
          }
          $order->save();
          if($user){
            note_log()->withOrder($item->leads->id)->withActivity(Notes::ICON_APPT)->add("Assign the appointment to " . $user->name);
          }else{
            note_log()->withOrder($item->leads->id)->withActivity(Notes::ICON_APPT)->add("Remove appointment assignment staff.");
          }

        } else {
          // update the sales consultant person to leads
          $leads=$item->leads;
          $leads->sales_consultant_id = $userId;
          $leads->save();
          if($user){
            note_log()->withLeads($item->leads->id)->withActivity(Notes::ICON_APPT)->add("Assign the appointment to " . $user->name);
          }else{
            note_log()->withLeads($item->leads->id)->withActivity(Notes::ICON_APPT)->add("Remove appointment assignment staff.");
          }
        }

        $this->repository->update($data, $id);
        // add event to google calendar
        $appt = Appointment::find($id);
        if ($appt->user_id) {
          $res = CalendarService::addEventToCalendar($appt);
          if ($res) {
            return $this->sendError('assign processed successfully, but add Google calendar event failed. ' . $res);
          }
        }
        // TODO AppointmentController::assign
        return $this->sendResponse(null, 'assign processed successfully.');
    }
    /**
    * @OA\Put(
    *      path="/admin/appointment/notify_client/{id}",
    *      operationId="AppointmentController::notifyClient",
    *      tags={"Admin.Appointment"},
    *      summary="",
    *      description="TODO AppointmentController::notifyClient ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.AppointmentNotifyClientRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param AppointmentNotifyClientRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function notifyClient($id, AppointmentNotifyClientRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not notify client other company  appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'notify client for the appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('notifyClient', $item);
        if ($item->is_notify) {
          return $this->sendError('You have notified the client');
        }
        $client = $item->leads->client;
        try {
          // send sms
          $res = true;
          if (str_starts_with($client->phone, '04')) {
            $res = SMS::SendAppointmentNotfy($client->given_name . ' ' . $client->surname, $client->phone);
          } else {
            Mail::to($client->email)->send(new Email($client->given_name . ' ' . $client->surname, $item));
          }
          if ($res !== true) {
            Mail::to($client->email)->send(new Email($client->given_name . ' ' . $client->surname, $item));
          }
        } catch (\Exception $exception) {
          \Log::error('Fail to notify the client: ' . $exception->getMessage());
          return $this->sendError('Fail to notify the client');
        }
        $item->is_notify = 1;
        $item->save();
        // TODO AppointmentController::notifyClient
        return $this->sendResponse(null, 'Notify Client processed successfully.');
    }
    /**
    * @OA\Put(
    *      path="/admin/appointment/cancel/{id}",
    *      operationId="AppointmentController::cancel",
    *      tags={"Admin.Appointment"},
    *      summary="",
    *      description="TODO AppointmentController::cancel ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.AppointmentCancelRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param AppointmentCancelRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function cancel($id, AppointmentCancelRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        $input = $request->input();
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not cancel other company  appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'cancel the appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        if ($item->status == Appointment::STATUS_CANCEL) {
          return $this->sendError('The appointment has beend cancelled!');
        }
        if ($item->type == 'sale' && !isset($input['followup_time'])) {
          return $this->sendError('Please enter the followup time!');
        }
        $leads = $item->leads;
        if ($item->type == 'sale') {
          $lassAppt = Appointment::where('status',Appointment::STATUS_COMPLETED)
          ->where('object_id',$leads->id)
          ->where('type','sale')
          ->orderBy('id','desc')
          ->first();
          $leads->result = 'followup';
          $leads->followup_time = $input['followup_time'];
          $leads->appt_date = $lassAppt?$lassAppt->date:null;
          $leads->save();
          note_log()->withLeads($leads->id)->withResult('followup')->add("Cancelled the appointment and change the result to followup", $input['note']);
          // update klaviyo profile
          KlaviyoService::UpdateOrCreateProfile($leads->client);
          FacebookService::createEvent($leads, 'In progress');
        }else if($item->type == 'service'){
          $service = $leads->service;
          if(empty($service)){
            return $this->sendError('Service Order not fond!');
          }
          $service->result = Service::RESULT_READY;
          $service->install_id = null;
          $service->installation_date = null;
          $service->ready_date = $input["ready_date"];
          $service->save();
          note_log()->withService($service->id)->withResult($service->result)->add("Cancelled the appointment" . ($service->result ? " and change the result to '" . $service->result. "'" : ""), $input['note']);

        } else {
          $order = $leads->order;
          $result = null;
          if ($order->result == Order::RESULT_INSTALLATION) {
            $result = Order::RESULT_READY;
            $order->last_result =  $order->result;
            $order->result =  $result;
            $order->install_id = null;
            $order->installation_date = null;
          } else if ($order->result == Order::RESULT_CM) {
            $result = Order::RESULT_NEW;
            $order->last_result =  $order->result;
            $order->result =  $result;
            $order->cm_id = null;
            $order->cm_booked_date = null;
          }
          $order->save();
          note_log()->withOrder($leads->order_id)->withResult($result)->add("Cancelled the appointment" . ($result ? " and change the result to '" . $result. "'" : ""), $input['note']);
        }
        $this->authorize('cancel', $item);
        $this->repository->update(['status' => Appointment::STATUS_CANCEL],$id);
         if ($item->event_id) {
          $res = CalendarService::delEventFromCalendar($item);
          if ($res) {
           return $this->sendError('Cancel processed successfully but cancel calendar event failed. ' . $res);
          }
        }
        return $this->sendResponse(null, 'cancel processed successfully.');
    }
    /**
    * @OA\Put(
    *      path="/admin/appointment/complete/{id}",
    *      operationId="AppointmentController::complete",
    *      tags={"Admin.Appointment"},
    *      summary="",
    *      description="TODO AppointmentController::complete ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.AppointmentCompleteRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param AppointmentCompleteRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function complete($id, AppointmentCompleteRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not complete other company  appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'complete the appointment');
  
        if ($errorMessage) {
          return $this->sendError($errorMessage, 500);
        }
        if (!$request->user()->isAppointmentUser()) {
          $this->authorize('complete', $item);
        }
        $input = $request->input();
        $leads = $item->leads;
        $note = isset($input['note']) ? $input['note'] : '';
        unset($input['note']);
        if ($item->type == 'sale') {
          if($input['result'] == 'cancelled'){
            Appointment::where('object_id', $item->object_id)->whereNotIn('status', [Appointment::STATUS_COMPLETED, Appointment::STATUS_CANCEL])->update(['status' => Appointment::STATUS_CANCEL]);
            Leads::where('id', $item->object_id)->update($input);
            note_log()->withLeads($item->object_id)->withResult($input['result'])->add('Cancel the appointment and change the result to '.$input['result'],  $note);
            // update klaviyo profile
            KlaviyoService::UpdateOrCreateProfile($leads->client);
            FacebookService::createEvent($leads, 'In progress');
            return $this->sendResponse(null, 'Cancel processed successfully.');
          }else{
            if (!isset($input['quoted']) || !$input['quoted']) {
              return $this->sendError('Please input the quoted amount!');
            }
            if (!isset($input['retail']) || !$input['retail']) {
              return $this->sendError('Please input the retail amount!');
            }
            if($input['result'] == Leads::RESULT_SOLD){
              if (!isset($input['sold']) || !$input['sold']) {
                return $this->sendError('Please input the sold amount!');
              }
              if(empty($leads->sold_date)){
                $input['sold_date'] = date("Y-m-d H:i:s");
              }
              // store the product to existing product
              $subCategoryIds = explode(',', $leads->sub_category_ids);
              foreach ($subCategoryIds as $subCategoryId) {
                ClientsExistingProduct::updateOrCreate(
                  ['client_id' => $leads->client_id, 'product_id' => $subCategoryId],
                  ['client_id' => $leads->client_id, 'product_id' => $subCategoryId]
                );
              }
            }
            if (!isset($input['sub_result'])) {
              $input['sub_result'] = null;
            }
          }
          Leads::where('id', $item->object_id)->update($input);
          note_log()->withLeads($item->object_id)->withResult($input['result'])->add('Completed the appointment and change the result to '.$input['result'],  $note);
          // update klaviyo profile
          $eventName = '';
          if ($input['result'] == 'sold') {
            $eventName = 'Complete';
          } else if ($input['result'] == 'quoted') {
            $eventName = 'Converted';
          } else if ($input['result'] == 'cancelled') {
            $eventName = 'Not qualified';
          }
          KlaviyoService::UpdateOrCreateProfile($leads->client);
          FacebookService::createEvent($leads, $eventName);
        }else if ($item->type == 'cm') {
            $order = Order::where('lead_id', $item->object_id)->first();
            $order->last_result = $order->result;
            $order->result = Order::RESULT_CM_RECEIVED;
            $order->cm_received_date = date("Y-m-d H:i:s");
            $order->save();
            note_log()->withOrder($leads->order->id)->withResult(Order::RESULT_CM_RECEIVED)->add('Change the result to ' . Order::RESULT_CM_RECEIVED,  $note);
        }else if ($item->type == 'install') {
          if (!isset($input['result']) || !$input['result']) {
            return $this->sendError('Please select the result!');
          }
          if ($input['result'] == Order::RESULT_READY && ( !isset($input['followup_date']) || !$input['followup_date'])) {
            return $this->sendError('Please input the followup date!');
          }
          $data = [
            'result' => $input['result'],
            'completion_date' => $input['result'] == Order::RESULT_COMPLETION ? date("Y-m-d H:i:s") : null,
            'reason' => $input['result'] == Order::RESULT_READY ? $note : '',
            'installed_date' => $input['result'] == Order::RESULT_OUTSTANDING ? date("Y-m-d H:i:s") : null,
            'followup_date' => $input['result'] == Order::RESULT_READY ? $input['followup_date'] : null,
            'product_staff_id' => $request->user()->id
          ];
          Order::where('lead_id', $item->object_id)->update($data);
          note_log()->withOrder($leads->order->id)->withResult($input['result'])->add('Change the result to ' . $input['result'], $note);

        }else if ($item->type == 'service'){
          if ($item->service_id){
            $service = $item->service;
            $service->result = $input['result'];
            $service->completion_date = $input['result'] == Service::RESULT_COMPLETION ? date("Y-m-d H:i:s") : null;
            if($input['result'] == Service::RESULT_ON_PRODUCTION){
              $service->on_product_date = $input["on_product_date"]?$input["on_product_date"]: date("Y-m-d");
            }
            $service->save();
            note_log()->withService($item->service_id)->withResult($input['result'])->add('Change the result to ' . $input['result'], $note);
          }else{
            return $this->sendError('Service order not found');
          }
        }else{
          return $this->sendError('Error appointment type');
        }
        // TODO AppointmentController::complete
        $this->repository->update(['status'=>Appointment::STATUS_COMPLETED], $id);
        return $this->sendResponse(null, 'complete processed successfully.');
    }
    /**
    * @OA\Post(
    *      path="/admin/appointment/sendToGoogleCalendar",
    *      operationId="AppointmentController::sendToGoogleCalendar",
    *      tags={"Admin.Appointment"},
    *      summary="",
    *      description="TODO AppointmentController::sendToGoogleCalendar ",
    *      security={{"api_http_auth": {}}},
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.AppointmentSendToGoogleCalendarRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param AppointmentSendToGoogleCalendarRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */

    public function sendToGoogleCalendar(AppointmentSendToGoogleCalendarRequest $request){
      $appointment_id = $request->input('appointment_id');
      if(empty($appointment_id)){
        return $this->sendError('Appointment not found');
      }
      $item = $this->repository->findWithoutFail($appointment_id);
      if (empty($item)) {
        return $this->sendError('Appointment not found');
      }
      if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
        return $this->sendError('You can not send to google calendar other company  appointmen');
      }
      $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'send to google calendar for the appointment');
      if ($errorMessage) {
        return $this->sendError($errorMessage);
      }
      $eventDetails=[
        "title"=>"Just Enquality CRM:".ucwords($item->type." appointment"),
        "location"=>$item->address,
        "description"=>"Just Enquality CRM appointment.(".$item->object_id.")",
        "start"=> $item->date,
        "end"=> Carbon::parse($item->date)->addHours(1),
      ];
      try{
        $calendar=new GoogleCalendarService();
        $res= $calendar->addEvent($eventDetails);
        if ($res) {
          return $this->sendResponse(null, 'Event created in user\'s calendar successfully!');
        } else {
          return $this->sendError('Failed to create event.',500);
        }
      }catch(Exception $e){
        \Log::error('Error adding event: ' . $e->getMessage());
        return $this->sendResponse(null, 'Failed to create event!',500);
      }
    }
    /**
    * @OA\Put(
    *      path="/admin/appointment/notifyAssign/{id}",
    *      operationId="AppointmentController::notifyAssign",
    *      tags={"Admin.Appointment"},
    *      summary="",
    *      description="TODO AppointmentController::notifyAssign ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\RequestBody(ref="#/components/requestBodies/Admin.AppointmentNotifyAssignRequestBody"),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param AppointmentNotifyAssignRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function notifyAssign($id, AppointmentNotifyAssignRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not notify assign for other company  appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'notify assign for the appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('notifyAssign', $item);
        if (!$item->user_id) {
          return $this->sendError('Please assign a user to the appointment.');
        }
        // TODO AppointmentController::notifyAssign
        $user = Account::find($item->user_id);
        $res = $item->sentNotifyToAssign($user);
        if ( $res ) {
          $this->repository->update([
            'is_notify_assign'=> true,
            'notify_assign_date' => date("Y-m-d H:i:s")
          ], $id);
          return $this->sendResponse(null, 'Notify assignee processed successfully.');
        } else {
          return $this->sendError('Notification of assignee processing failed.');
        }
    }
    /**
    * @OA\Put(
    *      path="/admin/appointment/assignAccept/{id}",
    *      operationId="AppointmentController::assignAccept",
    *      tags={"Admin.Appointment"},
    *      summary="",
    *      description="TODO AppointmentController::assignAccept ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param AppointmentAssignAcceptRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function assignAccept($id, AppointmentAssignAcceptRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not assign accept other company  appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'assign accept the appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('assignAccept', $item);
        $this->repository->update([
          'is_assign_received'=> true,
          'assign_received_date' => date("Y-m-d H:i:s"),
          // 'status' => Appointment::STATUS_RECEIVED
        ], $id);
        return $this->sendResponse(null, 'Assignee Accept processed successfully
.');
    }
    /**
    * @OA\Put(
    *      path="/admin/appointment/confirm/{id}",
    *      operationId="AppointmentController::confirm",
    *      tags={"Admin.Appointment"},
    *      summary="",
    *      description="TODO AppointmentController::confirm ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param AppointmentConfirmRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function confirm($id, AppointmentConfirmRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not confirm other company  appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'confirm the appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('confirm', $item);
        $data = [
          'confirm_date' => date("Y-m-d H:i:s")
        ];
        $data['status'] = Appointment::STATUS_CONFIRMED;
        $this->repository->update($data, $id);
        return $this->sendResponse(null, 'confirm processed successfully.');
    }
    /**
    * @OA\Post(
    *      path="/admin/Admin/Appointment/RevokeCancel/{id}",
    *      operationId="AppointmentController::revokeCancel",
    *      tags={"Admin.Appointment"},
    *      summary="",
    *      description="TODO AppointmentController::revokeCancel ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param AppointmentRevokeCancelRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function revokeCancel($id, AppointmentRevokeCancelRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not revoke other company  appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'revoke the appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('revokeCancel', $item);
        $item->status = Appointment::STATUS_OVERDUE;
        if ($item->leads) {
          $item->leads->result = Leads::RESULT_APPOINTED;
          $item->leads->save();
        }
        $item->save();
        // TODO AppointmentController::revokeCancel
        return $this->sendResponse(null, 'revokeCancel processed successfully.');
    }
    /**
    * @OA\Get(
    *      path="/admin/Admin/Appointment/syncCalendar/{id}",
    *      operationId="AppointmentController::syncCalendar",
    *      tags={"Admin.Appointment"},
    *      summary="",
    *      description="TODO AppointmentController::syncCalendar ",
    *      security={{"api_http_auth": {}}},
    *      @OA\Parameter(name="id", description="id", required=true, in="path",
    *          @OA\Schema(type="integer")
    *      ),
    *      @OA\Response(
    *          response=200,
    *          description="successful operation"
    *      ),
    *      @OA\Response(response=400, description="Bad request")
    *  )
    *
    * @param int $id
    * @param AppointmentSyncCalendarRequest $request
    * @return JsonResponse
    * @throws \Illuminate\Auth\Access\AuthorizationException
    */
    public function syncCalendar($id, AppointmentSyncCalendarRequest $request)
    {
        /**
         * @var $item Appointment
         */
        $item = $this->repository->findWithoutFail($id);
        if (empty($item)) {
            return $this->sendError('Appointment not found');
        }
        if (!$request->user()->isPlatformUser() && $item->company_region != $request->user()->company_region) {
          return $this->sendError('You can not sync the appointmen');
        }
        $errorMessage = $this->checkPlatformCompany($request, $item->company_region, 'sync the appointment');
        if ($errorMessage) {
          return $this->sendError($errorMessage);
        }
        $this->authorize('syncCalendar', $item);
        if ($item->event_id) {
          return $this->sendResponse(null, 'syncCalendar processed successfully.');
        }
        $res = CalendarService::addEventToCalendar($item);
        if ($res) {
          return $this->sendError($res);
        }
        // TODO AppointmentController::syncCalendar
        return $this->sendResponse(null, 'syncCalendar processed successfully.');
    }
}