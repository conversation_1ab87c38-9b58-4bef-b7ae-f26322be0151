<?php

namespace Modules\Admin\Policies;

use Modules\Admin\Models\Order;
use Modules\Admin\Models\Account;
use Illuminate\Auth\Access\HandlesAuthorization;
class OrderPolicy
{
    use HandlesAuthorization;
    public function before(?Account $loginAccount, $ability)
    {
        if ($loginAccount and $loginAccount->isSuperAdmin()) {
            return true;
        }
    }
    /**
     * Determine whether the user can view any Order instances.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function viewAny(Account $loginAccount)
    {
        //TODO OrderPolicy::viewAny
        if($loginAccount->hasPermissionTo('viewAnyOrders')) {
          return true;
        }
    }
    /**
     * Determine whether the user can view the Order instance.
     *
     * @param Account $loginAccount
     * @param Order $instance
     * @return mixed
     */
    public function view(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::view
        return true;
    }
    /**
     * Determine whether the user can create Order instance.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function create(Account $loginAccount)
    {
        //TODO OrderPolicy::create
        return false;
    }
    /**
     * Determine whether the user can update the Order instance.
     *
     * @param Account $loginAccount
     * @param Order $instance
     * @return mixed
     */
    public function update(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::update
        return $loginAccount->hasPermissionTo('viewAnyOrders');
    }
    /**
     * Determine whether the user can delete the Order instance.
     *
     * @param Account $loginAccount
     * @param Order $instance
     * @return mixed
     */
    public function delete(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::delete
        if($loginAccount->hasPermissionTo('cancelOrder')) {
          return true;
        }
    }
    /**
     * Determine whether the user can restore the Order instance.
     *
     * @param Account $loginAccount
     * @param Order $instance
     * @return mixed
     */
    public function restore(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::restore
        return true;
    }
    /**
     * Determine whether the user can permanently delete the Order instance.
     *
     * @param Account $loginAccount
     * @param Order $instance
     * @return mixed
     */
    public function forceDelete(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::forceDelete
        return true;
    }
    /**
     * Determine whether the user can do addPayment.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function addPayment(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::addPayment
        if($loginAccount->hasPermissionTo('updatePayment') || $loginAccount->hasPermissionTo('updateServicePayment')) {
          return true;
        }
    }

    /**
     * Determine whether the user can do delPayment.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function delPayment(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::delPayment
        if($loginAccount->hasPermissionTo('updatePayment') || $loginAccount->hasPermissionTo('updateServicePayment')) {
          return true;
        }
    }
    /**
     * Determine whether the user can do changeResult.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function changeResult(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::changeResult
        if($loginAccount->hasPermissionTo('changeOrderResult')) {
          return true;
        }
    }

    public function cancelOrder(Account $loginAccount, Order $instance)
    {
        if($loginAccount->hasPermissionTo('changeOrderResult') || $loginAccount->hasPermissionTo('cancelOrder')) {
          return true;
        }
    }
    /**
     * Determine whether the user can do saveSpec.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function saveSpec(Account $loginAccount)
    {
        //TODO OrderPolicy::saveSpec
        if($loginAccount->hasPermissionTo('orderSpec')) {
          return true;
        }
    }
    /**
     * Determine whether the user can do addSupplier.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function addOrderSupplier(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::addSupplier
        if($loginAccount->hasPermissionTo('addSupplierForOrder')) {
          return true;
        }
    }

    /**
     * Determine whether the user can do removeSupplier.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function removeOrderSupplier(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::removeSupplier
        if($loginAccount->hasPermissionTo('removeSupplierForOrder')) {
          return true;
        }
    }
    /**
     * Determine whether the user can do addSupplier.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function addServiceSupplier(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::addSupplier
        if($loginAccount->hasPermissionTo('addSupplierForService')) {
          return true;
        }
    }

    /**
     * Determine whether the user can do removeSupplier.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function removeServiceSupplier(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::removeSupplier
        if($loginAccount->hasPermissionTo('removeSupplierForService')) {
          return true;
        }
    }
    /**
     * Determine whether the user can do createService.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function createService(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::createService
        if($loginAccount->hasPermissionTo('createService')) {
          return true;
        }
    }

    public function editOrderManagement(Account $loginAccount, Order $instance)
    {
        if($loginAccount->hasPermissionTo('editOrderManagement')) {
          return true;
        }
    }
    /**
     * Determine whether the user can do generatorPDF.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function generatorPDF(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::generatorPDF
        return true;
    }
    /**
     * Determine whether the user can do generatorSatisfactionPDF.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function generatorSatisfactionPDF(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::generatorSatisfactionPDF
        return true;
    }
    /**
     * Determine whether the user can do generatorWarrantyCardPDF.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function generatorWarrantyCardPDF(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::generatorWarrantyCardPDF
        return true;
    }
    /**
     * Determine whether the user can do generatorReport.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function generatorReport(Account $loginAccount)
    {
        //TODO OrderPolicy::generatorReport
        if($loginAccount->hasPermissionTo('viewAnyOrders')) {
          return true;
        }
    }
    /**
     * Determine whether the user can do revokeOrder.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function revokeOrder(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::revokeOrder
        if($loginAccount->hasPermissionTo('canRevokeOrder')) {
          return true;
        }
    }
    /**
     * Determine whether the user can do emailToClient.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function emailToClient(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::emailToClient
        return true;
    }
    /**
     * Determine whether the user can do sendPaymentEmail.
     *
     * @param Account $loginAccount
     * @return mixed
     */
    public function sendPaymentEmail(Account $loginAccount, Order $instance)
    {
        //TODO OrderPolicy::sendPaymentEmail
        return true;
    }
}