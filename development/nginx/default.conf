server {
    listen 80;
    root /etc/dist;
    index index.php index.html index.htm;

    location / {
        client_max_body_size 60M;
        if (!-e $request_filename){
               rewrite  ^/(.*)$  /index.php/$1  last;
               break;
          }
    }

    location ~ \.php$ {
        try_files $uri /index.php =404;
        fastcgi_pass php:9000;
        fastcgi_index index.php;
        fastcgi_split_path_info ^(.+\.php)(.*)$;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        fastcgi_intercept_errors on;
        #fixes timeouts
        fastcgi_read_timeout 600;
        include fastcgi_params;
    }

    rewrite ^/storage/(.*)$ /backend/public/storage/$1;
    rewrite ^/images/(.*)$ /backend/public/images/$1;
    rewrite ^/broadcasting/(.*)$ /backend/public/index.php;
    rewrite ^/api/(.*)$ /backend/public/index.php;
    rewrite ^/docs/(.*)$ /backend/public/index.php;

    if ( $request_uri !~ ^/(storage|api|docs|broadcasting|thirdparty|images) ){
            rewrite ^(.*)$ /admin/$1;
    }

    if ( $uri !~ /storage/|/thirdparty|\.|/images/){
            rewrite ^(.*)$ /admin/index.html;
    }
}
