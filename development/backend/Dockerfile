#FROM ingress.osvlabs.com:32116/infrastructures/base-images/base-images-php8.2.13-fpm-supervisord
FROM harbor.ks.osvlabs.com:31310/infrastructures/base-images/base-images-php:php-8-2-13-fpm-8107

ENV TZ=Asia/Shanghai

ARG DOCKER_UID=1000
ENV DOCKER_UID ${DOCKER_UID}
ARG DOCKER_GID=1000
ENV DOCKER_GID ${DOCKER_GID}

# user and group and permissions
RUN groupadd -g ${DOCKER_GID} docker && \
  useradd -u ${DOCKER_UID} -g docker docker && \
  usermod -d /home/<USER>
RUN mkdir /home/<USER>
  chown docker:docker /home/<USER>

RUN chown docker:docker ./ -R

# supervisord
RUN mkdir -p /var/log/supervisor && \
  chown docker:docker /var/log/supervisor && \
  chmod 777 /run

#RUN mkdir -p /etc/dist/backend && \
#    chown docker:docker /etc/dist/backend

# Development php.ini setting
ARG PHAR_READONLY=On
RUN cp /usr/local/etc/php/php.ini-development /usr/local/etc/php/php.ini
RUN sed -i "s/upload_max_filesize = 2M/upload_max_filesize = 55M/" /usr/local/etc/php/php.ini && \
  sed -i "s/post_max_size = 8M/post_max_size = 60M/" /usr/local/etc/php/php.ini && \
  sed -i "s/;opcache.enable=1/opcache.enable=1/" /usr/local/etc/php/php.ini && \
  sed -i "s/;opcache.enable_cli=0/opcache.enable_cli=1/" /usr/local/etc/php/php.ini && \
  sed -i "s/;phar.readonly = On/phar.readonly = ${PHAR_READONLY}/" /usr/local/etc/php/php.ini && \
  sed -i "s/;error_log = php_errors.log/error_log = \/var\/log\/php\/php_errors.log/" /usr/local/etc/php/php.ini

# install composer
COPY --from=composer:2.7.1 /usr/bin/composer /usr/local/bin/composer
# RUN composer config -g repo.packagist composer https://mirrors.cloud.tencent.com/composer/
# RUN composer self-update --2

# 安装wkhtmltopdf依赖
RUN apt-get update && apt-get install -y wkhtmltopdf


COPY ./development/backend/entrypoint.sh /etc/entrypoint/entrypoint.sh
RUN chmod 777 /etc/entrypoint/entrypoint.sh
ENTRYPOINT [ "/etc/entrypoint/entrypoint.sh" ]

USER docker

WORKDIR /etc/dist/backend
EXPOSE 9000

