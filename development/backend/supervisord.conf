[supervisord]
nodaemon=false

[program:queue]
directory=/etc/dist/backend
command=php artisan queue:work --tries=3
process_name=%(program_name)s_%(process_num)02d
numprocs=1
autostart=true
autorestart=true
user=docker

[program:supercronic]
directory=/etc/dist/backend
command=supercronic /etc/supercronictab
process_name=%(program_name)s_%(process_num)02d
numprocs=1
autostart=true
autorestart=true
user=docker

[group:default]
programs=queue,supercronic
priority=999
